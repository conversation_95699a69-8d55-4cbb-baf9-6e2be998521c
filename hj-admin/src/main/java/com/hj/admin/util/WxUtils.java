package com.hj.admin.util;

import cn.hutool.http.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class WxUtils {

    private static final Logger logger = LoggerFactory.getLogger(WxUtils.class);

    public void sendWxWorkMsg(String apiUrl, String msg) {
        try {
            logger.info("发送企业微信消息到：{}", apiUrl);
            String result = HttpUtil.createPost(apiUrl).contentType("application/json")
                    .body(msg)
                    .execute()
                    .body();
            logger.info("发送企业微信消息返回结果：{}", result);
        } catch (Exception e) {
            logger.error("发送企业微信消息失败", e);
            throw e;
        }
    }
}
