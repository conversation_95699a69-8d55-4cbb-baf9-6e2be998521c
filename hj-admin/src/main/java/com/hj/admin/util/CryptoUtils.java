package com.hj.admin.util;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;
import java.util.UUID;

/**
 * 一个用于加密和生成随机字符串的工具类。
 */
public class CryptoUtils {

    private static final String ALPHANUMERIC = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final Random RANDOM = new SecureRandom();

    /**
     * 对输入字符串进行SHA-512 Hex加密。
     *
     * @param input 要加密的字符串。
     * @return 加密后的SHA-512 Hex字符串。
     */
    public static String generateSha512Hex(String input) {
        if (input == null) {
            return null;
        }
        try {
            // 获取SHA-512算法的MessageDigest实例 [6, 8]
            MessageDigest md = MessageDigest.getInstance("SHA-512");

            // 计算消息摘要
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制表示 [6]
            BigInteger no = new BigInteger(1, messageDigest);
            StringBuilder hashText = new StringBuilder(no.toString(16));

            // 补零，以确保结果是128位的十六进制字符串
            while (hashText.length() < 128) {
                hashText.insert(0, "0");
            }

            return hashText.toString();
        } catch (NoSuchAlgorithmException e) {
            // 在标准Java环境中，SHA-512算法总是可用的
            throw new RuntimeException("SHA-512 algorithm not found", e);
        }
    }

    /**
     * 生成一个指定长度的唯一随机字符串
     * <p>
     * 为了保证唯一性，此方法结合了UUID和安全随机数生成器。 [1, 5]
     *
     * @param length 期望的字符串长度
     * @return 生成的唯一随机字符串
     * @throws IllegalArgumentException 如果长度超出10-50的范围
     */
    public static String generateRandomString(int length) {
        // 使用UUID确保基本唯一性 [1, 2]
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 如果所需长度小于等于32，则截取UUID的一部分
        if (length <= 32) {
            return uuid.substring(0, length);
        }

        // 如果所需长度大于32，则在UUID基础上补充随机字符
        StringBuilder sb = new StringBuilder(uuid);
        for (int i = 0; i < length - 32; i++) {
            sb.append(ALPHANUMERIC.charAt(RANDOM.nextInt(ALPHANUMERIC.length())));
        }

        return sb.toString();
    }

}

