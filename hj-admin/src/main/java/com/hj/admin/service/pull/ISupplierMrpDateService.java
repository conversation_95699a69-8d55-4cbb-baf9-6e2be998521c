package com.hj.admin.service.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierMrpDate;

/**
 * 日物料需求计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierMrpDateService 
{
    /**
     * 查询日物料需求计划
     * 
     * @param id 日物料需求计划主键
     * @return 日物料需求计划
     */
    public SupplierMrpDate selectSupplierMrpDateById(Long id);

    /**
     * 查询日物料需求计划列表
     * 
     * @param supplierMrpDate 日物料需求计划
     * @return 日物料需求计划集合
     */
    public List<SupplierMrpDate> selectSupplierMrpDateList(SupplierMrpDate supplierMrpDate);

    /**
     * 新增日物料需求计划
     * 
     * @param supplierMrpDate 日物料需求计划
     * @return 结果
     */
    public int insertSupplierMrpDate(SupplierMrpDate supplierMrpDate);

    /**
     * 修改日物料需求计划
     * 
     * @param supplierMrpDate 日物料需求计划
     * @return 结果
     */
    public int updateSupplierMrpDate(SupplierMrpDate supplierMrpDate);

    /**
     * 批量删除日物料需求计划
     * 
     * @param ids 需要删除的日物料需求计划主键集合
     * @return 结果
     */
    public int deleteSupplierMrpDateByIds(String ids);

    /**
     * 删除日物料需求计划信息
     *
     * @param id 日物料需求计划主键
     * @return 结果
     */
    public int deleteSupplierMrpDateById(Long id);

    /**
     * 批量插入或更新日物料需求计划
     * 根据id判断是插入还是更新
     *
     * @param supplierMrpDateList 日物料需求计划列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<SupplierMrpDate> supplierMrpDateList);
}
