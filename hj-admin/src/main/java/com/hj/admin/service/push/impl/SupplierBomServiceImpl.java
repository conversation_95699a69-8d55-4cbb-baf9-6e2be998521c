package com.hj.admin.service.push.impl;

import com.hj.admin.domain.push.SupplierBom;
import com.hj.admin.mapper.push.SupplierBomMapper;
import com.hj.admin.service.push.ISupplierBomService;
import com.hj.common.core.text.Convert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * BOM主数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierBomServiceImpl implements ISupplierBomService 
{
    @Resource
    private SupplierBomMapper supplierBomMapper;

    /**
     * 查询BOM主数据
     * 
     * @param id BOM主数据主键
     * @return BOM主数据
     */
    @Override
    public SupplierBom selectSupplierBomById(Long id)
    {
        return supplierBomMapper.selectSupplierBomById(id);
    }

    /**
     * 查询BOM主数据列表
     * 
     * @param supplierBom BOM主数据
     * @return BOM主数据
     */
    @Override
    public List<SupplierBom> selectSupplierBomList(SupplierBom supplierBom)
    {
        return supplierBomMapper.selectSupplierBomList(supplierBom);
    }

    /**
     * 新增BOM主数据
     *
     * @param supplierBom BOM主数据
     * @return 结果
     */
    @Override
    public int insertSupplierBom(SupplierBom supplierBom)
    {
        return supplierBomMapper.insertSupplierBom(supplierBom);
    }

    /**
     * 修改BOM主数据
     * 
     * @param supplierBom BOM主数据
     * @return 结果
     */
    @Override
    public int updateSupplierBom(SupplierBom supplierBom)
    {
        return supplierBomMapper.updateSupplierBom(supplierBom);
    }

    /**
     * 批量删除BOM主数据
     *
     * @param ids 需要删除的BOM主数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierBomByIds(String ids)
    {
        return supplierBomMapper.deleteSupplierBomByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除BOM主数据信息
     *
     * @param id BOM主数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierBomById(Long id)
    {
        return supplierBomMapper.deleteSupplierBomById(id);
    }

    @Override
    public int batchUpdate(List<SupplierBom> list) {
        return supplierBomMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierBom> selectSupplierBomByIds(String batchNo) {
        return supplierBomMapper.selectSupplierBomByIds(batchNo);
    }
}
