package com.hj.admin.service.push.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierProEnvironmentMapper;
import com.hj.admin.domain.push.SupplierProEnvironment;
import com.hj.admin.service.push.ISupplierProEnvironmentService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 环境业务数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProEnvironmentServiceImpl implements ISupplierProEnvironmentService 
{
    @Resource
    private SupplierProEnvironmentMapper supplierProEnvironmentMapper;

    /**
     * 查询环境业务数据
     * 
     * @param id 环境业务数据主键
     * @return 环境业务数据
     */
    @Override
    public SupplierProEnvironment selectSupplierProEnvironmentById(Long id)
    {
        return supplierProEnvironmentMapper.selectSupplierProEnvironmentById(id);
    }

    /**
     * 查询环境业务数据列表
     * 
     * @param supplierProEnvironment 环境业务数据
     * @return 环境业务数据
     */
    @Override
    public List<SupplierProEnvironment> selectSupplierProEnvironmentList(SupplierProEnvironment supplierProEnvironment)
    {
        return supplierProEnvironmentMapper.selectSupplierProEnvironmentList(supplierProEnvironment);
    }

    /**
     * 新增环境业务数据
     * 
     * @param supplierProEnvironment 环境业务数据
     * @return 结果
     */
    @Override
    public int insertSupplierProEnvironment(SupplierProEnvironment supplierProEnvironment)
    {
        return supplierProEnvironmentMapper.insertSupplierProEnvironment(supplierProEnvironment);
    }

    /**
     * 修改环境业务数据
     * 
     * @param supplierProEnvironment 环境业务数据
     * @return 结果
     */
    @Override
    public int updateSupplierProEnvironment(SupplierProEnvironment supplierProEnvironment)
    {
        return supplierProEnvironmentMapper.updateSupplierProEnvironment(supplierProEnvironment);
    }

    /**
     * 批量删除环境业务数据
     * 
     * @param ids 需要删除的环境业务数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProEnvironmentByIds(String ids)
    {
        return supplierProEnvironmentMapper.deleteSupplierProEnvironmentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除环境业务数据信息
     * 
     * @param id 环境业务数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProEnvironmentById(Long id)
    {
        return supplierProEnvironmentMapper.deleteSupplierProEnvironmentById(id);
    }

    @Override
    public int batchUpdate(List<SupplierProEnvironment> list) {
        return supplierProEnvironmentMapper.batchUpdate(list);
    }

    /**
     * 根据ID批量查询环境业务数据
     *
     * @param ids
     * @return
     */
    @Override
    public List<SupplierProEnvironment> selectSupplierProEnvironmentByIds(String batchNo) {
        return supplierProEnvironmentMapper.selectSupplierProEnvironmentByIds(batchNo);
    }
}
