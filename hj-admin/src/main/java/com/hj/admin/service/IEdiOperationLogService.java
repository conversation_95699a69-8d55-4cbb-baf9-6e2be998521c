package com.hj.admin.service;

import com.hj.admin.domain.log.EdiOperationLog;
import java.util.List;

/**
 * EDI操作记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public interface IEdiOperationLogService {
    /**
     * 查询EDI操作记录
     * 
     * @param id EDI操作记录主键
     * @return EDI操作记录
     */
    public EdiOperationLog selectEdiOperationLogById(Long id);

    /**
     * 查询EDI操作记录列表
     * 
     * @param ediOperationLog EDI操作记录
     * @return EDI操作记录集合
     */
    public List<EdiOperationLog> selectEdiOperationLogList(EdiOperationLog ediOperationLog);

    /**
     * 新增EDI操作记录
     * 
     * @param ediOperationLog EDI操作记录
     * @return 结果
     */
    public int insertEdiOperationLog(EdiOperationLog ediOperationLog);

    /**
     * 修改EDI操作记录
     * 
     * @param ediOperationLog EDI操作记录
     * @return 结果
     */
    public int updateEdiOperationLog(EdiOperationLog ediOperationLog);

    /**
     * 批量删除EDI操作记录
     * 
     * @param ids 需要删除的EDI操作记录主键集合
     * @return 结果
     */
    public int deleteEdiOperationLogByIds(String ids);

    /**
     * 删除EDI操作记录信息
     * 
     * @param id EDI操作记录主键
     * @return 结果
     */
    public int deleteEdiOperationLogById(Long id);

    /**
     * 根据批次号查询操作记录
     * 
     * @param batchNo 批次号
     * @return EDI操作记录
     */
    public EdiOperationLog selectEdiOperationLogByBatchNo(String batchNo);

    /**
     * 根据接口路径和操作类型查询最新记录
     * 
     * @param interfacePath 接口路径
     * @param operationType 操作类型
     * @return EDI操作记录
     */
    public EdiOperationLog selectLatestLogByPathAndType(String interfacePath, String operationType);

    /**
     * 创建操作记录（开始记录）
     * 
     * @param operationType 操作类型
     * @param interfacePath 接口路径
     * @param interfaceName 接口名称
     * @param batchNo 批次号
     * @param requestParams 请求参数
     * @param isScheduled 是否定时任务
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 操作记录ID
     */
    public Long createOperationLog(String operationType, String interfacePath, String interfaceName, 
                                   String batchNo, String requestParams, Integer isScheduled, 
                                   Long operatorId, String operatorName);

    /**
     * 更新操作记录（完成记录）
     * 
     * @param logId 记录ID
     * @param requestStatus 请求状态
     * @param pushStatus 推送状态
     * @param responseResult 响应结果
     * @param errorMessage 错误信息
     * @return 结果
     */
    public int updateOperationLogResult(Long logId, Integer requestStatus, Integer pushStatus, 
                                        String responseResult, String errorMessage);
}
