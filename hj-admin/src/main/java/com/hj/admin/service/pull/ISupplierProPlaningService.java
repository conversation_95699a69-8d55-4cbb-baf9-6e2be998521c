package com.hj.admin.service.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierProPlaning;

/**
 * 整车月度生产计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface ISupplierProPlaningService 
{
    /**
     * 查询整车月度生产计划
     * 
     * @param id 整车月度生产计划主键
     * @return 整车月度生产计划
     */
    public SupplierProPlaning selectSupplierProPlaningById(Long id);

    /**
     * 查询整车月度生产计划列表
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 整车月度生产计划集合
     */
    public List<SupplierProPlaning> selectSupplierProPlaningList(SupplierProPlaning supplierProPlaning);

    /**
     * 新增整车月度生产计划
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 结果
     */
    public int insertSupplierProPlaning(SupplierProPlaning supplierProPlaning);

    /**
     * 修改整车月度生产计划
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 结果
     */
    public int updateSupplierProPlaning(SupplierProPlaning supplierProPlaning);

    /**
     * 批量删除整车月度生产计划
     * 
     * @param ids 需要删除的整车月度生产计划主键集合
     * @return 结果
     */
    public int deleteSupplierProPlaningByIds(String ids);

    /**
     * 删除整车月度生产计划信息
     *
     * @param id 整车月度生产计划主键
     * @return 结果
     */
    public int deleteSupplierProPlaningById(Long id);

    /**
     * 批量插入或更新整车月度生产计划
     * 根据id判断是插入还是更新
     *
     * @param supplierProPlaningList 整车月度生产计划列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<SupplierProPlaning> supplierProPlaningList);
}
