package com.hj.admin.service.pull.impl;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.pull.SupplierReturnMapper;
import com.hj.admin.domain.pull.SupplierReturn;
import com.hj.admin.service.pull.ISupplierReturnService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 退货单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierReturnServiceImpl implements ISupplierReturnService 
{
    @Resource
    private SupplierReturnMapper supplierReturnMapper;

    /**
     * 查询退货单
     * 
     * @param id 退货单主键
     * @return 退货单
     */
    @Override
    public SupplierReturn selectSupplierReturnById(Long id)
    {
        return supplierReturnMapper.selectSupplierReturnById(id);
    }

    /**
     * 查询退货单列表
     * 
     * @param supplierReturn 退货单
     * @return 退货单
     */
    @Override
    public List<SupplierReturn> selectSupplierReturnList(SupplierReturn supplierReturn)
    {
        return supplierReturnMapper.selectSupplierReturnList(supplierReturn);
    }

    /**
     * 新增退货单
     * 
     * @param supplierReturn 退货单
     * @return 结果
     */
    @Override
    public int insertSupplierReturn(SupplierReturn supplierReturn)
    {
        supplierReturn.setInsertTime(DateUtils.getNowDate());
        return supplierReturnMapper.insertSupplierReturn(supplierReturn);
    }

    /**
     * 修改退货单
     * 
     * @param supplierReturn 退货单
     * @return 结果
     */
    @Override
    public int updateSupplierReturn(SupplierReturn supplierReturn)
    {
        supplierReturn.setModifyTime(DateUtils.getNowDate());
        return supplierReturnMapper.updateSupplierReturn(supplierReturn);
    }

    /**
     * 批量删除退货单
     * 
     * @param ids 需要删除的退货单主键
     * @return 结果
     */
    @Override
    public int deleteSupplierReturnByIds(String ids)
    {
        return supplierReturnMapper.deleteSupplierReturnByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除退货单信息
     * 
     * @param id 退货单主键
     * @return 结果
     */
    @Override
    public int deleteSupplierReturnById(Long id)
    {
        return supplierReturnMapper.deleteSupplierReturnById(id);
    }

    /**
     * 批量插入或更新退货单
     * 根据id判断是插入还是更新
     *
     * @param supplierReturnList 退货单列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierReturn> supplierReturnList) {
        if (supplierReturnList == null || supplierReturnList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierReturn item : supplierReturnList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierReturnMapper.batchInsertOrUpdate(supplierReturnList);
    }
}
