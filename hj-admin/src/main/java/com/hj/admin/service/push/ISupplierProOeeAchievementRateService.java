package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProOeeAchievementRate;

/**
 * 设备OEE达成率Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProOeeAchievementRateService 
{
    /**
     * 查询设备OEE达成率
     * 
     * @param id 设备OEE达成率主键
     * @return 设备OEE达成率
     */
    public SupplierProOeeAchievementRate selectSupplierProOeeAchievementRateById(Long id);

    /**
     * 查询设备OEE达成率列表
     * 
     * @param supplierProOeeAchievementRate 设备OEE达成率
     * @return 设备OEE达成率集合
     */
    public List<SupplierProOeeAchievementRate> selectSupplierProOeeAchievementRateList(SupplierProOeeAchievementRate supplierProOeeAchievementRate);

    /**
     * 新增设备OEE达成率
     * 
     * @param supplierProOeeAchievementRate 设备OEE达成率
     * @return 结果
     */
    public int insertSupplierProOeeAchievementRate(SupplierProOeeAchievementRate supplierProOeeAchievementRate);

    /**
     * 修改设备OEE达成率
     * 
     * @param supplierProOeeAchievementRate 设备OEE达成率
     * @return 结果
     */
    public int updateSupplierProOeeAchievementRate(SupplierProOeeAchievementRate supplierProOeeAchievementRate);

    /**
     * 批量删除设备OEE达成率
     * 
     * @param ids 需要删除的设备OEE达成率主键集合
     * @return 结果
     */
    public int deleteSupplierProOeeAchievementRateByIds(String ids);

    /**
     * 删除设备OEE达成率信息
     * 
     * @param id 设备OEE达成率主键
     * @return 结果
     */
    public int deleteSupplierProOeeAchievementRateById(Long id);

    /**
     * 批量更新设备OEE达成率
     *
     * @param list 设备OEE达成率
     * @return 结果
     */
    public int batchUpdate(List<SupplierProOeeAchievementRate> list);

    List<SupplierProOeeAchievementRate> selectSupplierProOeeAchievementRateByIds(String batchNo);
}
