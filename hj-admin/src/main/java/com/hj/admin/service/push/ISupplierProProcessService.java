package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProProcess;

/**
 * 工艺Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProProcessService 
{
    /**
     * 查询工艺
     * 
     * @param id 工艺主键
     * @return 工艺
     */
    public SupplierProProcess selectSupplierProProcessById(Long id);

    /**
     * 查询工艺列表
     * 
     * @param supplierProProcess 工艺
     * @return 工艺集合
     */
    public List<SupplierProProcess> selectSupplierProProcessList(SupplierProProcess supplierProProcess);

    /**
     * 新增工艺
     * 
     * @param supplierProProcess 工艺
     * @return 结果
     */
    public int insertSupplierProProcess(SupplierProProcess supplierProProcess);

    /**
     * 修改工艺
     * 
     * @param supplierProProcess 工艺
     * @return 结果
     */
    public int updateSupplierProProcess(SupplierProProcess supplierProProcess);

    /**
     * 批量删除工艺
     * 
     * @param ids 需要删除的工艺主键集合
     * @return 结果
     */
    public int deleteSupplierProProcessByIds(String ids);

    /**
     * 删除工艺信息
     * 
     * @param id 工艺主键
     * @return 结果
     */
    public int deleteSupplierProProcessById(Long id);

    /**
     * 批量更新工艺
     *
     * @param list 工艺
     * @return 结果
     */
    public int batchUpdate(List<SupplierProProcess> list);

    List<SupplierProProcess> selectSupplierProProcessByIds(String batchNo);
}
