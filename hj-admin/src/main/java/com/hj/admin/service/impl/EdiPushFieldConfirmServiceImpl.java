package com.hj.admin.service.impl;

import com.hj.admin.domain.config.EdiPushFieldConfig;
import com.hj.admin.domain.EdiPushFieldConfirm;
import com.hj.admin.domain.config.EdiPushInterfaceConfig;
import com.hj.admin.mapper.config.EdiPushFieldConfigMapper;
import com.hj.admin.mapper.confirm.EdiPushFieldConfirmMapper;
import com.hj.admin.mapper.config.EdiPushInterfaceConfigMapper;
import com.hj.admin.mapper.push.PushBaseMapper;
import com.hj.admin.service.IEdiPushFieldConfirmService;
import com.hj.common.core.text.Convert;
import com.hj.common.exception.ServiceException;
import com.hj.common.utils.DateUtils;
import com.hj.common.utils.ShiroUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Push接口字段确认Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class EdiPushFieldConfirmServiceImpl implements IEdiPushFieldConfirmService {
    private static final Logger log = LoggerFactory.getLogger(EdiPushFieldConfirmServiceImpl.class);

    @Resource
    private EdiPushFieldConfirmMapper ediPushFieldConfirmMapper;
    @Resource
    private EdiPushInterfaceConfigMapper ediPushInterfaceConfigMapper;
    @Resource
    private EdiPushFieldConfigMapper ediPushFieldConfigMapper;
    @Resource
    private PushBaseMapper pushBaseMapper;

    /**
     * 查询Push接口字段确认
     *
     * @param id Push接口字段确认主键
     * @return Push接口字段确认
     */
    @Override
    public EdiPushFieldConfirm selectEdiPushFieldConfirmById(Long id) {
        return ediPushFieldConfirmMapper.selectEdiPushFieldConfirmById(id);
    }

    /**
     * 查询Push接口字段确认列表
     *
     * @param ediPushFieldConfirm Push接口字段确认
     * @return Push接口字段确认
     */
    @Override
    public List<EdiPushFieldConfirm> selectEdiPushFieldConfirmList(EdiPushFieldConfirm ediPushFieldConfirm) {
        return ediPushFieldConfirmMapper.selectEdiPushFieldConfirmList(ediPushFieldConfirm);
    }

    /**
     * 新增Push接口字段确认
     *
     * @param ediPushFieldConfirm Push接口字段确认
     * @return 结果
     */
    @Override
    public int insertEdiPushFieldConfirm(EdiPushFieldConfirm ediPushFieldConfirm) {
        ediPushFieldConfirm.setCreateTime(DateUtils.getNowDate());
        return ediPushFieldConfirmMapper.insertEdiPushFieldConfirm(ediPushFieldConfirm);
    }

    /**
     * 修改Push接口字段确认
     *
     * @param ediPushFieldConfirm Push接口字段确认
     * @return 结果
     */
    @Override
    public int updateEdiPushFieldConfirm(EdiPushFieldConfirm ediPushFieldConfirm) {
        ediPushFieldConfirm.setUpdateTime(DateUtils.getNowDate());
        return ediPushFieldConfirmMapper.updateEdiPushFieldConfirm(ediPushFieldConfirm);
    }

    /**
     * 批量删除Push接口字段确认
     *
     * @param ids 需要删除的Push接口字段确认主键
     * @return 结果
     */
    @Override
    public int deleteEdiPushFieldConfirmByIds(String ids) {
        return ediPushFieldConfirmMapper.deleteEdiPushFieldConfirmByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除Push接口字段确认信息
     *
     * @param id Push接口字段确认主键
     * @return 结果
     */
    @Override
    public int deleteEdiPushFieldConfirmById(Long id) {
        return ediPushFieldConfirmMapper.deleteEdiPushFieldConfirmById(id);
    }

    @Override
    public int confirm(String batchNo, String interfacePath) {
        EdiPushInterfaceConfig ediPushInterfaceConfig = ediPushInterfaceConfigMapper.selectOneByInterfacePath(interfacePath);
        if (null == ediPushInterfaceConfig) {
            log.error("未找到接口配置信息，无法确认，接口路径：{}", interfacePath);
            throw new ServiceException("未找到接口配置信息，无法确认");
        }
        Long userId = ShiroUtils.getUserId();
        EdiPushFieldConfirm ediPushFieldConfirm = new EdiPushFieldConfirm();
        ediPushFieldConfirm.setBatchNo(batchNo);
        ediPushFieldConfirm.setFieldMaintainerId(userId);
        ediPushFieldConfirm.setCreateTime(DateUtils.getNowDate());
        ediPushFieldConfirm.setCreateBy(ShiroUtils.getLoginName());
        ediPushFieldConfirmMapper.insertEdiPushFieldConfirm(ediPushFieldConfirm);

        //判断各个数据是否已全部确认，若已全部确认，则修改数据字段确认状态为已确认
        EdiPushFieldConfig ediPushFieldConfig = new EdiPushFieldConfig();
        ediPushFieldConfig.setInterfaceConfigId(ediPushInterfaceConfig.getId());
        List<EdiPushFieldConfig> ediPushFieldConfigs = ediPushFieldConfigMapper.selectEdiPushFieldConfigList(ediPushFieldConfig);
        int fieldMaintainerCount = ediPushFieldConfigs.size();
        int confirmCount = ediPushFieldConfirmMapper.selectEdiPushFieldConfirmList(ediPushFieldConfirm).size();
        if (confirmCount == fieldMaintainerCount) {
            pushBaseMapper.changeFieldConfirmStatus(interfacePath, batchNo);
        }
        return 1;
    }


}
