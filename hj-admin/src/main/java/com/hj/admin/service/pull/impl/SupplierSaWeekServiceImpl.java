package com.hj.admin.service.pull.impl;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.pull.SupplierSaWeekMapper;
import com.hj.admin.domain.pull.SupplierSaWeek;
import com.hj.admin.service.pull.ISupplierSaWeekService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 计划协议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierSaWeekServiceImpl implements ISupplierSaWeekService 
{
    @Resource
    private SupplierSaWeekMapper supplierSaWeekMapper;

    /**
     * 查询计划协议
     * 
     * @param id 计划协议主键
     * @return 计划协议
     */
    @Override
    public SupplierSaWeek selectSupplierSaWeekById(Long id)
    {
        return supplierSaWeekMapper.selectSupplierSaWeekById(id);
    }

    /**
     * 查询计划协议列表
     * 
     * @param supplierSaWeek 计划协议
     * @return 计划协议
     */
    @Override
    public List<SupplierSaWeek> selectSupplierSaWeekList(SupplierSaWeek supplierSaWeek)
    {
        return supplierSaWeekMapper.selectSupplierSaWeekList(supplierSaWeek);
    }

    /**
     * 新增计划协议
     * 
     * @param supplierSaWeek 计划协议
     * @return 结果
     */
    @Override
    public int insertSupplierSaWeek(SupplierSaWeek supplierSaWeek)
    {
        supplierSaWeek.setInsertTime(DateUtils.getNowDate());
        return supplierSaWeekMapper.insertSupplierSaWeek(supplierSaWeek);
    }

    /**
     * 修改计划协议
     * 
     * @param supplierSaWeek 计划协议
     * @return 结果
     */
    @Override
    public int updateSupplierSaWeek(SupplierSaWeek supplierSaWeek)
    {
        supplierSaWeek.setModifyTime(DateUtils.getNowDate());
        return supplierSaWeekMapper.updateSupplierSaWeek(supplierSaWeek);
    }

    /**
     * 批量删除计划协议
     * 
     * @param ids 需要删除的计划协议主键
     * @return 结果
     */
    @Override
    public int deleteSupplierSaWeekByIds(String ids)
    {
        return supplierSaWeekMapper.deleteSupplierSaWeekByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除计划协议信息
     * 
     * @param id 计划协议主键
     * @return 结果
     */
    @Override
    public int deleteSupplierSaWeekById(Long id)
    {
        return supplierSaWeekMapper.deleteSupplierSaWeekById(id);
    }

    /**
     * 批量插入或更新计划协议
     * 根据id判断是插入还是更新
     *
     * @param supplierSaWeekList 计划协议列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierSaWeek> supplierSaWeekList) {
        if (supplierSaWeekList == null || supplierSaWeekList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierSaWeek item : supplierSaWeekList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierSaWeekMapper.batchInsertOrUpdate(supplierSaWeekList);
    }
}
