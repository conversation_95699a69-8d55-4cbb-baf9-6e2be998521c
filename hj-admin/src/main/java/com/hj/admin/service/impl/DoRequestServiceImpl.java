package com.hj.admin.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hj.admin.domain.base.CheryBaseRes;
import com.hj.admin.domain.base.CheryPullRes;
import com.hj.admin.domain.base.CheryPushReq;
import com.hj.admin.service.IDoRequestService;
import com.hj.admin.service.IEdiOperationLogService;
import com.hj.admin.util.CryptoUtils;
import com.hj.common.exception.ServiceException;
import com.hj.common.utils.DateUtils;
import com.hj.common.utils.ShiroUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Service
public class DoRequestServiceImpl implements IDoRequestService {

    private static final Logger log = LoggerFactory.getLogger(DoRequestServiceImpl.class);

    @Value("${chery.appKey}")
    private String appKey;
    @Value("${chery.appSecret}")
    private String appSecret;
    @Value("${chery.path.prefixUrl}")
    private String prefixUrl;

    @Resource
    private IEdiOperationLogService ediOperationLogService;

    @Override
    public CheryBaseRes pull(String path, String interfaceName, Map<String, Object> map) throws Exception {
        String batchNo = "HJ_EDI_" + DateUtils.dateTimeNow("yyyyMMddHHmmss") + "_" + IdUtil.fastSimpleUUID().substring(0, 8);
        return doRequest(true, path, new ObjectMapper().writeValueAsString(map), "PULL", batchNo, interfaceName);
    }


    @Override
    public CheryBaseRes push(String path, String interfaceName, CheryPushReq cheryPushReq) throws Exception {
        return doRequest(false, path, new ObjectMapper().writeValueAsString(cheryPushReq), "PUSH", cheryPushReq.getBatchNo(), interfaceName);
    }

    private CheryBaseRes doRequest(Boolean pull, String path, String jsonBody, String operationType, String batchNo, String interfaceName) throws Exception {
        // 获取当前用户信息
        Long operatorId = null;
        String operatorName = "系统";
        try {
            if (ShiroUtils.getSysUser() != null) {
                operatorId = ShiroUtils.getUserId();
                operatorName = ShiroUtils.getSysUser().getUserName();
            }
        } catch (Exception e) {
            log.debug("获取用户信息失败，使用默认值");
        }

        //排除jsonBody中的id属性
        jsonBody = jsonBody.replaceAll("\"id\":\\s*\\d+,", "");

        // 创建操作记录
        Long logId = ediOperationLogService.createOperationLog(
                operationType, path, interfaceName, batchNo, jsonBody, 1, operatorId, operatorName);

        try {
            String timestamp = "" + new Date().getTime();
            String nonce = CryptoUtils.generateRandomString(RandomUtil.randomInt(40) + 10);
            String originalString = "method=" + Method.POST.name() + "&path=" + path + "&appKey=" + appKey
                    + "&appSecret=" + appSecret + "&timestamp=" + timestamp +
                    "&nonce=" + nonce + "&jsonBody=" + jsonBody;
            String encryptedString = CryptoUtils.generateSha512Hex(originalString);

            log.info("原始字符串: {}", originalString);
            log.info("sign: {}", encryptedString);
            log.info("timestamp: {}", timestamp);
            log.info("nonce: {}", nonce);

            HttpRequest request = HttpUtil.createPost(prefixUrl + path)
                    .contentType("application/json")
                    .header("timestamp", timestamp)
                    .header("nonce", nonce)
                    .header("appKey", appKey)
                    .header("sign", encryptedString)
                    .body(jsonBody);
            String response = request.execute().body();
            CheryBaseRes cheryBaseRes;
            if(pull){
                cheryBaseRes = JSONUtil.toBean(response, CheryPullRes.class, true);
            }else {
                cheryBaseRes = JSONUtil.toBean(response, CheryBaseRes.class, true);
            }
            if (!cheryBaseRes.getCode().equals("200")) {
                log.error("请求失败: {}", cheryBaseRes.getMessage());
                // 更新操作记录为失败
                ediOperationLogService.updateOperationLogResult(logId, 0, 3, response, cheryBaseRes.getMessage());
                throw new ServiceException(cheryBaseRes.getMessage());
            }

            log.info("响应结果: {}", response);
            // 更新操作记录为成功
            ediOperationLogService.updateOperationLogResult(logId, 1, 2, response, null);
            return cheryBaseRes;

        } catch (Exception e) {
            log.error("请求异常: ", e);
            // 更新操作记录为失败
            ediOperationLogService.updateOperationLogResult(logId, 0, 3, null, e.getMessage());
            throw e;
        }
    }


}
