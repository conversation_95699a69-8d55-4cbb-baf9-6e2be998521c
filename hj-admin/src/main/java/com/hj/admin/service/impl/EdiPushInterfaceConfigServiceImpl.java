package com.hj.admin.service.impl;

import com.hj.admin.domain.config.EdiPushInterfaceConfig;
import com.hj.admin.mapper.config.EdiPushInterfaceConfigMapper;
import com.hj.admin.service.IEdiPushInterfaceConfigService;
import com.hj.common.core.text.Convert;
import com.hj.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * Push接口配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class EdiPushInterfaceConfigServiceImpl implements IEdiPushInterfaceConfigService
{
    @Resource
    private EdiPushInterfaceConfigMapper ediPushInterfaceConfigMapper;

    /**
     * 查询Push接口配置
     * 
     * @param id Push接口配置主键
     * @return Push接口配置
     */
    @Override
    public EdiPushInterfaceConfig selectEdiPushInterfaceConfigById(Long id)
    {
        return ediPushInterfaceConfigMapper.selectEdiPushInterfaceConfigById(id);
    }

    /**
     * 查询Push接口配置列表
     * 
     * @param ediPushInterfaceConfig Push接口配置
     * @return Push接口配置
     */
    @Override
    public List<EdiPushInterfaceConfig> selectEdiPushInterfaceConfigList(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        return ediPushInterfaceConfigMapper.selectEdiPushInterfaceConfigList(ediPushInterfaceConfig);
    }

    /**
     * 新增Push接口配置
     * 
     * @param ediPushInterfaceConfig Push接口配置
     * @return 结果
     */
    @Override
    public int insertEdiPushInterfaceConfig(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        ediPushInterfaceConfig.setCreateTime(DateUtils.getNowDate());
        return ediPushInterfaceConfigMapper.insertEdiPushInterfaceConfig(ediPushInterfaceConfig);
    }

    /**
     * 修改Push接口配置
     * 
     * @param ediPushInterfaceConfig Push接口配置
     * @return 结果
     */
    @Override
    public int updateEdiPushInterfaceConfig(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        ediPushInterfaceConfig.setUpdateTime(DateUtils.getNowDate());
        return ediPushInterfaceConfigMapper.updateEdiPushInterfaceConfig(ediPushInterfaceConfig);
    }

    /**
     * 批量删除Push接口配置
     * 
     * @param ids 需要删除的Push接口配置主键
     * @return 结果
     */
    @Override
    public int deleteEdiPushInterfaceConfigByIds(String ids)
    {
        return ediPushInterfaceConfigMapper.deleteEdiPushInterfaceConfigByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除Push接口配置信息
     * 
     * @param id Push接口配置主键
     * @return 结果
     */
    @Override
    public int deleteEdiPushInterfaceConfigById(Long id)
    {
        return ediPushInterfaceConfigMapper.deleteEdiPushInterfaceConfigById(id);
    }

    @Override
    public EdiPushInterfaceConfig selectOneByInterfacePath(String path) {
        return ediPushInterfaceConfigMapper.selectOneByInterfacePath(path);
    }

    @Override
    public EdiPushInterfaceConfig selectByTableName(String tableName) {
        return ediPushInterfaceConfigMapper.selectByTableName(tableName);
    }

    @Override
    public List<String> selectInterfaceNameByIds(List<Long> ids) {
        Long[] idArray = ids.toArray(new Long[0]);
        return ediPushInterfaceConfigMapper.selectInterfaceNameByIds(idArray);
    }
}
