package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProFlaw;

/**
 * 缺陷业务数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProFlawService 
{
    /**
     * 查询缺陷业务数据
     * 
     * @param id 缺陷业务数据主键
     * @return 缺陷业务数据
     */
    public SupplierProFlaw selectSupplierProFlawById(Long id);

    /**
     * 查询缺陷业务数据列表
     * 
     * @param supplierProFlaw 缺陷业务数据
     * @return 缺陷业务数据集合
     */
    public List<SupplierProFlaw> selectSupplierProFlawList(SupplierProFlaw supplierProFlaw);

    /**
     * 新增缺陷业务数据
     * 
     * @param supplierProFlaw 缺陷业务数据
     * @return 结果
     */
    public int insertSupplierProFlaw(SupplierProFlaw supplierProFlaw);

    /**
     * 修改缺陷业务数据
     * 
     * @param supplierProFlaw 缺陷业务数据
     * @return 结果
     */
    public int updateSupplierProFlaw(SupplierProFlaw supplierProFlaw);

    /**
     * 批量删除缺陷业务数据
     * 
     * @param ids 需要删除的缺陷业务数据主键集合
     * @return 结果
     */
    public int deleteSupplierProFlawByIds(String ids);

    /**
     * 删除缺陷业务数据信息
     * 
     * @param id 缺陷业务数据主键
     * @return 结果
     */
    public int deleteSupplierProFlawById(Long id);

    /**
     * 批量更新缺陷业务数据
     *
     * @param list 缺陷业务数据
     * @return 结果
     */
    public int batchUpdate(List<SupplierProFlaw> list);

    List<SupplierProFlaw> selectSupplierProFlawByIds(String batchNo);
}
