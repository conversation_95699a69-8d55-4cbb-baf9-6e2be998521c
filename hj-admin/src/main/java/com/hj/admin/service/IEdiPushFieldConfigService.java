package com.hj.admin.service;

import com.hj.admin.domain.config.EdiPushFieldConfig;

import java.util.List;

/**
 * Push接口字段配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IEdiPushFieldConfigService 
{
    /**
     * 查询Push接口字段配置
     * 
     * @param id Push接口字段配置主键
     * @return Push接口字段配置
     */
    public EdiPushFieldConfig selectEdiPushFieldConfigById(Long id);

    /**
     * 查询Push接口字段配置列表
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return Push接口字段配置集合
     */
    public List<EdiPushFieldConfig> selectEdiPushFieldConfigList(EdiPushFieldConfig ediPushFieldConfig);

    /**
     * 新增Push接口字段配置
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return 结果
     */
    public int insertEdiPushFieldConfig(EdiPushFieldConfig ediPushFieldConfig);

    /**
     * 修改Push接口字段配置
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return 结果
     */
    public int updateEdiPushFieldConfig(EdiPushFieldConfig ediPushFieldConfig);

    /**
     * 批量删除Push接口字段配置
     * 
     * @param ids 需要删除的Push接口字段配置主键集合
     * @return 结果
     */
    public int deleteEdiPushFieldConfigByIds(String ids);

    /**
     * 删除Push接口字段配置信息
     * 
     * @param id Push接口字段配置主键
     * @return 结果
     */
    public int deleteEdiPushFieldConfigById(Long id);

    List<EdiPushFieldConfig> selectByInterfacePathAndUser(String interfacePath, Long userId);
}
