package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProOeeTimeDetails;

/**
 * OEE时间明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProOeeTimeDetailsService 
{
    /**
     * 查询OEE时间明细
     * 
     * @param id OEE时间明细主键
     * @return OEE时间明细
     */
    public SupplierProOeeTimeDetails selectSupplierProOeeTimeDetailsById(Long id);

    /**
     * 查询OEE时间明细列表
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return OEE时间明细集合
     */
    public List<SupplierProOeeTimeDetails> selectSupplierProOeeTimeDetailsList(SupplierProOeeTimeDetails supplierProOeeTimeDetails);

    /**
     * 新增OEE时间明细
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return 结果
     */
    public int insertSupplierProOeeTimeDetails(SupplierProOeeTimeDetails supplierProOeeTimeDetails);

    /**
     * 修改OEE时间明细
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return 结果
     */
    public int updateSupplierProOeeTimeDetails(SupplierProOeeTimeDetails supplierProOeeTimeDetails);

    /**
     * 批量删除OEE时间明细
     * 
     * @param ids 需要删除的OEE时间明细主键集合
     * @return 结果
     */
    public int deleteSupplierProOeeTimeDetailsByIds(String ids);

    /**
     * 删除OEE时间明细信息
     * 
     * @param id OEE时间明细主键
     * @return 结果
     */
    public int deleteSupplierProOeeTimeDetailsById(Long id);

    /**
     * 批量更新OEE时间明细
     *
     * @param list OEE时间明细
     * @return 结果
     */
    public int batchUpdate(List<SupplierProOeeTimeDetails> list);

    /**
     * 根据ID批量查询OEE时间明细
     *
     * @param ids
     * @return
     */
    public List<SupplierProOeeTimeDetails> selectSupplierProOeeTimeDetailsByIds(String batchNo);
}
