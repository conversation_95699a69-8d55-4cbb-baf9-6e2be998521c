package com.hj.admin.service.push.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierProProcessGrandChildMapper;
import com.hj.admin.domain.push.SupplierProProcessGrandChild;
import com.hj.admin.service.push.ISupplierProProcessGrandChildService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 工艺子子集（关联supplier_pro_process_child）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProProcessGrandChildServiceImpl implements ISupplierProProcessGrandChildService 
{
    @Resource
    private SupplierProProcessGrandChildMapper supplierProProcessGrandChildMapper;

    /**
     * 查询工艺子子集（关联supplier_pro_process_child）
     * 
     * @param id 工艺子子集（关联supplier_pro_process_child）主键
     * @return 工艺子子集（关联supplier_pro_process_child）
     */
    @Override
    public SupplierProProcessGrandChild selectSupplierProProcessGrandChildById(Long id)
    {
        return supplierProProcessGrandChildMapper.selectSupplierProProcessGrandChildById(id);
    }

    /**
     * 查询工艺子子集（关联supplier_pro_process_child）列表
     * 
     * @param supplierProProcessGrandChild 工艺子子集（关联supplier_pro_process_child）
     * @return 工艺子子集（关联supplier_pro_process_child）
     */
    @Override
    public List<SupplierProProcessGrandChild> selectSupplierProProcessGrandChildList(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        return supplierProProcessGrandChildMapper.selectSupplierProProcessGrandChildList(supplierProProcessGrandChild);
    }

    /**
     * 新增工艺子子集（关联supplier_pro_process_child）
     * 
     * @param supplierProProcessGrandChild 工艺子子集（关联supplier_pro_process_child）
     * @return 结果
     */
    @Override
    public int insertSupplierProProcessGrandChild(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        return supplierProProcessGrandChildMapper.insertSupplierProProcessGrandChild(supplierProProcessGrandChild);
    }

    /**
     * 修改工艺子子集（关联supplier_pro_process_child）
     * 
     * @param supplierProProcessGrandChild 工艺子子集（关联supplier_pro_process_child）
     * @return 结果
     */
    @Override
    public int updateSupplierProProcessGrandChild(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        return supplierProProcessGrandChildMapper.updateSupplierProProcessGrandChild(supplierProProcessGrandChild);
    }

    /**
     * 批量删除工艺子子集（关联supplier_pro_process_child）
     * 
     * @param ids 需要删除的工艺子子集（关联supplier_pro_process_child）主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessGrandChildByIds(String ids)
    {
        return supplierProProcessGrandChildMapper.deleteSupplierProProcessGrandChildByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除工艺子子集（关联supplier_pro_process_child）信息
     * 
     * @param id 工艺子子集（关联supplier_pro_process_child）主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessGrandChildById(Long id)
    {
        return supplierProProcessGrandChildMapper.deleteSupplierProProcessGrandChildById(id);
    }

    @Override
    public int batchUpdate(List<SupplierProProcessGrandChild> list) {
        return supplierProProcessGrandChildMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierProProcessGrandChild> selectSupplierProProcessGrandChildByIds(String batchNo) {
        return supplierProProcessGrandChildMapper.selectSupplierProProcessGrandChildByIds(batchNo);
    }
}
