package com.hj.admin.service.impl;

import com.hj.admin.domain.config.EdiPushFieldConfig;
import com.hj.admin.mapper.config.EdiPushFieldConfigMapper;
import com.hj.admin.service.IEdiPushFieldConfigService;
import com.hj.common.core.text.Convert;
import com.hj.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * Push接口字段配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class EdiPushFieldConfigServiceImpl implements IEdiPushFieldConfigService
{
    @Resource
    private EdiPushFieldConfigMapper ediPushFieldConfigMapper;

    /**
     * 查询Push接口字段配置
     * 
     * @param id Push接口字段配置主键
     * @return Push接口字段配置
     */
    @Override
    public EdiPushFieldConfig selectEdiPushFieldConfigById(Long id)
    {
        return ediPushFieldConfigMapper.selectEdiPushFieldConfigById(id);
    }

    /**
     * 查询Push接口字段配置列表
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return Push接口字段配置
     */
    @Override
    public List<EdiPushFieldConfig> selectEdiPushFieldConfigList(EdiPushFieldConfig ediPushFieldConfig)
    {
        return ediPushFieldConfigMapper.selectEdiPushFieldConfigList(ediPushFieldConfig);
    }

    /**
     * 新增Push接口字段配置
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return 结果
     */
    @Override
    public int insertEdiPushFieldConfig(EdiPushFieldConfig ediPushFieldConfig)
    {
        ediPushFieldConfig.setCreateTime(DateUtils.getNowDate());
        return ediPushFieldConfigMapper.insertEdiPushFieldConfig(ediPushFieldConfig);
    }

    /**
     * 修改Push接口字段配置
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return 结果
     */
    @Override
    public int updateEdiPushFieldConfig(EdiPushFieldConfig ediPushFieldConfig)
    {
        ediPushFieldConfig.setUpdateTime(DateUtils.getNowDate());
        return ediPushFieldConfigMapper.updateEdiPushFieldConfig(ediPushFieldConfig);
    }

    /**
     * 批量删除Push接口字段配置
     * 
     * @param ids 需要删除的Push接口字段配置主键
     * @return 结果
     */
    @Override
    public int deleteEdiPushFieldConfigByIds(String ids)
    {
        return ediPushFieldConfigMapper.deleteEdiPushFieldConfigByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除Push接口字段配置信息
     * 
     * @param id Push接口字段配置主键
     * @return 结果
     */
    @Override
    public int deleteEdiPushFieldConfigById(Long id)
    {
        return ediPushFieldConfigMapper.deleteEdiPushFieldConfigById(id);
    }

    public List<EdiPushFieldConfig> selectByInterfacePathAndUser(String interfacePath, Long userId) {
        return ediPushFieldConfigMapper.selectByInterfacePathAndUser(interfacePath, userId);
    }
}
