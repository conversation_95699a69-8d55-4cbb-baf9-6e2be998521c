package com.hj.admin.service.push.impl;

import com.hj.admin.domain.push.SupplierSinvData;
import com.hj.admin.mapper.push.SupplierSinvDataMapper;
import com.hj.admin.service.push.ISupplierSinvDataService;
import com.hj.common.core.text.Convert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 供应商共享库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class SupplierSinvDataServiceImpl implements ISupplierSinvDataService {
    @Resource
    private SupplierSinvDataMapper supplierSinvDataMapper;

    /**
     * 查询供应商共享库存
     *
     * @param id 供应商共享库存主键
     * @return 供应商共享库存
     */
    @Override
    public SupplierSinvData selectSupplierSinvDataById(Long id) {
        return supplierSinvDataMapper.selectSupplierSinvDataById(id);
    }

    /**
     * 查询供应商共享库存列表
     *
     * @param supplierSinvData 供应商共享库存
     * @return 供应商共享库存
     */
    @Override
    public List<SupplierSinvData> selectSupplierSinvDataList(SupplierSinvData supplierSinvData) {
        return supplierSinvDataMapper.selectSupplierSinvDataList(supplierSinvData);
    }

    /**
     * 新增供应商共享库存
     *
     * @param supplierSinvData 供应商共享库存
     * @return 结果
     */
    @Override
    public int insertSupplierSinvData(SupplierSinvData supplierSinvData) {
        return supplierSinvDataMapper.insertSupplierSinvData(supplierSinvData);
    }

    /**
     * 修改供应商共享库存
     *
     * @param supplierSinvData 供应商共享库存
     * @return 结果
     */
    @Override
    public int updateSupplierSinvData(SupplierSinvData supplierSinvData) {
        return supplierSinvDataMapper.updateSupplierSinvData(supplierSinvData);
    }

    /**
     * 批量删除供应商共享库存
     *
     * @param ids 需要删除的供应商共享库存主键
     * @return 结果
     */
    @Override
    public int deleteSupplierSinvDataByIds(String ids) {
        return supplierSinvDataMapper.deleteSupplierSinvDataByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除供应商共享库存信息
     *
     * @param id 供应商共享库存主键
     * @return 结果
     */
    @Override
    public int deleteSupplierSinvDataById(Long id) {
        return supplierSinvDataMapper.deleteSupplierSinvDataById(id);
    }

    @Override
    public int batchUpdate(List<SupplierSinvData> list) {
        return supplierSinvDataMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierSinvData> selectSupplierSinvDataByIds(String batchNo) {
        return supplierSinvDataMapper.selectSupplierSinvDataByIds(batchNo);
    }
}
