package com.hj.admin.service.impl;

import com.hj.admin.domain.log.EdiFieldModifyLog;
import com.hj.admin.mapper.log.EdiFieldModifyLogMapper;
import com.hj.admin.service.IEdiFieldModifyLogService;
import com.hj.common.utils.ShiroUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 字段维护者修改日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Service
public class EdiFieldModifyLogServiceImpl implements IEdiFieldModifyLogService 
{
    @Resource
    private EdiFieldModifyLogMapper ediFieldModifyLogMapper;

    /**
     * 新增字段维护者修改日志
     * 
     * @param ediFieldModifyLog 字段维护者修改日志
     * @return 结果
     */
    @Override
    public int insertEdiFieldModifyLog(EdiFieldModifyLog ediFieldModifyLog)
    {
        ediFieldModifyLog.setCreateTime(new Date());
        ediFieldModifyLog.setCreateBy(ShiroUtils.getLoginName());
        return ediFieldModifyLogMapper.insertEdiFieldModifyLog(ediFieldModifyLog);
    }

    /**
     * 记录字段修改日志
     * 
     * @param interfaceName 接口名称
     * @param tableName 表名
     * @param dataId 数据ID
     * @param fieldName 字段名
     * @param oldValue 原值
     * @param newValue 新值
     * @param modifyUserId 修改人ID
     * @param modifyUserName 修改人姓名
     */
    @Override
    public void recordFieldModify(String interfaceName, String tableName, Long dataId, 
                                 String fieldName, String oldValue, String newValue,
                                 Long modifyUserId, String modifyUserName)
    {
        EdiFieldModifyLog log = new EdiFieldModifyLog();
        log.setInterfaceName(interfaceName);
        log.setTableName(tableName);
        log.setDataId(dataId);
        log.setFieldName(fieldName);
        log.setOldValue(oldValue);
        log.setNewValue(newValue);
        log.setModifyUserId(modifyUserId);
        log.setModifyUserName(modifyUserName);
        log.setModifyTime(new Date());
        
        insertEdiFieldModifyLog(log);
    }
}
