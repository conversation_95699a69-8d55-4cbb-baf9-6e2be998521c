package com.hj.admin.service.impl;

import com.hj.admin.domain.log.EdiOperationLog;
import com.hj.admin.mapper.log.EdiOperationLogMapper;
import com.hj.admin.service.IEdiOperationLogService;
import com.hj.common.core.text.Convert;
import com.hj.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * EDI操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class EdiOperationLogServiceImpl implements IEdiOperationLogService {
    @Resource
    private EdiOperationLogMapper ediOperationLogMapper;

    /**
     * 查询EDI操作记录
     * 
     * @param id EDI操作记录主键
     * @return EDI操作记录
     */
    @Override
    public EdiOperationLog selectEdiOperationLogById(Long id) {
        return ediOperationLogMapper.selectEdiOperationLogById(id);
    }

    /**
     * 查询EDI操作记录列表
     * 
     * @param ediOperationLog EDI操作记录
     * @return EDI操作记录
     */
    @Override
    public List<EdiOperationLog> selectEdiOperationLogList(EdiOperationLog ediOperationLog) {
        return ediOperationLogMapper.selectEdiOperationLogList(ediOperationLog);
    }

    /**
     * 新增EDI操作记录
     * 
     * @param ediOperationLog EDI操作记录
     * @return 结果
     */
    @Override
    public int insertEdiOperationLog(EdiOperationLog ediOperationLog) {
        ediOperationLog.setCreateTime(DateUtils.getNowDate());
        return ediOperationLogMapper.insertEdiOperationLog(ediOperationLog);
    }

    /**
     * 修改EDI操作记录
     * 
     * @param ediOperationLog EDI操作记录
     * @return 结果
     */
    @Override
    public int updateEdiOperationLog(EdiOperationLog ediOperationLog) {
        ediOperationLog.setUpdateTime(DateUtils.getNowDate());
        return ediOperationLogMapper.updateEdiOperationLog(ediOperationLog);
    }

    /**
     * 批量删除EDI操作记录
     * 
     * @param ids 需要删除的EDI操作记录主键
     * @return 结果
     */
    @Override
    public int deleteEdiOperationLogByIds(String ids) {
        return ediOperationLogMapper.deleteEdiOperationLogByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除EDI操作记录信息
     * 
     * @param id EDI操作记录主键
     * @return 结果
     */
    @Override
    public int deleteEdiOperationLogById(Long id) {
        return ediOperationLogMapper.deleteEdiOperationLogById(id);
    }

    /**
     * 根据批次号查询操作记录
     * 
     * @param batchNo 批次号
     * @return EDI操作记录
     */
    @Override
    public EdiOperationLog selectEdiOperationLogByBatchNo(String batchNo) {
        return ediOperationLogMapper.selectEdiOperationLogByBatchNo(batchNo);
    }

    /**
     * 根据接口路径和操作类型查询最新记录
     * 
     * @param interfacePath 接口路径
     * @param operationType 操作类型
     * @return EDI操作记录
     */
    @Override
    public EdiOperationLog selectLatestLogByPathAndType(String interfacePath, String operationType) {
        return ediOperationLogMapper.selectLatestLogByPathAndType(interfacePath, operationType);
    }

    /**
     * 创建操作记录（开始记录）
     * 
     * @param operationType 操作类型
     * @param interfacePath 接口路径
     * @param interfaceName 接口名称
     * @param batchNo 批次号
     * @param requestParams 请求参数
     * @param isScheduled 是否定时任务
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 操作记录ID
     */
    @Override
    public Long createOperationLog(String operationType, String interfacePath, String interfaceName,
                                   String batchNo, String requestParams, Integer isScheduled, 
                                   Long operatorId, String operatorName) {
        EdiOperationLog log = new EdiOperationLog();
        
        log.setBatchNo(batchNo);
        log.setOperationType(operationType);
        log.setInterfacePath(interfacePath);
        log.setInterfaceName(interfaceName);
        log.setPushStatus(1); // 推送中
        log.setRequestStatus(0); // 初始状态为失败，成功后更新
        log.setRequestParams(requestParams);
        log.setStartTime(new Date());
        log.setRetryCount(0);
        log.setIsScheduled(isScheduled);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        ediOperationLogMapper.insertEdiOperationLog(log);
        return log.getId();
    }

    /**
     * 更新操作记录（完成记录）
     * 
     * @param logId 记录ID
     * @param requestStatus 请求状态
     * @param pushStatus 推送状态
     * @param responseResult 响应结果
     * @param errorMessage 错误信息
     * @return 结果
     */
    @Override
    public int updateOperationLogResult(Long logId, Integer requestStatus, Integer pushStatus, 
                                        String responseResult, String errorMessage) {
        EdiOperationLog log = new EdiOperationLog();
        log.setId(logId);
        log.setRequestStatus(requestStatus);
        log.setPushStatus(pushStatus);
        log.setResponseResult(responseResult);
        log.setErrorMessage(errorMessage);
        log.setEndTime(new Date());
        
        // 计算执行耗时
        EdiOperationLog existingLog = ediOperationLogMapper.selectEdiOperationLogById(logId);
        if (existingLog != null && existingLog.getStartTime() != null) {
            long executionTime = new Date().getTime() - existingLog.getStartTime().getTime();
            log.setExecutionTime(executionTime);
        }
        
        log.setUpdateTime(DateUtils.getNowDate());
        return ediOperationLogMapper.updateEdiOperationLog(log);
    }

}
