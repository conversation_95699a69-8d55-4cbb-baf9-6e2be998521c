package com.hj.admin.service.push.impl;


import com.hj.admin.domain.push.SupplierProScheduling;
import com.hj.admin.mapper.push.SupplierProSchedulingMapper;
import com.hj.admin.service.push.ISupplierProSchedulingService;
import com.hj.common.core.text.Convert;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 排产数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProSchedulingServiceImpl implements ISupplierProSchedulingService
{
    @Resource
    private SupplierProSchedulingMapper supplierProSchedulingMapper;

    /**
     * 查询排产数据
     * 
     * @param id 排产数据主键
     * @return 排产数据
     */
    @Override
    public SupplierProScheduling selectSupplierProSchedulingById(Long id)
    {
        return supplierProSchedulingMapper.selectSupplierProSchedulingById(id);
    }

    /**
     * 查询排产数据列表
     * 
     * @param supplierProScheduling 排产数据
     * @return 排产数据
     */
    @Override
    public List<SupplierProScheduling> selectSupplierProSchedulingList(SupplierProScheduling supplierProScheduling)
    {
        return supplierProSchedulingMapper.selectSupplierProSchedulingList(supplierProScheduling);
    }

    /**
     * 新增排产数据
     * 
     * @param supplierProScheduling 排产数据
     * @return 结果
     */
    @Override
    public int insertSupplierProScheduling(SupplierProScheduling supplierProScheduling)
    {
        return supplierProSchedulingMapper.insertSupplierProScheduling(supplierProScheduling);
    }

    /**
     * 修改排产数据
     * 
     * @param supplierProScheduling 排产数据
     * @return 结果
     */
    @Override
    public int updateSupplierProScheduling(SupplierProScheduling supplierProScheduling)
    {
        return supplierProSchedulingMapper.updateSupplierProScheduling(supplierProScheduling);
    }

    /**
     * 批量删除排产数据
     * 
     * @param ids 需要删除的排产数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProSchedulingByIds(String ids)
    {
        return supplierProSchedulingMapper.deleteSupplierProSchedulingByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除排产数据信息
     * 
     * @param id 排产数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProSchedulingById(Long id)
    {
        return supplierProSchedulingMapper.deleteSupplierProSchedulingById(id);
    }

    /**
     * 批量更新排产数据
     * 
     * @param list 排产数据列表
     * @return 结果
     */
    @Override
    public int batchUpdate(List<SupplierProScheduling> list) {
        return supplierProSchedulingMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierProScheduling> selectSupplierProSchedulingByIds(String batchNo) {
        return supplierProSchedulingMapper.selectSupplierProSchedulingByIds(batchNo);
    }
}
