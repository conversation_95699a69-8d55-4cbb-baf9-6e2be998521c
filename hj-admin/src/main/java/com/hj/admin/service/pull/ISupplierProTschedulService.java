package com.hj.admin.service.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierProTschedul;

/**
 * 过涂装未过总装Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProTschedulService 
{
    /**
     * 查询过涂装未过总装
     * 
     * @param id 过涂装未过总装主键
     * @return 过涂装未过总装
     */
    public SupplierProTschedul selectSupplierProTschedulById(Long id);

    /**
     * 查询过涂装未过总装列表
     * 
     * @param supplierProTschedul 过涂装未过总装
     * @return 过涂装未过总装集合
     */
    public List<SupplierProTschedul> selectSupplierProTschedulList(SupplierProTschedul supplierProTschedul);

    /**
     * 新增过涂装未过总装
     * 
     * @param supplierProTschedul 过涂装未过总装
     * @return 结果
     */
    public int insertSupplierProTschedul(SupplierProTschedul supplierProTschedul);

    /**
     * 修改过涂装未过总装
     * 
     * @param supplierProTschedul 过涂装未过总装
     * @return 结果
     */
    public int updateSupplierProTschedul(SupplierProTschedul supplierProTschedul);

    /**
     * 批量删除过涂装未过总装
     * 
     * @param ids 需要删除的过涂装未过总装主键集合
     * @return 结果
     */
    public int deleteSupplierProTschedulByIds(String ids);

    /**
     * 删除过涂装未过总装信息
     *
     * @param id 过涂装未过总装主键
     * @return 结果
     */
    public int deleteSupplierProTschedulById(Long id);

    /**
     * 批量插入或更新过涂装未过总装
     * 根据id判断是插入还是更新
     *
     * @param supplierProTschedulList 过涂装未过总装列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<SupplierProTschedul> supplierProTschedulList);
}
