package com.hj.admin.service;

import com.hj.admin.domain.log.EdiFieldModifyLog;

/**
 * 字段维护者修改日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface IEdiFieldModifyLogService 
{
    /**
     * 新增字段维护者修改日志
     * 
     * @param ediFieldModifyLog 字段维护者修改日志
     * @return 结果
     */
    public int insertEdiFieldModifyLog(EdiFieldModifyLog ediFieldModifyLog);

    /**
     * 记录字段修改日志
     * 
     * @param interfaceName 接口名称
     * @param tableName 表名
     * @param dataId 数据ID
     * @param fieldName 字段名
     * @param oldValue 原值
     * @param newValue 新值
     * @param modifyUserId 修改人ID
     * @param modifyUserName 修改人姓名
     */
    public void recordFieldModify(String interfaceName, String tableName, Long dataId, 
                                 String fieldName, String oldValue, String newValue,
                                 Long modifyUserId, String modifyUserName);
}
