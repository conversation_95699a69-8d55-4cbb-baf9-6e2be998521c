package com.hj.admin.service.pull.impl;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.pull.SupplierPoMapper;
import com.hj.admin.domain.pull.SupplierPo;
import com.hj.admin.service.pull.ISupplierPoService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 采购订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierPoServiceImpl implements ISupplierPoService 
{
    @Resource
    private SupplierPoMapper supplierPoMapper;

    /**
     * 查询采购订单
     * 
     * @param id 采购订单主键
     * @return 采购订单
     */
    @Override
    public SupplierPo selectSupplierPoById(Long id)
    {
        return supplierPoMapper.selectSupplierPoById(id);
    }

    /**
     * 查询采购订单列表
     * 
     * @param supplierPo 采购订单
     * @return 采购订单
     */
    @Override
    public List<SupplierPo> selectSupplierPoList(SupplierPo supplierPo)
    {
        return supplierPoMapper.selectSupplierPoList(supplierPo);
    }

    /**
     * 新增采购订单
     * 
     * @param supplierPo 采购订单
     * @return 结果
     */
    @Override
    public int insertSupplierPo(SupplierPo supplierPo)
    {
        supplierPo.setInsertTime(DateUtils.getNowDate());
        return supplierPoMapper.insertSupplierPo(supplierPo);
    }

    /**
     * 修改采购订单
     * 
     * @param supplierPo 采购订单
     * @return 结果
     */
    @Override
    public int updateSupplierPo(SupplierPo supplierPo)
    {
        supplierPo.setModifyTime(DateUtils.getNowDate());
        return supplierPoMapper.updateSupplierPo(supplierPo);
    }

    /**
     * 批量删除采购订单
     * 
     * @param ids 需要删除的采购订单主键
     * @return 结果
     */
    @Override
    public int deleteSupplierPoByIds(String ids)
    {
        return supplierPoMapper.deleteSupplierPoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除采购订单信息
     * 
     * @param id 采购订单主键
     * @return 结果
     */
    @Override
    public int deleteSupplierPoById(Long id)
    {
        return supplierPoMapper.deleteSupplierPoById(id);
    }

    /**
     * 批量插入或更新采购订单
     * 根据id判断是插入还是更新
     *
     * @param supplierPoList 采购订单列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierPo> supplierPoList) {
        if (supplierPoList == null || supplierPoList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierPo item : supplierPoList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierPoMapper.batchInsertOrUpdate(supplierPoList);
    }
}
