package com.hj.admin.service.push.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierProProcessChildMapper;
import com.hj.admin.domain.push.SupplierProProcessChild;
import com.hj.admin.service.push.ISupplierProProcessChildService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 工艺子集（关联supplier_pro_process）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProProcessChildServiceImpl implements ISupplierProProcessChildService 
{
    @Resource
    private SupplierProProcessChildMapper supplierProProcessChildMapper;

    /**
     * 查询工艺子集（关联supplier_pro_process）
     * 
     * @param id 工艺子集（关联supplier_pro_process）主键
     * @return 工艺子集（关联supplier_pro_process）
     */
    @Override
    public SupplierProProcessChild selectSupplierProProcessChildById(Long id)
    {
        return supplierProProcessChildMapper.selectSupplierProProcessChildById(id);
    }

    /**
     * 查询工艺子集（关联supplier_pro_process）列表
     * 
     * @param supplierProProcessChild 工艺子集（关联supplier_pro_process）
     * @return 工艺子集（关联supplier_pro_process）
     */
    @Override
    public List<SupplierProProcessChild> selectSupplierProProcessChildList(SupplierProProcessChild supplierProProcessChild)
    {
        return supplierProProcessChildMapper.selectSupplierProProcessChildList(supplierProProcessChild);
    }

    /**
     * 新增工艺子集（关联supplier_pro_process）
     * 
     * @param supplierProProcessChild 工艺子集（关联supplier_pro_process）
     * @return 结果
     */
    @Override
    public int insertSupplierProProcessChild(SupplierProProcessChild supplierProProcessChild)
    {
        return supplierProProcessChildMapper.insertSupplierProProcessChild(supplierProProcessChild);
    }

    /**
     * 修改工艺子集（关联supplier_pro_process）
     * 
     * @param supplierProProcessChild 工艺子集（关联supplier_pro_process）
     * @return 结果
     */
    @Override
    public int updateSupplierProProcessChild(SupplierProProcessChild supplierProProcessChild)
    {
        return supplierProProcessChildMapper.updateSupplierProProcessChild(supplierProProcessChild);
    }

    /**
     * 批量删除工艺子集（关联supplier_pro_process）
     * 
     * @param ids 需要删除的工艺子集（关联supplier_pro_process）主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessChildByIds(String ids)
    {
        return supplierProProcessChildMapper.deleteSupplierProProcessChildByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除工艺子集（关联supplier_pro_process）信息
     * 
     * @param id 工艺子集（关联supplier_pro_process）主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessChildById(Long id)
    {
        return supplierProProcessChildMapper.deleteSupplierProProcessChildById(id);
    }

    @Override
    public int batchUpdate(List<SupplierProProcessChild> list) {
        return supplierProProcessChildMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierProProcessChild> selectSupplierProProcessChildByIds(String batchNo) {
        return supplierProProcessChildMapper.selectSupplierProProcessChildByIds(batchNo);
    }
}
