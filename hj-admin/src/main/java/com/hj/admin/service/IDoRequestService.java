package com.hj.admin.service;

import com.hj.admin.domain.base.CheryBaseRes;
import com.hj.admin.domain.base.CheryPushReq;
import com.hj.admin.domain.base.CheryPullRes;

import java.util.Map;

public interface IDoRequestService {

    CheryBaseRes pull(String path, String interfaceName, Map<String, Object> map) throws Exception;

    CheryBaseRes push(String path, String interfaceName, CheryPushReq cheryPushReq) throws Exception;

}
