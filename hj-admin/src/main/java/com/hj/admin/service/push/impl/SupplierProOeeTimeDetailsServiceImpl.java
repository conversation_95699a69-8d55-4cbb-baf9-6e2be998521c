package com.hj.admin.service.push.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierProOeeTimeDetailsMapper;
import com.hj.admin.domain.push.SupplierProOeeTimeDetails;
import com.hj.admin.service.push.ISupplierProOeeTimeDetailsService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * OEE时间明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProOeeTimeDetailsServiceImpl implements ISupplierProOeeTimeDetailsService 
{
    @Resource
    private SupplierProOeeTimeDetailsMapper supplierProOeeTimeDetailsMapper;

    /**
     * 查询OEE时间明细
     * 
     * @param id OEE时间明细主键
     * @return OEE时间明细
     */
    @Override
    public SupplierProOeeTimeDetails selectSupplierProOeeTimeDetailsById(Long id)
    {
        return supplierProOeeTimeDetailsMapper.selectSupplierProOeeTimeDetailsById(id);
    }

    /**
     * 查询OEE时间明细列表
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return OEE时间明细
     */
    @Override
    public List<SupplierProOeeTimeDetails> selectSupplierProOeeTimeDetailsList(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        return supplierProOeeTimeDetailsMapper.selectSupplierProOeeTimeDetailsList(supplierProOeeTimeDetails);
    }

    /**
     * 新增OEE时间明细
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return 结果
     */
    @Override
    public int insertSupplierProOeeTimeDetails(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        return supplierProOeeTimeDetailsMapper.insertSupplierProOeeTimeDetails(supplierProOeeTimeDetails);
    }

    /**
     * 修改OEE时间明细
     * 
     * @param supplierProOeeTimeDetails OEE时间明细
     * @return 结果
     */
    @Override
    public int updateSupplierProOeeTimeDetails(SupplierProOeeTimeDetails supplierProOeeTimeDetails)
    {
        return supplierProOeeTimeDetailsMapper.updateSupplierProOeeTimeDetails(supplierProOeeTimeDetails);
    }

    /**
     * 批量删除OEE时间明细
     * 
     * @param ids 需要删除的OEE时间明细主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProOeeTimeDetailsByIds(String ids)
    {
        return supplierProOeeTimeDetailsMapper.deleteSupplierProOeeTimeDetailsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除OEE时间明细信息
     * 
     * @param id OEE时间明细主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProOeeTimeDetailsById(Long id)
    {
        return supplierProOeeTimeDetailsMapper.deleteSupplierProOeeTimeDetailsById(id);
    }

    @Override
    public int batchUpdate(List<SupplierProOeeTimeDetails> list) {
        return supplierProOeeTimeDetailsMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierProOeeTimeDetails> selectSupplierProOeeTimeDetailsByIds(String batchNo) {
        return supplierProOeeTimeDetailsMapper.selectSupplierProOeeTimeDetailsByIds(batchNo);
    }
}
