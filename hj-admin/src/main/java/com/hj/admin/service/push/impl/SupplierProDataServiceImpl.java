package com.hj.admin.service.push.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierProDataMapper;
import com.hj.admin.domain.push.SupplierProData;
import com.hj.admin.service.push.ISupplierProDataService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 生产过程数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProDataServiceImpl implements ISupplierProDataService 
{
    @Resource
    private SupplierProDataMapper supplierProDataMapper;

    /**
     * 查询生产过程数据
     * 
     * @param id 生产过程数据主键
     * @return 生产过程数据
     */
    @Override
    public SupplierProData selectSupplierProDataById(Long id)
    {
        return supplierProDataMapper.selectSupplierProDataById(id);
    }

    /**
     * 查询生产过程数据列表
     * 
     * @param supplierProData 生产过程数据
     * @return 生产过程数据
     */
    @Override
    public List<SupplierProData> selectSupplierProDataList(SupplierProData supplierProData)
    {
        return supplierProDataMapper.selectSupplierProDataList(supplierProData);
    }

    /**
     * 新增生产过程数据
     * 
     * @param supplierProData 生产过程数据
     * @return 结果
     */
    @Override
    public int insertSupplierProData(SupplierProData supplierProData)
    {
        return supplierProDataMapper.insertSupplierProData(supplierProData);
    }

    /**
     * 修改生产过程数据
     * 
     * @param supplierProData 生产过程数据
     * @return 结果
     */
    @Override
    public int updateSupplierProData(SupplierProData supplierProData)
    {
        return supplierProDataMapper.updateSupplierProData(supplierProData);
    }

    /**
     * 批量删除生产过程数据
     * 
     * @param ids 需要删除的生产过程数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProDataByIds(String ids)
    {
        return supplierProDataMapper.deleteSupplierProDataByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除生产过程数据信息
     * 
     * @param id 生产过程数据主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProDataById(Long id)
    {
        return supplierProDataMapper.deleteSupplierProDataById(id);
    }

    /**
     * 批量更新生产过程数据
     * 
     * @param list 生产过程数据列表
     * @return 结果
     */
    @Override
    public int batchUpdate(List<SupplierProData> list) {
        return supplierProDataMapper.batchUpdate(list);
    }

    /**
     * 根据ID批量查询生产过程数据
     *
     * @param ids
     * @return
     */
    @Override
    public List<SupplierProData> selectSupplierProDataByIds(String batchNo) {
        return supplierProDataMapper.selectSupplierProDataByIds(batchNo);
    }
}
