package com.hj.admin.service.pull.impl;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.pull.SupplierProCschedulMapper;
import com.hj.admin.domain.pull.SupplierProCschedul;
import com.hj.admin.service.pull.ISupplierProCschedulService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 排序供货Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProCschedulServiceImpl implements ISupplierProCschedulService 
{
    @Resource
    private SupplierProCschedulMapper supplierProCschedulMapper;

    /**
     * 查询排序供货
     * 
     * @param id 排序供货主键
     * @return 排序供货
     */
    @Override
    public SupplierProCschedul selectSupplierProCschedulById(Long id)
    {
        return supplierProCschedulMapper.selectSupplierProCschedulById(id);
    }

    /**
     * 查询排序供货列表
     * 
     * @param supplierProCschedul 排序供货
     * @return 排序供货
     */
    @Override
    public List<SupplierProCschedul> selectSupplierProCschedulList(SupplierProCschedul supplierProCschedul)
    {
        return supplierProCschedulMapper.selectSupplierProCschedulList(supplierProCschedul);
    }

    /**
     * 新增排序供货
     * 
     * @param supplierProCschedul 排序供货
     * @return 结果
     */
    @Override
    public int insertSupplierProCschedul(SupplierProCschedul supplierProCschedul)
    {
        supplierProCschedul.setInsertTime(DateUtils.getNowDate());
        return supplierProCschedulMapper.insertSupplierProCschedul(supplierProCschedul);
    }

    /**
     * 修改排序供货
     * 
     * @param supplierProCschedul 排序供货
     * @return 结果
     */
    @Override
    public int updateSupplierProCschedul(SupplierProCschedul supplierProCschedul)
    {
        supplierProCschedul.setModifyTime(DateUtils.getNowDate());
        return supplierProCschedulMapper.updateSupplierProCschedul(supplierProCschedul);
    }

    /**
     * 批量删除排序供货
     * 
     * @param ids 需要删除的排序供货主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProCschedulByIds(String ids)
    {
        return supplierProCschedulMapper.deleteSupplierProCschedulByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除排序供货信息
     * 
     * @param id 排序供货主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProCschedulById(Long id)
    {
        return supplierProCschedulMapper.deleteSupplierProCschedulById(id);
    }

    /**
     * 批量插入或更新排序供货
     * 根据id判断是插入还是更新
     *
     * @param supplierProCschedulList 排序供货列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierProCschedul> supplierProCschedulList) {
        if (supplierProCschedulList == null || supplierProCschedulList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierProCschedul item : supplierProCschedulList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierProCschedulMapper.batchInsertOrUpdate(supplierProCschedulList);
    }
}
