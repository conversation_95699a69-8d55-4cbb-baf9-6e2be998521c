package com.hj.admin.service.pull.impl;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierMrpDateMapper;
import com.hj.admin.domain.pull.SupplierMrpDate;
import com.hj.admin.service.pull.ISupplierMrpDateService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 日物料需求计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierMrpDateServiceImpl implements ISupplierMrpDateService 
{
    @Resource
    private SupplierMrpDateMapper supplierMrpDateMapper;

    /**
     * 查询日物料需求计划
     * 
     * @param id 日物料需求计划主键
     * @return 日物料需求计划
     */
    @Override
    public SupplierMrpDate selectSupplierMrpDateById(Long id)
    {
        return supplierMrpDateMapper.selectSupplierMrpDateById(id);
    }

    /**
     * 查询日物料需求计划列表
     * 
     * @param supplierMrpDate 日物料需求计划
     * @return 日物料需求计划
     */
    @Override
    public List<SupplierMrpDate> selectSupplierMrpDateList(SupplierMrpDate supplierMrpDate)
    {
        return supplierMrpDateMapper.selectSupplierMrpDateList(supplierMrpDate);
    }

    /**
     * 新增日物料需求计划
     * 
     * @param supplierMrpDate 日物料需求计划
     * @return 结果
     */
    @Override
    public int insertSupplierMrpDate(SupplierMrpDate supplierMrpDate)
    {
        supplierMrpDate.setInsertTime(DateUtils.getNowDate());
        return supplierMrpDateMapper.insertSupplierMrpDate(supplierMrpDate);
    }

    /**
     * 修改日物料需求计划
     * 
     * @param supplierMrpDate 日物料需求计划
     * @return 结果
     */
    @Override
    public int updateSupplierMrpDate(SupplierMrpDate supplierMrpDate)
    {
        supplierMrpDate.setModifyTime(DateUtils.getNowDate());
        return supplierMrpDateMapper.updateSupplierMrpDate(supplierMrpDate);
    }

    /**
     * 批量删除日物料需求计划
     * 
     * @param ids 需要删除的日物料需求计划主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpDateByIds(String ids)
    {
        return supplierMrpDateMapper.deleteSupplierMrpDateByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除日物料需求计划信息
     * 
     * @param id 日物料需求计划主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpDateById(Long id)
    {
        return supplierMrpDateMapper.deleteSupplierMrpDateById(id);
    }

    /**
     * 批量插入或更新日物料需求计划
     * 根据id判断是插入还是更新
     *
     * @param supplierMrpDateList 日物料需求计划列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierMrpDate> supplierMrpDateList) {
        if (supplierMrpDateList == null || supplierMrpDateList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierMrpDate item : supplierMrpDateList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierMrpDateMapper.batchInsertOrUpdate(supplierMrpDateList);
    }
}
