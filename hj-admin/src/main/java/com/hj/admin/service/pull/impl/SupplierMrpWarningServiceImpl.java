package com.hj.admin.service.pull.impl;

import java.util.List;
import com.hj.common.utils.DateUtils;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.pull.SupplierMrpWarningMapper;
import com.hj.admin.domain.pull.SupplierMrpWarning;
import com.hj.admin.service.pull.ISupplierMrpWarningService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 日MRP预警推移Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierMrpWarningServiceImpl implements ISupplierMrpWarningService 
{
    @Resource
    private SupplierMrpWarningMapper supplierMrpWarningMapper;

    /**
     * 查询日MRP预警推移
     * 
     * @param id 日MRP预警推移主键
     * @return 日MRP预警推移
     */
    @Override
    public SupplierMrpWarning selectSupplierMrpWarningById(Long id)
    {
        return supplierMrpWarningMapper.selectSupplierMrpWarningById(id);
    }

    /**
     * 查询日MRP预警推移列表
     * 
     * @param supplierMrpWarning 日MRP预警推移
     * @return 日MRP预警推移
     */
    @Override
    public List<SupplierMrpWarning> selectSupplierMrpWarningList(SupplierMrpWarning supplierMrpWarning)
    {
        return supplierMrpWarningMapper.selectSupplierMrpWarningList(supplierMrpWarning);
    }

    /**
     * 新增日MRP预警推移
     * 
     * @param supplierMrpWarning 日MRP预警推移
     * @return 结果
     */
    @Override
    public int insertSupplierMrpWarning(SupplierMrpWarning supplierMrpWarning)
    {
        supplierMrpWarning.setInsertTime(DateUtils.getNowDate());
        return supplierMrpWarningMapper.insertSupplierMrpWarning(supplierMrpWarning);
    }

    /**
     * 修改日MRP预警推移
     * 
     * @param supplierMrpWarning 日MRP预警推移
     * @return 结果
     */
    @Override
    public int updateSupplierMrpWarning(SupplierMrpWarning supplierMrpWarning)
    {
        supplierMrpWarning.setModifyTime(DateUtils.getNowDate());
        return supplierMrpWarningMapper.updateSupplierMrpWarning(supplierMrpWarning);
    }

    /**
     * 批量删除日MRP预警推移
     * 
     * @param ids 需要删除的日MRP预警推移主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpWarningByIds(String ids)
    {
        return supplierMrpWarningMapper.deleteSupplierMrpWarningByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除日MRP预警推移信息
     * 
     * @param id 日MRP预警推移主键
     * @return 结果
     */
    @Override
    public int deleteSupplierMrpWarningById(Long id)
    {
        return supplierMrpWarningMapper.deleteSupplierMrpWarningById(id);
    }

    /**
     * 批量插入或更新日MRP预警推移
     * 根据id判断是插入还是更新
     *
     * @param supplierMrpWarningList 日MRP预警推移列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierMrpWarning> supplierMrpWarningList) {
        if (supplierMrpWarningList == null || supplierMrpWarningList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierMrpWarning item : supplierMrpWarningList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierMrpWarningMapper.batchInsertOrUpdate(supplierMrpWarningList);
    }
}
