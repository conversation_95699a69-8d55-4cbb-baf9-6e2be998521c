package com.hj.admin.service.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProStationFirstPassyield;

/**
 * 工位一次合格率Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface ISupplierProStationFirstPassyieldService 
{
    /**
     * 查询工位一次合格率
     * 
     * @param id 工位一次合格率主键
     * @return 工位一次合格率
     */
    public SupplierProStationFirstPassyield selectSupplierProStationFirstPassyieldById(Long id);

    /**
     * 查询工位一次合格率列表
     * 
     * @param supplierProStationFirstPassyield 工位一次合格率
     * @return 工位一次合格率集合
     */
    public List<SupplierProStationFirstPassyield> selectSupplierProStationFirstPassyieldList(SupplierProStationFirstPassyield supplierProStationFirstPassyield);

    /**
     * 新增工位一次合格率
     * 
     * @param supplierProStationFirstPassyield 工位一次合格率
     * @return 结果
     */
    public int insertSupplierProStationFirstPassyield(SupplierProStationFirstPassyield supplierProStationFirstPassyield);

    /**
     * 修改工位一次合格率
     * 
     * @param supplierProStationFirstPassyield 工位一次合格率
     * @return 结果
     */
    public int updateSupplierProStationFirstPassyield(SupplierProStationFirstPassyield supplierProStationFirstPassyield);

    /**
     * 批量删除工位一次合格率
     * 
     * @param ids 需要删除的工位一次合格率主键集合
     * @return 结果
     */
    public int deleteSupplierProStationFirstPassyieldByIds(String ids);

    /**
     * 删除工位一次合格率信息
     * 
     * @param id 工位一次合格率主键
     * @return 结果
     */
    public int deleteSupplierProStationFirstPassyieldById(Long id);

    /**
     * 批量更新工位一次合格率
     *
     * @param list 工位一次合格率
     * @return 结果
     */
    public int batchUpdate(List<SupplierProStationFirstPassyield> list);

    /**
     * 根据ID批量查询工位一次合格率
     *
     * @param ids
     * @return
     */
    public List<SupplierProStationFirstPassyield> selectSupplierProStationFirstPassyieldByIds(String batchNo);
}
