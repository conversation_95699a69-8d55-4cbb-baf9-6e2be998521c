package com.hj.admin.service.push.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import com.hj.admin.mapper.push.SupplierProProcessMapper;
import com.hj.admin.domain.push.SupplierProProcess;
import com.hj.admin.service.push.ISupplierProProcessService;
import com.hj.common.core.text.Convert;

import javax.annotation.Resource;

/**
 * 工艺Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class SupplierProProcessServiceImpl implements ISupplierProProcessService 
{
    @Resource
    private SupplierProProcessMapper supplierProProcessMapper;

    /**
     * 查询工艺
     * 
     * @param id 工艺主键
     * @return 工艺
     */
    @Override
    public SupplierProProcess selectSupplierProProcessById(Long id)
    {
        return supplierProProcessMapper.selectSupplierProProcessById(id);
    }

    /**
     * 查询工艺列表
     * 
     * @param supplierProProcess 工艺
     * @return 工艺
     */
    @Override
    public List<SupplierProProcess> selectSupplierProProcessList(SupplierProProcess supplierProProcess)
    {
        return supplierProProcessMapper.selectSupplierProProcessList(supplierProProcess);
    }

    /**
     * 新增工艺
     * 
     * @param supplierProProcess 工艺
     * @return 结果
     */
    @Override
    public int insertSupplierProProcess(SupplierProProcess supplierProProcess)
    {
        return supplierProProcessMapper.insertSupplierProProcess(supplierProProcess);
    }

    /**
     * 修改工艺
     * 
     * @param supplierProProcess 工艺
     * @return 结果
     */
    @Override
    public int updateSupplierProProcess(SupplierProProcess supplierProProcess)
    {
        return supplierProProcessMapper.updateSupplierProProcess(supplierProProcess);
    }

    /**
     * 批量删除工艺
     * 
     * @param ids 需要删除的工艺主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessByIds(String ids)
    {
        return supplierProProcessMapper.deleteSupplierProProcessByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除工艺信息
     * 
     * @param id 工艺主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProProcessById(Long id)
    {
        return supplierProProcessMapper.deleteSupplierProProcessById(id);
    }

    @Override
    public int batchUpdate(List<SupplierProProcess> list) {
        return supplierProProcessMapper.batchUpdate(list);
    }

    @Override
    public List<SupplierProProcess> selectSupplierProProcessByIds(String batchNo) {
        return supplierProProcessMapper.selectSupplierProProcessByIds(batchNo);
    }
}
