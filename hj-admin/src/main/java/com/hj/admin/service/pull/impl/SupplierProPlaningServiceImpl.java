package com.hj.admin.service.pull.impl;

import com.hj.admin.domain.pull.SupplierProPlaning;
import com.hj.admin.mapper.pull.SupplierProPlaningMapper;
import com.hj.admin.service.pull.ISupplierProPlaningService;
import com.hj.common.core.text.Convert;
import com.hj.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 整车月度生产计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class SupplierProPlaningServiceImpl implements ISupplierProPlaningService 
{
    @Resource
    private SupplierProPlaningMapper supplierProPlaningMapper;

    /**
     * 查询整车月度生产计划
     * 
     * @param id 整车月度生产计划主键
     * @return 整车月度生产计划
     */
    @Override
    public SupplierProPlaning selectSupplierProPlaningById(Long id)
    {
        return supplierProPlaningMapper.selectSupplierProPlaningById(id);
    }

    /**
     * 查询整车月度生产计划列表
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 整车月度生产计划
     */
    @Override
    public List<SupplierProPlaning> selectSupplierProPlaningList(SupplierProPlaning supplierProPlaning)
    {
        return supplierProPlaningMapper.selectSupplierProPlaningList(supplierProPlaning);
    }

    /**
     * 新增整车月度生产计划
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 结果
     */
    @Override
    public int insertSupplierProPlaning(SupplierProPlaning supplierProPlaning)
    {
        supplierProPlaning.setInsertTime(DateUtils.getNowDate());
        return supplierProPlaningMapper.insertSupplierProPlaning(supplierProPlaning);
    }

    /**
     * 修改整车月度生产计划
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 结果
     */
    @Override
    public int updateSupplierProPlaning(SupplierProPlaning supplierProPlaning)
    {
        supplierProPlaning.setModifyTime(DateUtils.getNowDate());
        return supplierProPlaningMapper.updateSupplierProPlaning(supplierProPlaning);
    }

    /**
     * 批量删除整车月度生产计划
     * 
     * @param ids 需要删除的整车月度生产计划主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProPlaningByIds(String ids)
    {
        return supplierProPlaningMapper.deleteSupplierProPlaningByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除整车月度生产计划信息
     * 
     * @param id 整车月度生产计划主键
     * @return 结果
     */
    @Override
    public int deleteSupplierProPlaningById(Long id)
    {
        return supplierProPlaningMapper.deleteSupplierProPlaningById(id);
    }

    /**
     * 批量插入或更新整车月度生产计划
     * 根据id判断是插入还是更新
     *
     * @param supplierProPlaningList 整车月度生产计划列表
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<SupplierProPlaning> supplierProPlaningList) {
        if (supplierProPlaningList == null || supplierProPlaningList.isEmpty()) {
            return 0;
        }

        // 设置插入时间和修改时间
        for (SupplierProPlaning item : supplierProPlaningList) {
            if (item.getInsertTime() == null) {
                item.setInsertTime(DateUtils.getNowDate());
            }
            item.setModifyTime(DateUtils.getNowDate());
        }

        return supplierProPlaningMapper.batchInsertOrUpdate(supplierProPlaningList);
    }
}
