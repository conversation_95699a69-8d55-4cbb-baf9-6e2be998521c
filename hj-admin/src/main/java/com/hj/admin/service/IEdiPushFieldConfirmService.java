package com.hj.admin.service;

import com.hj.admin.domain.EdiPushFieldConfirm;

import java.util.List;

/**
 * Push接口字段确认Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IEdiPushFieldConfirmService 
{
    /**
     * 查询Push接口字段确认
     * 
     * @param id Push接口字段确认主键
     * @return Push接口字段确认
     */
    public EdiPushFieldConfirm selectEdiPushFieldConfirmById(Long id);

    /**
     * 查询Push接口字段确认列表
     * 
     * @param ediPushFieldConfirm Push接口字段确认
     * @return Push接口字段确认集合
     */
    public List<EdiPushFieldConfirm> selectEdiPushFieldConfirmList(EdiPushFieldConfirm ediPushFieldConfirm);

    /**
     * 新增Push接口字段确认
     * 
     * @param ediPushFieldConfirm Push接口字段确认
     * @return 结果
     */
    public int insertEdiPushFieldConfirm(EdiPushFieldConfirm ediPushFieldConfirm);

    /**
     * 修改Push接口字段确认
     * 
     * @param ediPushFieldConfirm Push接口字段确认
     * @return 结果
     */
    public int updateEdiPushFieldConfirm(EdiPushFieldConfirm ediPushFieldConfirm);

    /**
     * 批量删除Push接口字段确认
     * 
     * @param ids 需要删除的Push接口字段确认主键集合
     * @return 结果
     */
    public int deleteEdiPushFieldConfirmByIds(String ids);

    /**
     * 删除Push接口字段确认信息
     * 
     * @param id Push接口字段确认主键
     * @return 结果
     */
    public int deleteEdiPushFieldConfirmById(Long id);

    int confirm(String batchNo, String interfacePath);
}
