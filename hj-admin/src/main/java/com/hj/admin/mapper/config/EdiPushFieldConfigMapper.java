package com.hj.admin.mapper.config;

import com.hj.admin.domain.config.EdiPushFieldConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Push接口字段配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface EdiPushFieldConfigMapper 
{
    /**
     * 查询Push接口字段配置
     * 
     * @param id Push接口字段配置主键
     * @return Push接口字段配置
     */
    public EdiPushFieldConfig selectEdiPushFieldConfigById(Long id);

    /**
     * 查询Push接口字段配置列表
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return Push接口字段配置集合
     */
    public List<EdiPushFieldConfig> selectEdiPushFieldConfigList(EdiPushFieldConfig ediPushFieldConfig);

    /**
     * 新增Push接口字段配置
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return 结果
     */
    public int insertEdiPushFieldConfig(EdiPushFieldConfig ediPushFieldConfig);

    /**
     * 修改Push接口字段配置
     * 
     * @param ediPushFieldConfig Push接口字段配置
     * @return 结果
     */
    public int updateEdiPushFieldConfig(EdiPushFieldConfig ediPushFieldConfig);

    /**
     * 删除Push接口字段配置
     * 
     * @param id Push接口字段配置主键
     * @return 结果
     */
    public int deleteEdiPushFieldConfigById(Long id);

    /**
     * 批量删除Push接口字段配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEdiPushFieldConfigByIds(String[] ids);

    List<EdiPushFieldConfig> selectByInterfacePathAndUser(@Param("interfacePath") String interfacePath, @Param("userId") Long userId);
}
