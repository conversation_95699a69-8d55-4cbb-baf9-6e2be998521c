package com.hj.admin.mapper.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProProcess;

/**
 * 工艺Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProProcessMapper 
{
    /**
     * 查询工艺
     * 
     * @param id 工艺主键
     * @return 工艺
     */
    public SupplierProProcess selectSupplierProProcessById(Long id);

    /**
     * 查询工艺列表
     * 
     * @param supplierProProcess 工艺
     * @return 工艺集合
     */
    public List<SupplierProProcess> selectSupplierProProcessList(SupplierProProcess supplierProProcess);

    /**
     * 新增工艺
     * 
     * @param supplierProProcess 工艺
     * @return 结果
     */
    public int insertSupplierProProcess(SupplierProProcess supplierProProcess);

    /**
     * 修改工艺
     * 
     * @param supplierProProcess 工艺
     * @return 结果
     */
    public int updateSupplierProProcess(SupplierProProcess supplierProProcess);

    /**
     * 删除工艺
     * 
     * @param id 工艺主键
     * @return 结果
     */
    public int deleteSupplierProProcessById(Long id);

    /**
     * 批量删除工艺
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProProcessByIds(String[] ids);

    /**
     * 批量更新工艺
     *
     * @param list 工艺
     * @return 结果
     */
    public int batchUpdate(List<SupplierProProcess> list);

    /**
     * 根据ID批量查询工艺
     *
     * @param ids
     * @return
     */
    public List<SupplierProProcess> selectSupplierProProcessByIds(String batchNo);
}
