package com.hj.admin.mapper.push;

import java.util.List;

import com.hj.admin.domain.push.SupplierBom;

/**
 * BOM主数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierBomMapper 
{
    /**
     * 查询BOM主数据
     * 
     * @param id BOM主数据主键
     * @return BOM主数据
     */
    public SupplierBom selectSupplierBomById(Long id);

    /**
     * 查询BOM主数据列表
     * 
     * @param supplierBom BOM主数据
     * @return BOM主数据集合
     */
    public List<SupplierBom> selectSupplierBomList(SupplierBom supplierBom);

    /**
     * 新增BOM主数据
     * 
     * @param supplierBom BOM主数据
     * @return 结果
     */
    public int insertSupplierBom(SupplierBom supplierBom);

    /**
     * 修改BOM主数据
     * 
     * @param supplierBom BOM主数据
     * @return 结果
     */
    public int updateSupplierBom(SupplierBom supplierBom);

    /**
     * 删除BOM主数据
     * 
     * @param id BOM主数据主键
     * @return 结果
     */
    public int deleteSupplierBomById(Long id);

    /**
     * 批量删除BOM主数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierBomByIds(String[] ids);

    /**
     * 批量更新BOM主数据
     *
     * @param list BOM主数据
     * @return 结果
     */
    public int batchUpdate(List<SupplierBom> list);

    /**
     * 根据ID批量查询BOM主数据
     *
     * @param batchNo 批次号
     * @return
     */
    public List<SupplierBom> selectSupplierBomByIds(String batchNo);

}
