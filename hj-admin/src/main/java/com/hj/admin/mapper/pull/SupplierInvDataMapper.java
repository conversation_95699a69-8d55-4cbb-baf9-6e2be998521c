package com.hj.admin.mapper.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierInvData;

/**
 * 奇瑞RDC共享库存Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierInvDataMapper 
{
    /**
     * 查询奇瑞RDC共享库存
     * 
     * @param id 奇瑞RDC共享库存主键
     * @return 奇瑞RDC共享库存
     */
    public SupplierInvData selectSupplierInvDataById(Long id);

    /**
     * 查询奇瑞RDC共享库存列表
     * 
     * @param supplierInvData 奇瑞RDC共享库存
     * @return 奇瑞RDC共享库存集合
     */
    public List<SupplierInvData> selectSupplierInvDataList(SupplierInvData supplierInvData);

    /**
     * 新增奇瑞RDC共享库存
     * 
     * @param supplierInvData 奇瑞RDC共享库存
     * @return 结果
     */
    public int insertSupplierInvData(SupplierInvData supplierInvData);

    /**
     * 修改奇瑞RDC共享库存
     * 
     * @param supplierInvData 奇瑞RDC共享库存
     * @return 结果
     */
    public int updateSupplierInvData(SupplierInvData supplierInvData);

    /**
     * 删除奇瑞RDC共享库存
     * 
     * @param id 奇瑞RDC共享库存主键
     * @return 结果
     */
    public int deleteSupplierInvDataById(Long id);

    /**
     * 批量删除奇瑞RDC共享库存
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierInvDataByIds(String[] ids);

    /**
     * 批量插入或更新奇瑞RDC共享库存
     * 根据id判断是插入还是更新
     *
     * @param supplierInvDataList 奇瑞RDC共享库存列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<SupplierInvData> supplierInvDataList);
}
