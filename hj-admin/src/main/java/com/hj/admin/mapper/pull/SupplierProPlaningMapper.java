package com.hj.admin.mapper.pull;

import com.hj.admin.domain.pull.SupplierProPlaning;

import java.util.List;

/**
 * 整车月度生产计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProPlaningMapper 
{
    /**
     * 查询整车月度生产计划
     * 
     * @param id 整车月度生产计划主键
     * @return 整车月度生产计划
     */
    public SupplierProPlaning selectSupplierProPlaningById(Long id);

    /**
     * 查询整车月度生产计划列表
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 整车月度生产计划集合
     */
    public List<SupplierProPlaning> selectSupplierProPlaningList(SupplierProPlaning supplierProPlaning);

    /**
     * 新增整车月度生产计划
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 结果
     */
    public int insertSupplierProPlaning(SupplierProPlaning supplierProPlaning);

    /**
     * 修改整车月度生产计划
     * 
     * @param supplierProPlaning 整车月度生产计划
     * @return 结果
     */
    public int updateSupplierProPlaning(SupplierProPlaning supplierProPlaning);

    /**
     * 删除整车月度生产计划
     * 
     * @param id 整车月度生产计划主键
     * @return 结果
     */
    public int deleteSupplierProPlaningById(Long id);

    /**
     * 批量删除整车月度生产计划
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProPlaningByIds(String[] ids);

    /**
     * 批量插入或更新整车月度生产计划
     * 根据id判断是插入还是更新
     *
     * @param supplierProPlaningList 整车月度生产计划列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<SupplierProPlaning> supplierProPlaningList);
}
