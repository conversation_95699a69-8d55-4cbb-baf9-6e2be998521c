package com.hj.admin.mapper.pull;

import java.util.List;
import com.hj.admin.domain.pull.SupplierMrpWarning;

/**
 * 日MRP预警推移Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierMrpWarningMapper 
{
    /**
     * 查询日MRP预警推移
     * 
     * @param id 日MRP预警推移主键
     * @return 日MRP预警推移
     */
    public SupplierMrpWarning selectSupplierMrpWarningById(Long id);

    /**
     * 查询日MRP预警推移列表
     * 
     * @param supplierMrpWarning 日MRP预警推移
     * @return 日MRP预警推移集合
     */
    public List<SupplierMrpWarning> selectSupplierMrpWarningList(SupplierMrpWarning supplierMrpWarning);

    /**
     * 新增日MRP预警推移
     * 
     * @param supplierMrpWarning 日MRP预警推移
     * @return 结果
     */
    public int insertSupplierMrpWarning(SupplierMrpWarning supplierMrpWarning);

    /**
     * 修改日MRP预警推移
     * 
     * @param supplierMrpWarning 日MRP预警推移
     * @return 结果
     */
    public int updateSupplierMrpWarning(SupplierMrpWarning supplierMrpWarning);

    /**
     * 删除日MRP预警推移
     * 
     * @param id 日MRP预警推移主键
     * @return 结果
     */
    public int deleteSupplierMrpWarningById(Long id);

    /**
     * 批量删除日MRP预警推移
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierMrpWarningByIds(String[] ids);

    /**
     * 批量插入或更新日MRP预警推移
     * 根据id判断是插入还是更新
     *
     * @param supplierMrpWarningList 日MRP预警推移列表
     * @return 结果
     */
    public int batchInsertOrUpdate(List<SupplierMrpWarning> supplierMrpWarningList);
}
