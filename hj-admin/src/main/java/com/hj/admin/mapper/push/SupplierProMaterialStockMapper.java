package com.hj.admin.mapper.push;

import com.hj.admin.domain.push.SupplierProMaterialStock;

import java.util.List;

/**
 * 来料检验数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProMaterialStockMapper 
{
    /**
     * 查询来料检验数据
     * 
     * @param id 来料检验数据主键
     * @return 来料检验数据
     */
    public SupplierProMaterialStock selectSupplierProMaterialStockById(Long id);

    /**
     * 查询来料检验数据列表
     * 
     * @param supplierProMaterialStock 来料检验数据
     * @return 来料检验数据集合
     */
    public List<SupplierProMaterialStock> selectSupplierProMaterialStockList(SupplierProMaterialStock supplierProMaterialStock);

    /**
     * 新增来料检验数据
     * 
     * @param supplierProMaterialStock 来料检验数据
     * @return 结果
     */
    public int insertSupplierProMaterialStock(SupplierProMaterialStock supplierProMaterialStock);

    /**
     * 修改来料检验数据
     * 
     * @param supplierProMaterialStock 来料检验数据
     * @return 结果
     */
    public int updateSupplierProMaterialStock(SupplierProMaterialStock supplierProMaterialStock);

    /**
     * 删除来料检验数据
     * 
     * @param id 来料检验数据主键
     * @return 结果
     */
    public int deleteSupplierProMaterialStockById(Long id);

    /**
     * 批量删除来料检验数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProMaterialStockByIds(String[] ids);

    /**
     * 批量更新来料检验数据
     *
     * @param list 来料检验数据
     * @return 结果
     */
    public int batchUpdate(List<SupplierProMaterialStock> list);

    /**
     * 根据ID批量查询来料检验数据
     *
     * @param ids
     * @return
     */
    public List<SupplierProMaterialStock> selectSupplierProMaterialStockByIds(String batchNo);
}
