package com.hj.admin.mapper.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierConPo;

/**
 * 采购订单风险确认Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierConPoMapper 
{
    /**
     * 查询采购订单风险确认
     * 
     * @param id 采购订单风险确认主键
     * @return 采购订单风险确认
     */
    public SupplierConPo selectSupplierConPoById(Long id);

    /**
     * 查询采购订单风险确认列表
     * 
     * @param supplierConPo 采购订单风险确认
     * @return 采购订单风险确认集合
     */
    public List<SupplierConPo> selectSupplierConPoList(SupplierConPo supplierConPo);

    /**
     * 新增采购订单风险确认
     * 
     * @param supplierConPo 采购订单风险确认
     * @return 结果
     */
    public int insertSupplierConPo(SupplierConPo supplierConPo);

    /**
     * 修改采购订单风险确认
     * 
     * @param supplierConPo 采购订单风险确认
     * @return 结果
     */
    public int updateSupplierConPo(SupplierConPo supplierConPo);

    /**
     * 删除采购订单风险确认
     * 
     * @param id 采购订单风险确认主键
     * @return 结果
     */
    public int deleteSupplierConPoById(Long id);

    /**
     * 批量删除采购订单风险确认
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierConPoByIds(String[] ids);

    /**
     * 批量更新采购订单风险确认
     *
     * @param list 采购订单风险确认
     * @return 结果
     */
    public int batchUpdate(List<SupplierConPo> list);

    /**
     * 根据ID批量查询采购订单风险确认
     *
     * @param ids
     * @return
     */
    public List<SupplierConPo> selectSupplierConPoByIds(String batchNo);
}
