package com.hj.admin.mapper.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProProcessEquipment;

/**
 * 工艺装备Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProProcessEquipmentMapper 
{
    /**
     * 查询工艺装备
     * 
     * @param id 工艺装备主键
     * @return 工艺装备
     */
    public SupplierProProcessEquipment selectSupplierProProcessEquipmentById(Long id);

    /**
     * 查询工艺装备列表
     * 
     * @param supplierProProcessEquipment 工艺装备
     * @return 工艺装备集合
     */
    public List<SupplierProProcessEquipment> selectSupplierProProcessEquipmentList(SupplierProProcessEquipment supplierProProcessEquipment);

    /**
     * 新增工艺装备
     * 
     * @param supplierProProcessEquipment 工艺装备
     * @return 结果
     */
    public int insertSupplierProProcessEquipment(SupplierProProcessEquipment supplierProProcessEquipment);

    /**
     * 修改工艺装备
     * 
     * @param supplierProProcessEquipment 工艺装备
     * @return 结果
     */
    public int updateSupplierProProcessEquipment(SupplierProProcessEquipment supplierProProcessEquipment);

    /**
     * 删除工艺装备
     * 
     * @param id 工艺装备主键
     * @return 结果
     */
    public int deleteSupplierProProcessEquipmentById(Long id);

    /**
     * 批量删除工艺装备
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProProcessEquipmentByIds(String[] ids);

    /**
     * 批量更新工艺装备
     *
     * @param list 工艺装备
     * @return 结果
     */
    public int batchUpdate(List<SupplierProProcessEquipment> list);

    /**
     * 根据ID批量查询工艺装备
     *
     * @param ids
     * @return
     */
    public List<SupplierProProcessEquipment> selectSupplierProProcessEquipmentByIds(String batchNo);
}
