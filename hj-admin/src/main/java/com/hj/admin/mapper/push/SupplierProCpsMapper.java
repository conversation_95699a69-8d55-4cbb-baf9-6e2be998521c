package com.hj.admin.mapper.push;

import java.util.List;
import com.hj.admin.domain.push.SupplierProCps;

/**
 * 过程控制项质量数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface SupplierProCpsMapper 
{
    /**
     * 查询过程控制项质量数据
     * 
     * @param id 过程控制项质量数据主键
     * @return 过程控制项质量数据
     */
    public SupplierProCps selectSupplierProCpsById(Long id);

    /**
     * 查询过程控制项质量数据列表
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 过程控制项质量数据集合
     */
    public List<SupplierProCps> selectSupplierProCpsList(SupplierProCps supplierProCps);

    /**
     * 新增过程控制项质量数据
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 结果
     */
    public int insertSupplierProCps(SupplierProCps supplierProCps);

    /**
     * 修改过程控制项质量数据
     * 
     * @param supplierProCps 过程控制项质量数据
     * @return 结果
     */
    public int updateSupplierProCps(SupplierProCps supplierProCps);

    /**
     * 删除过程控制项质量数据
     * 
     * @param id 过程控制项质量数据主键
     * @return 结果
     */
    public int deleteSupplierProCpsById(Long id);

    /**
     * 批量删除过程控制项质量数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierProCpsByIds(String[] ids);

    /**
     * 批量更新过程控制项质量数据
     *
     * @param list 过程控制项质量数据
     * @return 结果
     */
    public int batchUpdate(List<SupplierProCps> list);

    /**
     * 根据ID批量查询过程控制项质量数据
     *
     * @param ids
     * @return
     */
    public List<SupplierProCps> selectSupplierProCpsByIds(String batchNo);
}
