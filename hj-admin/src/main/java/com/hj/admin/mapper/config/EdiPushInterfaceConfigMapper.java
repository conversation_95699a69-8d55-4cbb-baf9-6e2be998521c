package com.hj.admin.mapper.config;

import com.hj.admin.domain.config.EdiPushInterfaceConfig;

import java.util.List;

/**
 * Push接口配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface EdiPushInterfaceConfigMapper 
{
    /**
     * 查询Push接口配置
     * 
     * @param id Push接口配置主键
     * @return Push接口配置
     */
    public EdiPushInterfaceConfig selectEdiPushInterfaceConfigById(Long id);

    /**
     * 查询Push接口配置列表
     * 
     * @param ediPushInterfaceConfig Push接口配置
     * @return Push接口配置集合
     */
    public List<EdiPushInterfaceConfig> selectEdiPushInterfaceConfigList(EdiPushInterfaceConfig ediPushInterfaceConfig);

    /**
     * 新增Push接口配置
     * 
     * @param ediPushInterfaceConfig Push接口配置
     * @return 结果
     */
    public int insertEdiPushInterfaceConfig(EdiPushInterfaceConfig ediPushInterfaceConfig);

    /**
     * 修改Push接口配置
     * 
     * @param ediPushInterfaceConfig Push接口配置
     * @return 结果
     */
    public int updateEdiPushInterfaceConfig(EdiPushInterfaceConfig ediPushInterfaceConfig);

    /**
     * 删除Push接口配置
     * 
     * @param id Push接口配置主键
     * @return 结果
     */
    public int deleteEdiPushInterfaceConfigById(Long id);

    /**
     * 批量删除Push接口配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEdiPushInterfaceConfigByIds(String[] ids);

    EdiPushInterfaceConfig selectOneByInterfacePath(String path);

    /**
     * 根据表名查询接口配置
     *
     * @param tableName 表名
     * @return 接口配置
     */
    EdiPushInterfaceConfig selectByTableName(String tableName);

    List<String> selectInterfaceNameByIds(Long[] ids);
}
