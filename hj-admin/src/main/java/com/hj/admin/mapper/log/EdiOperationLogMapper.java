package com.hj.admin.mapper.log;

import com.hj.admin.domain.log.EdiOperationLog;
import java.util.List;

/**
 * EDI操作记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public interface EdiOperationLogMapper {
    /**
     * 查询EDI操作记录
     * 
     * @param id EDI操作记录主键
     * @return EDI操作记录
     */
    public EdiOperationLog selectEdiOperationLogById(Long id);

    /**
     * 查询EDI操作记录列表
     * 
     * @param ediOperationLog EDI操作记录
     * @return EDI操作记录集合
     */
    public List<EdiOperationLog> selectEdiOperationLogList(EdiOperationLog ediOperationLog);

    /**
     * 新增EDI操作记录
     * 
     * @param ediOperationLog EDI操作记录
     * @return 结果
     */
    public int insertEdiOperationLog(EdiOperationLog ediOperationLog);

    /**
     * 修改EDI操作记录
     * 
     * @param ediOperationLog EDI操作记录
     * @return 结果
     */
    public int updateEdiOperationLog(EdiOperationLog ediOperationLog);

    /**
     * 删除EDI操作记录
     * 
     * @param id EDI操作记录主键
     * @return 结果
     */
    public int deleteEdiOperationLogById(Long id);

    /**
     * 批量删除EDI操作记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEdiOperationLogByIds(String[] ids);

    /**
     * 根据批次号查询操作记录
     * 
     * @param batchNo 批次号
     * @return EDI操作记录
     */
    public EdiOperationLog selectEdiOperationLogByBatchNo(String batchNo);

    /**
     * 根据接口路径和操作类型查询最新记录
     * 
     * @param interfacePath 接口路径
     * @param operationType 操作类型
     * @return EDI操作记录
     */
    public EdiOperationLog selectLatestLogByPathAndType(String interfacePath, String operationType);
}
