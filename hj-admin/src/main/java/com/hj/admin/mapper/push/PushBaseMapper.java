package com.hj.admin.mapper.push;

import com.hj.admin.domain.base.PushBaseDomain;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PushBaseMapper {

    void changeFieldConfirmStatus(@Param("tableName") String tableName, @Param("batchNo") String batchNo);

    void batchUpdate(@Param("tableName") String tableName, @Param("outBatchNo")String outBatchNo, @Param("list") List<Long> list);

    int changeInterfaceConfirmStatus(@Param("batchNo") String batchNo, @Param("tableName") String tableName);

    List<Map<String, Object>> listUnPushData(@Param("tableName") String tableName);
}
