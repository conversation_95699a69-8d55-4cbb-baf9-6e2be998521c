package com.hj.admin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "wx-work")
public class WxWorkConfig {

    private String sendTextMsgUrl;
    private String sendMarkdownMsgUrl;
    private int agentId;
    private int enableIdTrans;
    private int enableDuplicateCheck;
    private int duplicateCheckInterval;
    private int safe;

    public String getSendMarkdownMsgUrl() {
        return sendMarkdownMsgUrl;
    }

    public void setSendMarkdownMsgUrl(String sendMarkdownMsgUrl) {
        this.sendMarkdownMsgUrl = sendMarkdownMsgUrl;
    }

    public String getSendTextMsgUrl() {
        return sendTextMsgUrl;
    }

    public void setSendTextMsgUrl(String sendTextMsgUrl) {
        this.sendTextMsgUrl = sendTextMsgUrl;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public int getEnableIdTrans() {
        return enableIdTrans;
    }

    public void setEnableIdTrans(int enableIdTrans) {
        this.enableIdTrans = enableIdTrans;
    }

    public int getEnableDuplicateCheck() {
        return enableDuplicateCheck;
    }

    public void setEnableDuplicateCheck(int enableDuplicateCheck) {
        this.enableDuplicateCheck = enableDuplicateCheck;
    }

    public int getDuplicateCheckInterval() {
        return duplicateCheckInterval;
    }

    public void setDuplicateCheckInterval(int duplicateCheckInterval) {
        this.duplicateCheckInterval = duplicateCheckInterval;
    }

    public int getSafe() {
        return safe;
    }

    public void setSafe(int safe) {
        this.safe = safe;
    }

}