package com.hj.admin.task;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.hj.admin.config.WxWorkConfig;
import com.hj.admin.domain.config.EdiPushFieldConfig;
import com.hj.admin.domain.config.EdiPushInterfaceConfig;
import com.hj.admin.domain.base.CheryPullRes;
import com.hj.admin.domain.base.CheryPushReq;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.admin.domain.pull.*;
import com.hj.admin.domain.push.*;
import com.hj.admin.domain.wxwork.TextCard;
import com.hj.admin.mapper.push.PushBaseMapper;
import com.hj.admin.service.IDoRequestService;
import com.hj.admin.service.IEdiPushFieldConfigService;
import com.hj.admin.service.IEdiPushInterfaceConfigService;
import com.hj.admin.service.pull.*;
import com.hj.admin.service.push.*;
import com.hj.admin.util.WxUtils;
import com.hj.common.core.domain.entity.SysUser;
import com.hj.common.exception.ServiceException;
import com.hj.common.utils.DateUtils;
import com.hj.common.utils.StringUtils;
import com.hj.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * EDI定时任务执行器
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Component("ediScheduleTask")
public class EdiScheduleTask {
    private static final Logger log = LoggerFactory.getLogger(EdiScheduleTask.class);

    @Resource
    private IDoRequestService doRequestService;

    @Resource
    private ISupplierDelStateService iSupplierDelStateService;

    public void pullSupplierDelState() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierDelState", "供应商交付状态");
        List<SupplierDelState> supplierDelStates = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierDelStates.add((SupplierDelState) baseDomain));
        iSupplierDelStateService.batchInsertOrUpdate(supplierDelStates);
    }

    @Resource
    private ISupplierInvDataService iSupplierInvDataService;

    public void pullSupplierInvData() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierInvData", "供应商库存数据");
        List<SupplierInvData> supplierInvDatas = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierInvDatas.add((SupplierInvData) baseDomain));
        iSupplierInvDataService.batchInsertOrUpdate(supplierInvDatas);
    }

    @Resource
    private ISupplierMrpDateService iSupplierMrpDateService;

    public void pullSupplierMrpDate() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierMrpDate", "日物料需求计划");
        List<SupplierMrpDate> supplierMrpDates = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierMrpDates.add((SupplierMrpDate) baseDomain));
        iSupplierMrpDateService.batchInsertOrUpdate(supplierMrpDates);
    }

    @Resource
    private ISupplierMrpMonthService iSupplierMrpMonthService;

    public void pullSupplierMrpMonth() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierMrpMonth", "月物料需求计划");
        List<SupplierMrpMonth> supplierMrpMonths = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierMrpMonths.add((SupplierMrpMonth) baseDomain));
        iSupplierMrpMonthService.batchInsertOrUpdate(supplierMrpMonths);
    }

    @Resource
    private ISupplierMrpStateService iSupplierMrpStateService;

    public void pullSupplierMrpState() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierMrpState", "物料需求计划状态");
        List<SupplierMrpState> supplierMrpStates = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierMrpStates.add((SupplierMrpState) baseDomain));
        iSupplierMrpStateService.batchInsertOrUpdate(supplierMrpStates);
    }

    @Resource
    private ISupplierMrpWarningService iSupplierMrpWarningService;

    public void pullSupplierMrpWarning() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierMrpWarning", "物料需求计划预警");
        List<SupplierMrpWarning> supplierMrpWarnings = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierMrpWarnings.add((SupplierMrpWarning) baseDomain));
        iSupplierMrpWarningService.batchInsertOrUpdate(supplierMrpWarnings);
    }

    @Resource
    private ISupplierPoService iSupplierPoService;

    public void pullSupplierPo() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierPo", "采购订单");
        List<SupplierPo> supplierPos = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierPos.add((SupplierPo) baseDomain));
        iSupplierPoService.batchInsertOrUpdate(supplierPos);
    }

    @Resource
    private ISupplierProCschedulService iSupplierProCschedulService;

    public void pullSupplierProCschedul() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierProCschedul", "供应商生产排程(C)");
        List<SupplierProCschedul> supplierProCscheduls = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierProCscheduls.add((SupplierProCschedul) baseDomain));
        iSupplierProCschedulService.batchInsertOrUpdate(supplierProCscheduls);
    }

    @Resource
    private ISupplierProHschedulService iSupplierProHschedulService;

    public void pullSupplierProHschedul() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierProHschedul", "供应商生产排程(H)");
        List<SupplierProHschedul> supplierProHscheduls = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierProHscheduls.add((SupplierProHschedul) baseDomain));
        iSupplierProHschedulService.batchInsertOrUpdate(supplierProHscheduls);
    }

    @Resource
    private ISupplierProPlaningService iSupplierProPlaningService;

    public void pullSupplierProPlaning() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierProPlaning", "供应商生产计划");
        List<SupplierProPlaning> supplierProPlanings = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierProPlanings.add((SupplierProPlaning) baseDomain));
        iSupplierProPlaningService.batchInsertOrUpdate(supplierProPlanings);
    }

    @Resource
    private ISupplierProTschedulService iSupplierProTschedulService;

    public void pullSupplierProTschedul() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierProTschedul", "供应商生产排程(T)");
        List<SupplierProTschedul> supplierProTscheduls = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierProTscheduls.add((SupplierProTschedul) baseDomain));
        iSupplierProTschedulService.batchInsertOrUpdate(supplierProTscheduls);
    }

    @Resource
    private ISupplierReturnService iSupplierReturnService;

    public void pullSupplierReturn() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierReturn", "退货单");
        List<SupplierReturn> supplierReturns = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierReturns.add((SupplierReturn) baseDomain));
        iSupplierReturnService.batchInsertOrUpdate(supplierReturns);
    }

    @Resource
    private ISupplierSaWeekService iSupplierSaWeekService;

    public void pullSupplierSaWeek() throws Exception {
        CheryPullRes cheryPullRes = executePullTask("/v2/get/supplierSaWeek", "供应商销售预测周");
        List<SupplierSaWeek> supplierSaWeeks = new ArrayList<>();
        cheryPullRes.getData().getRows().forEach(baseDomain -> supplierSaWeeks.add((SupplierSaWeek) baseDomain));
        iSupplierSaWeekService.batchInsertOrUpdate(supplierSaWeeks);
    }

    private CheryPullRes executePullTask(String interfacePath, String interfaceName) throws Exception {
        Map<String, Object> params = createPullParams();
        try {
            CheryPullRes cheryPullRes = (CheryPullRes) doRequestService.pull(interfacePath, interfaceName, params);
            log.info("Pull操作执行成功: {}", interfaceName);
            return cheryPullRes;
        } catch (Exception e) {
            log.error("Pull操作执行失败: {}, 错误: {}", interfaceName, e.getMessage());
            throw e;
        }
    }

    /**
     * 创建Pull请求参数
     *
     * @return 参数Map
     */
    private Map<String, Object> createPullParams() {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("date", DateUtils.dateTime(new Date()));
        params.put("pageSize", 1000);
        params.put("pageNum", 1);
        params.put("isForce", false);
        return params;
    }


// ---------------------------------------------------push--------------------------------------------------------------

    @Resource
    private PushBaseMapper pushBaseMapper;

    @Resource
    private ISupplierBomService iSupplierBomService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierBom() {
        List<SupplierBom> list = iSupplierBomService.selectSupplierBomList(new SupplierBom(false)).stream().filter(supplierBom -> judgeInvalid(supplierBom.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierBom(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierBom(String batchNo) {
        List<SupplierBom> list = iSupplierBomService.selectSupplierBomByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierBom -> !supplierBom.getFieldHasConfirm() || !supplierBom.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierBom -> supplierBom.setFunctionHasConfirm(true));
        pushSupplierBom(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierBom(List<SupplierBom> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_bom", batchNo, list.stream().map(SupplierBom::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierBom", "BOM主数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierConDateService iSupplierConDateService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierConDate() {
        List<SupplierConDate> list = iSupplierConDateService.selectSupplierConDateList(new SupplierConDate(false)).stream().filter(supplierConDate -> judgeInvalid(supplierConDate.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierConDate(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierConDate(String batchNo) {
        List<SupplierConDate> list = iSupplierConDateService.selectSupplierConDateByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierConDate -> !supplierConDate.getFieldHasConfirm() || !supplierConDate.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierConDate -> supplierConDate.setFunctionHasConfirm(true));
        pushSupplierConDate(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierConDate(List<SupplierConDate> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_con_date", batchNo, list.stream().map(SupplierConDate::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierConDate", "日物料需求计划风险确认", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierConMmrpService iSupplierConMmrpService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierConMmrp() {
        List<SupplierConMmrp> list = iSupplierConMmrpService.selectSupplierConMmrpList(new SupplierConMmrp(false)).stream().filter(supplierConMmrp -> judgeInvalid(supplierConMmrp.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierConMmrp(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierConMmrp(String batchNo) {
        List<SupplierConMmrp> list = iSupplierConMmrpService.selectSupplierConMmrpByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierConMmrp -> !supplierConMmrp.getFieldHasConfirm() || !supplierConMmrp.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierConMmrp -> supplierConMmrp.setFunctionHasConfirm(true));
        pushSupplierConMmrp(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierConMmrp(List<SupplierConMmrp> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_con_mmrp", batchNo, list.stream().map(SupplierConMmrp::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierConMmrp", "M+6月物料需求计划风险确认", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierConPoService iSupplierConPoService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierConPo() {
        List<SupplierConPo> list = iSupplierConPoService.selectSupplierConPoList(new SupplierConPo(false)).stream().filter(supplierConPo -> judgeInvalid(supplierConPo.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierConPo(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierConPo(String batchNo) {
        List<SupplierConPo> list = iSupplierConPoService.selectSupplierConPoByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierConPo -> !supplierConPo.getFieldHasConfirm() || !supplierConPo.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierConPo -> supplierConPo.setFunctionHasConfirm(true));
        pushSupplierConPo(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierConPo(List<SupplierConPo> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_con_po", batchNo, list.stream().map(SupplierConPo::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierConPo", "采购订单风险确认", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierEmployeeService iSupplierEmployeeService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierEmployee() {
        List<SupplierEmployee> list = iSupplierEmployeeService.selectSupplierEmployeeList(new SupplierEmployee(false)).stream().filter(supplierEmployee -> judgeInvalid(supplierEmployee.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierEmployee(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierEmployee(String batchNo) {
        List<SupplierEmployee> list = iSupplierEmployeeService.selectSupplierEmployeeByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierEmployee -> !supplierEmployee.getFieldHasConfirm() || !supplierEmployee.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierEmployee -> supplierEmployee.setFunctionHasConfirm(true));
        pushSupplierEmployee(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierEmployee(List<SupplierEmployee> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_employee", batchNo, list.stream().map(SupplierEmployee::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierEmployee", "人员资质信息", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierInfoService iSupplierInfoService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierInfo() {
        List<SupplierInfo> list = iSupplierInfoService.selectSupplierInfoList(new SupplierInfo(false)).stream().filter(supplierInfo -> judgeInvalid(supplierInfo.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierInfo(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierInfo(String batchNo) {
        List<SupplierInfo> list = iSupplierInfoService.selectSupplierInfoByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierInfo -> !supplierInfo.getFieldHasConfirm() || !supplierInfo.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierInfo -> supplierInfo.setFunctionHasConfirm(true));
        pushSupplierInfo(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierInfo(List<SupplierInfo> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_info", batchNo, list.stream().map(SupplierInfo::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierInfo", "供应商基础信息", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProAttachmentDataService iSupplierProAttachmentDataService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProAttachmentData() {
        List<SupplierProAttachmentData> list = iSupplierProAttachmentDataService.selectSupplierProAttachmentDataList(new SupplierProAttachmentData(false)).stream().filter(supplierProAttachmentData -> judgeInvalid(supplierProAttachmentData.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProAttachmentData(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProAttachmentData(String batchNo) {
        List<SupplierProAttachmentData> list = iSupplierProAttachmentDataService.selectSupplierProAttachmentDataByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProAttachmentData -> !supplierProAttachmentData.getFieldHasConfirm() || !supplierProAttachmentData.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProAttachmentData -> supplierProAttachmentData.setFunctionHasConfirm(true));
        pushSupplierProAttachmentData(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProAttachmentData(List<SupplierProAttachmentData> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_attachment_data", batchNo, list.stream().map(SupplierProAttachmentData::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProAttachmentData", "附件类数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProCpsService iSupplierProCpsService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProCps() {
        List<SupplierProCps> list = iSupplierProCpsService.selectSupplierProCpsList(new SupplierProCps(false)).stream().filter(supplierProCps -> judgeInvalid(supplierProCps.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProCps(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProCps(String batchNo) {
        List<SupplierProCps> list = iSupplierProCpsService.selectSupplierProCpsByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProCps -> !supplierProCps.getFieldHasConfirm() || !supplierProCps.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProCps -> supplierProCps.setFunctionHasConfirm(true));
        pushSupplierProCps(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProCps(List<SupplierProCps> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_cps", batchNo, list.stream().map(SupplierProCps::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProCps", "过程控制项质量数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProDataService iSupplierProDataService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProData() {
        List<SupplierProData> list = iSupplierProDataService.selectSupplierProDataList(new SupplierProData(false)).stream().filter(supplierProData -> judgeInvalid(supplierProData.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProData(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProData(String batchNo) {
        List<SupplierProData> list = iSupplierProDataService.selectSupplierProDataByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProData -> !supplierProData.getFieldHasConfirm() || !supplierProData.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProData -> supplierProData.setFunctionHasConfirm(true));
        pushSupplierProData(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProData(List<SupplierProData> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_data", batchNo, list.stream().map(SupplierProData::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProData", "生产过程数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProEnvironmentService iSupplierProEnvironmentService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProEnvironment() {
        List<SupplierProEnvironment> list = iSupplierProEnvironmentService.selectSupplierProEnvironmentList(new SupplierProEnvironment(false)).stream().filter(supplierProEnvironment -> judgeInvalid(supplierProEnvironment.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProEnvironment(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProEnvironment(String batchNo) {
        List<SupplierProEnvironment> list = iSupplierProEnvironmentService.selectSupplierProEnvironmentByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProEnvironment -> !supplierProEnvironment.getFieldHasConfirm() || !supplierProEnvironment.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProEnvironment -> supplierProEnvironment.setFunctionHasConfirm(true));
        pushSupplierProEnvironment(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProEnvironment(List<SupplierProEnvironment> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_environment", batchNo, list.stream().map(SupplierProEnvironment::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProEnvironment", "环境业务数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProFirstPassyieldService iSupplierProFirstPassyieldService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProFirstPassyield() {
        List<SupplierProFirstPassyield> list = iSupplierProFirstPassyieldService.selectSupplierProFirstPassyieldList(new SupplierProFirstPassyield(false)).stream().filter(supplierProFirstPassyield -> judgeInvalid(supplierProFirstPassyield.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProFirstPassyield(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProFirstPassyield(String batchNo) {
        List<SupplierProFirstPassyield> list = iSupplierProFirstPassyieldService.selectSupplierProFirstPassyieldByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProFirstPassyield -> !supplierProFirstPassyield.getFieldHasConfirm() || !supplierProFirstPassyield.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProFirstPassyield -> supplierProFirstPassyield.setFunctionHasConfirm(true));
        pushSupplierProFirstPassyield(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProFirstPassyield(List<SupplierProFirstPassyield> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_first_passyield", batchNo, list.stream().map(SupplierProFirstPassyield::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProFirstPassyield", "产品一次合格率", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProFlawService iSupplierProFlawService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProFlaw() {
        List<SupplierProFlaw> list = iSupplierProFlawService.selectSupplierProFlawList(new SupplierProFlaw(false)).stream().filter(supplierProFlaw -> judgeInvalid(supplierProFlaw.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProFlaw(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProFlaw(String batchNo) {
        List<SupplierProFlaw> list = iSupplierProFlawService.selectSupplierProFlawByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProFlaw -> !supplierProFlaw.getFieldHasConfirm() || !supplierProFlaw.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProFlaw -> supplierProFlaw.setFunctionHasConfirm(true));
        pushSupplierProFlaw(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProFlaw(List<SupplierProFlaw> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_flaw", batchNo, list.stream().map(SupplierProFlaw::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProFlaw", "缺陷业务数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProMaterialDataService iSupplierProMaterialDataService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProMaterialData() {
        List<SupplierProMaterialData> list = iSupplierProMaterialDataService.selectSupplierProMaterialDataList(new SupplierProMaterialData(false)).stream().filter(supplierProMaterialData -> judgeInvalid(supplierProMaterialData.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProMaterialData(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProMaterialData(String batchNo) {
        List<SupplierProMaterialData> list = iSupplierProMaterialDataService.selectSupplierProMaterialDataByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProMaterialData -> !supplierProMaterialData.getFieldHasConfirm() || !supplierProMaterialData.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProMaterialData -> supplierProMaterialData.setFunctionHasConfirm(true));
        pushSupplierProMaterialData(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProMaterialData(List<SupplierProMaterialData> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_material_data", batchNo, list.stream().map(SupplierProMaterialData::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProMaterialData", "物料主数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProMaterialStockService iSupplierProMaterialStockService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProMaterialStock() {
        List<SupplierProMaterialStock> list = iSupplierProMaterialStockService.selectSupplierProMaterialStockList(new SupplierProMaterialStock(false)).stream().filter(supplierProMaterialStock -> judgeInvalid(supplierProMaterialStock.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProMaterialStock(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProMaterialStock(String batchNo) {
        List<SupplierProMaterialStock> list = iSupplierProMaterialStockService.selectSupplierProMaterialStockByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProMaterialStock -> !supplierProMaterialStock.getFieldHasConfirm() || !supplierProMaterialStock.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProMaterialStock -> supplierProMaterialStock.setFunctionHasConfirm(true));
        pushSupplierProMaterialStock(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProMaterialStock(List<SupplierProMaterialStock> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        pushBaseMapper.batchUpdate("supplier_pro_material_stock", batchNo, pushBaseDomains.stream().map(PushBaseDomain::getId).collect(Collectors.toList()));
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProMaterialStock", "来料检验数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProOeeAchievementRateService iSupplierProOeeAchievementRateService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProOeeAchievementRate() {
        List<SupplierProOeeAchievementRate> list = iSupplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateList(new SupplierProOeeAchievementRate(false)).stream().filter(supplierProOeeAchievementRate -> judgeInvalid(supplierProOeeAchievementRate.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProOeeAchievementRate(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProOeeAchievementRate(String batchNo) {
        List<SupplierProOeeAchievementRate> list = iSupplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProOeeAchievementRate -> !supplierProOeeAchievementRate.getFieldHasConfirm() || !supplierProOeeAchievementRate.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProOeeAchievementRate -> supplierProOeeAchievementRate.setFunctionHasConfirm(true));
        pushSupplierProOeeAchievementRate(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProOeeAchievementRate(List<SupplierProOeeAchievementRate> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_oee_achievement_rate", batchNo, list.stream().map(SupplierProOeeAchievementRate::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProOeeAchievementRate", "设备OEE达成率", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProOeeTimeDetailsService iSupplierProOeeTimeDetailsService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProOeeTimeDetails() {
        List<SupplierProOeeTimeDetails> list = iSupplierProOeeTimeDetailsService.selectSupplierProOeeTimeDetailsList(new SupplierProOeeTimeDetails(false)).stream().filter(supplierProOeeTimeDetails -> judgeInvalid(supplierProOeeTimeDetails.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProOeeTimeDetails(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProOeeTimeDetails(String batchNo) {
        List<SupplierProOeeTimeDetails> list = iSupplierProOeeTimeDetailsService.selectSupplierProOeeTimeDetailsByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProOeeTimeDetails -> !supplierProOeeTimeDetails.getFieldHasConfirm() || !supplierProOeeTimeDetails.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProOeeTimeDetails -> supplierProOeeTimeDetails.setFunctionHasConfirm(true));
        pushSupplierProOeeTimeDetails(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProOeeTimeDetails(List<SupplierProOeeTimeDetails> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_oee_time_details", batchNo, list.stream().map(SupplierProOeeTimeDetails::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProOeeTimeDetails", "设备OEE时间明细", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProProcessService iSupplierProProcessService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProProcess() {
        List<SupplierProProcess> list = iSupplierProProcessService.selectSupplierProProcessList(new SupplierProProcess(false)).stream().filter(supplierProProcess -> judgeInvalid(supplierProProcess.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProProcess(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProProcess(String batchNo) {
        List<SupplierProProcess> list = iSupplierProProcessService.selectSupplierProProcessByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProProcess -> !supplierProProcess.getFieldHasConfirm() || !supplierProProcess.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProProcess -> supplierProProcess.setFunctionHasConfirm(true));
        pushSupplierProProcess(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProProcess(List<SupplierProProcess> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_process", batchNo, list.stream().map(SupplierProProcess::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProProcess", "工艺", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProProcessEquipmentService isupplierProProcessEquipmentService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProProcessEquipment() {
        List<SupplierProProcessEquipment> list = isupplierProProcessEquipmentService.selectSupplierProProcessEquipmentList(new SupplierProProcessEquipment(false)).stream().filter(supplierProProcessEquipment -> judgeInvalid(supplierProProcessEquipment.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProProcessEquipment(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProProcessEquipment(String batchNo) {
        List<SupplierProProcessEquipment> list = isupplierProProcessEquipmentService.selectSupplierProProcessEquipmentByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProProcessEquipment -> !supplierProProcessEquipment.getFieldHasConfirm() || !supplierProProcessEquipment.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProProcessEquipment -> supplierProProcessEquipment.setFunctionHasConfirm(true));
        pushSupplierProProcessEquipment(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProProcessEquipment(List<SupplierProProcessEquipment> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_process_equipment", batchNo, list.stream().map(SupplierProProcessEquipment::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProProcessEquipment", "工艺装备", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProSchedulingService iSupplierProSchedulingService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProScheduling() {
        List<SupplierProScheduling> list = iSupplierProSchedulingService.selectSupplierProSchedulingList(new SupplierProScheduling(false)).stream().filter(supplierProScheduling -> judgeInvalid(supplierProScheduling.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProScheduling(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProScheduling(String batchNo) {
        List<SupplierProScheduling> list = iSupplierProSchedulingService.selectSupplierProSchedulingByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProScheduling -> !supplierProScheduling.getFieldHasConfirm() || !supplierProScheduling.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProScheduling -> supplierProScheduling.setFunctionHasConfirm(true));
        pushSupplierProScheduling(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProScheduling(List<SupplierProScheduling> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_scheduling", batchNo, list.stream().map(SupplierProScheduling::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProScheduling", "排产数据", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierProStationFirstPassyieldService iSupplierProStationFirstPassyieldService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProStationFirstPassyield() {
        List<SupplierProStationFirstPassyield> list = iSupplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldList(new SupplierProStationFirstPassyield(false)).stream().filter(supplierProStationFirstPassyield -> judgeInvalid(supplierProStationFirstPassyield.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierProStationFirstPassyield(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierProStationFirstPassyield(String batchNo) {
        List<SupplierProStationFirstPassyield> list = iSupplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierProStationFirstPassyield -> !supplierProStationFirstPassyield.getFieldHasConfirm() || !supplierProStationFirstPassyield.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierProStationFirstPassyield -> supplierProStationFirstPassyield.setFunctionHasConfirm(true));
        pushSupplierProStationFirstPassyield(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierProStationFirstPassyield(List<SupplierProStationFirstPassyield> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_pro_station_first_passyield", batchNo, list.stream().map(SupplierProStationFirstPassyield::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierProStationFirstPassyield", "工位一次合格率", cheryPushReq, directlyPush);
    }

    @Resource
    private ISupplierSinvDataService iSupplierSinvDataService;

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierSinvData() {
        List<SupplierSinvData> list = iSupplierSinvDataService.selectSupplierSinvDataList(new SupplierSinvData(false)).stream().filter(supplierSinvData -> judgeInvalid(supplierSinvData.getOutBatchNo())).collect(Collectors.toList());
        pushSupplierSinvData(list, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void directlyPushSupplierSinvData(String batchNo) {
        List<SupplierSinvData> list = iSupplierSinvDataService.selectSupplierSinvDataByIds(batchNo);
        // 校验数据是否存在未确认的情况
        boolean hasUnConfirm = list.stream().anyMatch(supplierSinvData -> !supplierSinvData.getFieldHasConfirm() || !supplierSinvData.getInterfaceHasConfirm());
        if (hasUnConfirm) {
            throw new ServiceException("存在未确认的数据，无法推送");
        }
        list.forEach(supplierSinvData -> supplierSinvData.setFunctionHasConfirm(true));
        pushSupplierSinvData(list, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pushSupplierSinvData(List<SupplierSinvData> list, Boolean directlyPush) {
        if(list.isEmpty()){
            return;
        }
        String batchNo = getBatchNo();
        pushBaseMapper.batchUpdate("supplier_sinv_data", batchNo, list.stream().map(SupplierSinvData::getId).collect(Collectors.toList()));
        List<PushBaseDomain> pushBaseDomains = new ArrayList<>(list);
        CheryPushReq cheryPushReq = new CheryPushReq();
        cheryPushReq.setBatchNo(batchNo);
        cheryPushReq.setList(pushBaseDomains);
        executePushTask("/v2/push/supplierSinvData", "供应商共享库存", cheryPushReq, directlyPush);
    }


    private String getBatchNo() {
        return "HJ_EDI_" + DateUtils.dateTimeNow("yyyyMMddHHmmss") + "_" + IdUtil.fastSimpleUUID().substring(0, 8);
    }

    @Resource
    private IEdiPushInterfaceConfigService ediPushInterfaceConfigService;
    @Resource
    private IEdiPushFieldConfigService ediPushFieldConfigService;
    @Resource
    private WxWorkConfig wxWorkConfig;
    @Resource
    private WxUtils wxUtils;
    @Resource
    private ISysUserService iSysUserService;

    private void executePushTask(String interfacePath, String interfaceName, CheryPushReq cheryPushReq, Boolean directlyPush) {
        List<PushBaseDomain> pushBaseDomains = cheryPushReq.getList();
        //1.判断该接口是否是自动推送，否则找出需要推送消息下三级维护人
        if (!directlyPush) {
            EdiPushInterfaceConfig ediPushInterfaceConfig = ediPushInterfaceConfigService.selectOneByInterfacePath(interfacePath);
            if (null != ediPushInterfaceConfig) {
                Date now = new Date();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
                //2.找出所有待维护字段数据的字段维护人，推送企业微信
                EdiPushFieldConfig ediPushFieldConfig = new EdiPushFieldConfig();
                ediPushFieldConfig.setInterfaceConfigId(ediPushInterfaceConfig.getId());
                List<EdiPushFieldConfig> ediPushFieldConfigs = ediPushFieldConfigService.selectEdiPushFieldConfigList(ediPushFieldConfig);
                List<PushBaseDomain> needConfirmFields = pushBaseDomains.stream().filter(domain -> !domain.getFieldHasConfirm()).collect(Collectors.toList());
                if (!ediPushFieldConfigs.isEmpty() && !needConfirmFields.isEmpty()) {
                    pushBaseDomains = pushBaseDomains.stream().filter(PushBaseDomain::getFieldHasConfirm).collect(Collectors.toList());
                    Set<Long> fieldMaintainerIds = new HashSet<>();
                    ediPushFieldConfigs.forEach(fieldConfig -> fieldMaintainerIds.add(fieldConfig.getFieldMaintainerId()));
                    Set<String> existWxWorkIds = new HashSet<>();
                    fieldMaintainerIds.forEach(fieldMaintainerId -> {
                        SysUser sysUser = iSysUserService.selectUserById(fieldMaintainerId);
                        if (null != sysUser) {
                            if (StringUtils.isNotEmpty(sysUser.getWxWorkId())) {
                                existWxWorkIds.add(sysUser.getWxWorkId());
                            } else {
                                log.error("字段维护者：{}未配置企业微信Id", fieldMaintainerId);
                            }
                        }else {
                            log.error("字段维护者：{}不存在", fieldMaintainerId);
                        }
                    });
                    String toUser = String.join("|", existWxWorkIds);
                    sendWxWorkMsg(toUser, now, dateFormat, interfaceName);
                }

                //3.若存在未进行接口确认的数据，推送企业微信通知接口维护人
                List<PushBaseDomain> needConfireInterface = pushBaseDomains.stream().filter(domain -> !domain.getInterfaceHasConfirm()).collect(Collectors.toList());
                if (null != ediPushInterfaceConfig.getInterfaceMaintainerId() && !needConfireInterface.isEmpty()) {
                    pushBaseDomains = pushBaseDomains.stream().filter(PushBaseDomain::getInterfaceHasConfirm).collect(Collectors.toList());
                    SysUser sysUser = iSysUserService.selectUserById(ediPushInterfaceConfig.getInterfaceMaintainerId());
                    if(null != sysUser){
                        if(StringUtils.isNotEmpty(sysUser.getWxWorkId())){
                            sendWxWorkMsg(sysUser.getWxWorkId(), now, dateFormat, interfaceName);
                        }else {
                            log.error("接口维护者：{}未配置企业微信Id", ediPushInterfaceConfig.getInterfaceMaintainerId());
                        }
                    }else {
                        log.error("接口维护者：{}不存在", ediPushInterfaceConfig.getInterfaceMaintainerId());
                    }
                }

                //4.若存在未进行功能确认的数据，推送企业微信通知功能维护人
                List<PushBaseDomain> needConfirmFunction = pushBaseDomains.stream().filter(domain -> !domain.getFieldHasConfirm()).collect(Collectors.toList());
                if (null != ediPushInterfaceConfig.getFunctionMaintainerId() && !needConfirmFunction.isEmpty()) {
                    pushBaseDomains = pushBaseDomains.stream().filter(PushBaseDomain::getFunctionHasConfirm).collect(Collectors.toList());
                    SysUser sysUser = iSysUserService.selectUserById(ediPushInterfaceConfig.getFunctionMaintainerId());
                    if(null != sysUser){
                        if(StringUtils.isNotEmpty(sysUser.getWxWorkId())){
                            sendWxWorkMsg(sysUser.getWxWorkId(), now, dateFormat, interfaceName);
                        }else {
                            log.error("功能维护者：{}未配置企业微信Id", ediPushInterfaceConfig.getFunctionMaintainerId());
                        }
                    }else {
                        log.error("功能维护者：{}不存在", ediPushInterfaceConfig.getFunctionMaintainerId());
                    }
                }

                cheryPushReq.setList(pushBaseDomains);
            }
        }
        if (pushBaseDomains.isEmpty()) {
            return;
        }
        //推送不需要确认的数据，或已确认的数据
        try {
            cheryPushReq.setTotal(pushBaseDomains.size());
            cheryPushReq.setPageSize(pushBaseDomains.size());
            cheryPushReq.setPageNum(1);
            doRequestService.push(interfacePath, interfaceName, cheryPushReq);
            log.info("Push操作执行成功: {}", interfaceName);
        } catch (Exception e) {
            log.error("Push操作执行失败: {}, 错误: {}", interfaceName, e.getMessage());
        }
    }

    private void sendWxWorkMsg(String toUser, Date now, SimpleDateFormat dateFormat, String interfaceName) {
        String content = "<div class=\"gray\">" + dateFormat.format(now) + "</div> <div class=\"highlight\">" + interfaceName + "接口有待确认的数据，请知悉！</div>";
        TextCard textCard = new TextCard(toUser, null, null, "宏景EDI系统通知", content, "", wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
        wxUtils.sendWxWorkMsg(wxWorkConfig.getSendTextMsgUrl(), JSONUtil.toJsonStr(textCard));
    }

    private boolean judgeInvalid(String batchNo) {
        return StringUtils.isNotBlank(batchNo);
    }

}
