package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 日物料需求计划风险确认对象 supplier_con_date
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierConDate extends PushBaseDomain {

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 需求发布版次 , 取自M+6月物料需求计划接口中的需求发布版次，针对与这个版次的需求进行风险反馈
     */
    @Excel(name = "需求发布版次 , 取自M+6月物料需求计划接口中的需求发布版次，针对与这个版次的需求进行风险反馈")
    private String releaseEdition;

    /**
     * 零件号 , 奇瑞零件号
     */
    @Excel(name = "零件号 , 奇瑞零件号")
    private String materialCode;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 反馈结果 , 1-异常；0-无异常（匹配峰值需求缺口，如果可满足峰值，即选择无异常）
     */
    @Excel(name = "反馈结果 , 1-异常；0-无异常", readConverterExp = "匹=配峰值需求缺口，如果可满足峰值，即选择无异常")
    private String feedbackResults;

    /**
     * 风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他
     */
    @Excel(name = "风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他")
    private String ventureType;

    /**
     * 具体风险 , 当反馈结果=1时，此字段必输 描述具体风险
     */
    @Excel(name = "具体风险 , 当反馈结果=1时，此字段必输 描述具体风险")
    private String ventureSpecific;

    /**
     * 应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施
     */
    @Excel(name = "应对措施 , 当反馈结果=1时，此字段必输 描述具体应对措施")
    private String measures;

    /**
     * 起始月份-格式：yyyy-MM
     */
    @Excel(name = "起始月份-格式：yyyy-MM")
    private String startMonth;

    /**
     * 满足数量1, 数量锁定，7日内满足数量必须等于需求数量
     */
    @Excel(name = "满足数量1, 数量锁定，7日内满足数量必须等于需求数量")
    private Long quantityMeet1;

    /**
     * 满足数量2
     */
    @Excel(name = "满足数量2")
    private Long quantityMeet2;

    /**
     * 满足数量3
     */
    @Excel(name = "满足数量3")
    private Long quantityMeet3;

    /**
     * 满足数量4
     */
    @Excel(name = "满足数量4")
    private Long quantityMeet4;

    /**
     * 满足数量5
     */
    @Excel(name = "满足数量5")
    private Long quantityMeet5;

    /**
     * 满足数量6
     */
    @Excel(name = "满足数量6")
    private Long quantityMeet6;

    /**
     * 满足数量7
     */
    @Excel(name = "满足数量7")
    private Long quantityMeet7;

    /**
     * 满足数量8 , 根据物料需求计划接口发布的需求天数，针对企业自身产能情况反馈可满足数量 注：目前发送数据为滚动12天的数据，13至31天的字段为预留，未来可能会增加至31天
     */
    @Excel(name = "满足数量8 , 根据物料需求计划接口发布的需求天数，针对企业自身产能情况反馈可满足数量 注：目前发送数据为滚动12天的数据，13至31天的字段为预留，未来可能会增加至31天")
    private Long quantityMeet8;

    /**
     * 满足数量9
     */
    @Excel(name = "满足数量9")
    private Long quantityMeet9;

    /**
     * 满足数量10
     */
    @Excel(name = "满足数量10")
    private Long quantityMeet10;

    /**
     * 满足数量11
     */
    @Excel(name = "满足数量11")
    private Long quantityMeet11;

    /**
     * 满足数量12
     */
    @Excel(name = "满足数量12")
    private Long quantityMeet12;

    /**
     * 满足数量13
     */
    @Excel(name = "满足数量13")
    private Long quantityMeet13;

    /**
     * 满足数量14
     */
    @Excel(name = "满足数量14")
    private Long quantityMeet14;

    /**
     * 满足数量15
     */
    @Excel(name = "满足数量15")
    private Long quantityMeet15;

    /**
     * 满足数量16
     */
    @Excel(name = "满足数量16")
    private Long quantityMeet16;

    /**
     * 满足数量17
     */
    @Excel(name = "满足数量17")
    private Long quantityMeet17;

    /**
     * 满足数量18
     */
    @Excel(name = "满足数量18")
    private Long quantityMeet18;

    /**
     * 满足数量19
     */
    @Excel(name = "满足数量19")
    private Long quantityMeet19;

    /**
     * 满足数量20
     */
    @Excel(name = "满足数量20")
    private Long quantityMeet20;

    /**
     * 满足数量21
     */
    @Excel(name = "满足数量21")
    private Long quantityMeet21;

    /**
     * 满足数量22
     */
    @Excel(name = "满足数量22")
    private Long quantityMeet22;

    /**
     * 满足数量23
     */
    @Excel(name = "满足数量23")
    private Long quantityMeet23;

    /**
     * 满足数量24
     */
    @Excel(name = "满足数量24")
    private Long quantityMeet24;

    /**
     * 满足数量25
     */
    @Excel(name = "满足数量25")
    private Long quantityMeet25;

    /**
     * 满足数量26
     */
    @Excel(name = "满足数量26")
    private Long quantityMeet26;

    /**
     * 满足数量27
     */
    @Excel(name = "满足数量27")
    private Long quantityMeet27;

    /**
     * 满足数量28
     */
    @Excel(name = "满足数量28")
    private Long quantityMeet28;

    /**
     * 满足数量29
     */
    @Excel(name = "满足数量29")
    private Long quantityMeet29;

    /**
     * 满足数量30
     */
    @Excel(name = "满足数量30")
    private Long quantityMeet30;

    /**
     * 满足数量31
     */
    @Excel(name = "满足数量31")
    private Long quantityMeet31;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    @JsonIgnore
    private Boolean hasPush;

    public SupplierConDate(Boolean hasPush) {
        this.hasPush = hasPush;
    }

}
