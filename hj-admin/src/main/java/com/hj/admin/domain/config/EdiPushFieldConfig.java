package com.hj.admin.domain.config;

import com.hj.common.annotation.Excel;
import com.hj.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Push接口字段配置对象 edi_push_field_config
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EdiPushFieldConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 接口配置ID */
    @Excel(name = "接口配置ID")
    private Long interfaceConfigId;

    /** 接口配置名称 */
    @Excel(name = "接口配置名称")
    private String interfaceConfigName;

    /** 数据库字段名 */
    @Excel(name = "数据库字段名")
    private String columnName;

    /** 字段维护者ID */
    @Excel(name = "字段维护者ID")
    private Long fieldMaintainerId;

    /** 字段维护者姓名 */
    @Excel(name = "字段维护者姓名")
    private String fieldMaintainerName;

}
