package com.hj.admin.domain.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class BaseDomain implements Serializable {

    /**
     * 创建者
     */
    @JsonIgnore
    private String insertBy;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date insertTime;

    /**
     * 更新者
     */
    @JsonIgnore
    private String modifyBy;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date modifyTime;

}
