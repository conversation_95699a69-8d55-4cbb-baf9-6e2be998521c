package com.hj.admin.domain.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CheryPullRes extends CheryBaseRes {

    private DataDetailRes data;

    @Data
    public static class DataDetailRes {
        private String total;
        private String pageNum;
        private String pageSize;
        private List<BaseDomain> rows;
    }

}

