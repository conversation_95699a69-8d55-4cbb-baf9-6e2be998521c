package com.hj.admin.domain;

import com.hj.common.annotation.Excel;
import com.hj.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Push接口字段确认对象 edi_push_field_confirm
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EdiPushFieldConfirm extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 字段维护者ID */
    @Excel(name = "字段维护者ID")
    private Long fieldMaintainerId;

}
