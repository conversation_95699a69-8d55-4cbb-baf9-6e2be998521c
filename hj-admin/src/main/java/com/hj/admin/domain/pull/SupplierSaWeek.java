package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 计划协议对象 supplier_sa_week
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierSaWeek extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 计划协议号
     */
    @Excel(name = "计划协议号")
    private String scheduleAgreement;

    /**
     * 行项目号
     */
    @Excel(name = "行项目号")
    private String serialNumber;

    /**
     * 零件号:奇瑞零件号
     */
    @Excel(name = "零件号:奇瑞零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称")
    private String materialDescription;

    /**
     * 采购组
     */
    @Excel(name = "采购组")
    private String purchasingGroup;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量")
    private Long quantityDemand;

    /**
     * 交货日期-格式：yyyy-MM-dd
     */
    @Excel(name = "交货日期-格式：yyyy-MM-dd")
    private String dateReceived;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;

    private String createTime;

    private String updateTime;
}
