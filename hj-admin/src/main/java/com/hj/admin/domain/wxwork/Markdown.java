package com.hj.admin.domain.wxwork;

import com.hj.admin.config.WxWorkConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class Markdown extends BaseReq {

    private MarkdownContent markdown;
    private int enable_id_trans;

    @Data
    public static class MarkdownContent {
        private String content;
    }

    public Markdown(String toUser, String toParty, String toTag, String content, int enable_id_trans, WxWorkConfig wxWorkConfig) {
        super(toUser, toParty, toTag, "markdown", wxWorkConfig);
        this.markdown = new MarkdownContent();
        this.markdown.content = content;
        this.enable_id_trans = enable_id_trans;
    }
}

