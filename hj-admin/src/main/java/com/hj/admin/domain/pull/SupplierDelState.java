package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 看板配送单对象 supplier_del_state
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierDelState extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 配送单号
     */
    @Excel(name = "配送单号")
    private String deliveryNumber;

    /**
     * 行项目号
     */
    @Excel(name = "行项目号")
    private String serialNumber;

    /**
     * 配送单状态
     */
    @Excel(name = "配送单状态")
    private String serialSrate;

    /**
     * 零件号
     */
    @Excel(name = "零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称")
    private String materialDescription;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 收货道口
     */
    @Excel(name = "收货道口")
    private String receivingCrossings;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private Long quantityDelivery;

    /**
     * 创建时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "创建时间-格式：yyyy-MM-dd HH:mm:ss")
    private String dataCreateTime;

    /**
     * 供应商接收时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "供应商接收时间-格式：yyyy-MM-dd HH:mm:ss")
    private String supplierReceiveTime;

    /**
     * 道口发货时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "道口发货时间-格式：yyyy-MM-dd HH:mm:ss")
    private String roadShippedTime;

    /**
     * 道口收货时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "道口收货时间-格式：yyyy-MM-dd HH:mm:ss")
    private String roadReceiveTime;

    /**
     * 要求到货时间-格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "要求到货时间-格式：yyyy-MM-dd HH:mm:ss")
    private String lastRequrieTime;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;

    private String createTime;

    private String updateTime;


}
