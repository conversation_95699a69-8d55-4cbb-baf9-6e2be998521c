package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 生产过程数据对象 supplier_pro_data
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProData extends PushBaseDomain {

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 工位人员编号
     */
    @Excel(name = "工位人员编号")
    private String empCode;

    /**
     * 工位人员姓名
     */
    @Excel(name = "工位人员姓名")
    private String empName;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成批次号
     */
    @Excel(name = "供应商总成批次号")
    private String vendorProductBatch;

    /**
     * 供应商总成SN码
     */
    @Excel(name = "供应商总成SN码")
    private String vendorProductSn;

    /**
     * 子件编码
     */
    @Excel(name = "子件编码")
    private String subProdNo;

    /**
     * 子件名称
     */
    @Excel(name = "子件名称")
    private String subProdName;

    /**
     * 子件批次号
     */
    @Excel(name = "子件批次号")
    private String subBatchNo;

    /**
     * 子件分包号
     */
    @Excel(name = "子件分包号")
    private String childPackageInfo;

    /**
     * 子件扣料数量
     */
    @Excel(name = "子件扣料数量")
    private Long subProdNum;

    /**
     * 子件SN码
     */
    @Excel(name = "子件SN码")
    private String subProdSn;

    /**
     * 子件物料来源
     */
    @Excel(name = "子件物料来源")
    private String childSource;

    /**
     * 分供方代码
     */
    @Excel(name = "分供方代码")
    private String subSupplierCode;

    /**
     * 分供方名称
     */
    @Excel(name = "分供方名称")
    private String subSupplierName;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 奇瑞SN码
     */
    @Excel(name = "奇瑞SN码")
    private String cheryProductSn;

    /**
     * 生产工单号
     */
    @Excel(name = "生产工单号")
    private String manufactureNo;

    /**
     * 生产批次号
     */
    @Excel(name = "生产批次号")
    private String productBatchNo;

    /**
     * 班次(白班，晚班，中班)
     */
    @Excel(name = "班次(白班，晚班，中班)")
    private String workShift;

    /**
     * 进工位的时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "进工位的时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String materialInputTime;

    /**
     * 出工位的时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "出工位的时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String materialOutputTime;

    /**
     * 装配设备编号
     */
    @Excel(name = "装配设备编号")
    private String vendorFieldNum;

    /**
     * 装配设备名称
     */
    @Excel(name = "装配设备名称")
    private String vendorFieldName;

    /**
     * 设备判定的质量状态 , 合格与否，NG不合适 OK合适
     */
    @Excel(name = "设备判定的质量状态 , 合格与否，NG不合适 OK合适")
    private String instrumentQualityStatus;

    /**
     * 人工判定的质量状态, 合格与否，NG不合适 OK合适
     */
    @Excel(name = "人工判定的质量状态, 合格与否，NG不合适 OK合适")
    private String manualQualityStatus;

    /**
     * 最终质量状态, 合格与否，NG不合适 OK合适
     */
    @Excel(name = "最终质量状态, 合格与否，NG不合适 OK合适")
    private String finalQualityStatus;

    /**
     * 采集时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "采集时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String collectTime;

    /**
     * 子件绑定扫码时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "子件绑定扫码时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String dateTime;

    /**
     * 父件硬件版本号
     */
    @Excel(name = "父件硬件版本号")
    private String parentHardwareRevision;

    /**
     * 父件软件版本号
     */
    @Excel(name = "父件软件版本号")
    private String parentSoftwareRevision;

    /**
     * 子件硬件版本号
     */
    @Excel(name = "子件硬件版本号")
    private String childHardwareRevision;

    /**
     * 子件软件版本号
     */
    @Excel(name = "子件软件版本号")
    private String childSoftwareRevision;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
   @JsonIgnore
private Boolean hasPush;

    public SupplierProData(Boolean hasPush){
        this.hasPush = hasPush;
    }

}
