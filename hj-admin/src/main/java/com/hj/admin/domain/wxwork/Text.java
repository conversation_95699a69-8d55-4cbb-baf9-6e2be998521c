package com.hj.admin.domain.wxwork;

import com.hj.admin.config.WxWorkConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class Text extends BaseReq{

    TextContent text;
    Integer safe;
    Integer enable_id_trans;

    @Data
    public static class TextContent {
        private String content;
    }

    public Text(String toUser, String toParty, String toTag, String content, int enable_id_trans, WxWorkConfig wxWorkConfig) {
        super(toUser, toParty, toTag, "text", wxWorkConfig);
        this.text = new TextContent();
        this.text.content = content;
        this.enable_id_trans = enable_id_trans;
    }
}
