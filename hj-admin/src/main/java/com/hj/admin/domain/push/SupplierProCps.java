package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 过程控制项质量数据对象 supplier_pro_cps
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProCps extends PushBaseDomain {

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 供应商总成SN码
     */
    @Excel(name = "供应商总成SN码")
    private String vendorProductSn;

    /**
     * 供应商总成批次号
     */
    @Excel(name = "供应商总成批次号")
    private String vendorProductBatch;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 奇瑞SN码
     */
    @Excel(name = "奇瑞SN码")
    private String cheryProductSn;

    /**
     * 生产批次号
     */
    @Excel(name = "生产批次号")
    private String productBatchNo;

    /**
     * 生产工单号
     */
    @Excel(name = "生产工单号")
    private String manufactureNo;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 工位人员编号
     */
    @Excel(name = "工位人员编号")
    private String empCode;

    /**
     * 工位人员姓名
     */
    @Excel(name = "工位人员姓名")
    private String empName;

    /**
     * 控制项名称
     */
    @Excel(name = "控制项名称")
    private String vendorFieldName;

    /**
     * 控制项代码
     */
    @Excel(name = "控制项代码")
    private String vendorFieldCode;

    /**
     * 控制项点位
     */
    @Excel(name = "控制项点位")
    private String gatherSpot;

    /**
     * 控制项要求频率
     */
    @Excel(name = "控制项要求频率")
    private Long samplingRate;

    /**
     * 上下限更新时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "上下限更新时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String limitUpdateTime;

    /**
     * 控制项描述
     */
    @Excel(name = "控制项描述")
    private String vendorFieldDesc;

    /**
     * 载体编码
     */
    @Excel(name = "载体编码")
    private String carrierCode;

    /**
     * 投入数量
     */
    @Excel(name = "投入数量")
    private Long intputQty;

    /**
     * 一次合格数量
     */
    @Excel(name = "一次合格数量")
    private Long fttQty;

    /**
     * 参数 , 是传Y，否传N
     */
    @Excel(name = "参数 , 是传Y，否传N")
    private String parameter;

    /**
     * 特性 , 是传Y，否传N
     */
    @Excel(name = "特性 , 是传Y，否传N")
    private String characteristic;

    /**
     * CC项 , 是传Y，否传N
     */
    @Excel(name = "CC项 , 是传Y，否传N")
    private String cc;

    /**
     * SC项 , 是传Y，否传N
     */
    @Excel(name = "SC项 , 是传Y，否传N")
    private String sc;

    /**
     * SPC , 是传Y，否传N
     */
    @Excel(name = "SPC , 是传Y，否传N")
    private String spc;

    /**
     * 控制项标准值
     */
    @Excel(name = "控制项标准值")
    private String standardValue;

    /**
     * 控制项上限
     */
    @Excel(name = "控制项上限")
    private Long upperLimit;

    /**
     * 控制项下限
     */
    @Excel(name = "控制项下限")
    private Long lowerLimit;

    /**
     * 控制项实测值
     */
    @Excel(name = "控制项实测值")
    private Long decimalValue;

    /**
     * 控制项值的单位名称-中文
     */
    @Excel(name = "控制项值的单位名称-中文")
    private String unitCn;

    /**
     * 控控制项单位英文
     */
    @Excel(name = "控控制项单位英文")
    private String unitEn;

    /**
     * 检测结果
     */
    @Excel(name = "检测结果")
    private String checkResult;

    /**
     * 在线检测(inline,offline,both) , 可选项: inline - 在生产线上进行检测. offline - 从生产线上拿下来进行检测. both - inline 和 offline 同时存在
     */
    @Excel(name = "在线检测(inline,offline,both) , 可选项: inline - 在生产线上进行检测. offline - 从生产线上拿下来进行检测. both - inline 和 offline 同时存在")
    private String detectionMode;

    /**
     * 班次(白班，晚班，中班)
     */
    @Excel(name = "班次(白班，晚班，中班)")
    private String workShift;

    /**
     * 采集时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "采集时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String collectTime;

    /**
     * 检测方式(人工,设备)
     */
    @Excel(name = "检测方式(人工,设备)")
    private String checkMode;

    /**
     * 检测设备编号
     */
    @Excel(name = "检测设备编号")
    private String deviceCode;

    /**
     * 检测设备名称
     */
    @Excel(name = "检测设备名称")
    private String deviceName;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
   @JsonIgnore
private Boolean hasPush;

    public SupplierProCps(Boolean hasPush){
        this.hasPush = hasPush;
    }

}
