package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 日MRP预警推移对象 supplier_mrp_warning
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierMrpWarning extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 零件号:奇瑞零件号
     */
    @Excel(name = "零件号:奇瑞零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称")
    private String materialDescription;

    /**
     * 当前库存
     */
    @Excel(name = "当前库存")
    private Long quantityCurrent;

    /**
     * 需求日期-格式:yyyy-MM-dd
     */
    @Excel(name = "需求日期-格式:yyyy-MM-dd")
    private String reckonDate;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量")
    private Long quantityPlanned;

    /**
     * 满足数量
     */
    @Excel(name = "满足数量")
    private Long quantityPlannedDelivery;

    /**
     * 在途数量
     */
    @Excel(name = "在途数量")
    private Long quantityInTransit;

    /**
     * 日GAP:日需求数量与满足数量差异
     */
    @Excel(name = "日GAP:日需求数量与满足数量差异")
    private Long dateGap;

    /**
     * 库存GAP:库存推移差异
     */
    @Excel(name = "库存GAP:库存推移差异")
    private Long inventoryGap;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;

    private String createTime;

    private String updateTime;
}
