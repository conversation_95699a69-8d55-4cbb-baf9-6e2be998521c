package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 工艺子子集（关联supplier_pro_process_child）对象 supplier_pro_process_grand_child
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProProcessGrandChild extends PushBaseDomain {

    /**
     * 父级id
     */
    @Excel(name = "父级id")
    private Long parentId;

    /**
     * 工艺装备分类
     */
    @Excel(name = "工艺装备分类")
    private String deviceType;

    /**
     * 工艺装备代码
     */
    @Excel(name = "工艺装备代码")
    private String deviceId;

    /**
     * 工艺装备名称
     */
    @Excel(name = "工艺装备名称")
    private String deviceName;


}
