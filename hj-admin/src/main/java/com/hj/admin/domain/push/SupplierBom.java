package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * BOM主数据对象 supplier_bom
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierBom extends PushBaseDomain {

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * BOM编码
     */
    @Excel(name = "BOM编码")
    private String bomCode;

    /**
     * BOM名称
     */
    @Excel(name = "BOM名称")
    private String bomName;

    /**
     * BOM版本
     */
    @Excel(name = "BOM版本")
    private String bomVersion;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 供应商父件编码
     */
    @Excel(name = "供应商父件编码")
    private String vendorProductNo;

    /**
     * 供应商父件名称
     */
    @Excel(name = "供应商父件名称")
    private String vendorProductName;

    /**
     * 父件类型(成品, 半成品)
     */
    @Excel(name = "父件类型(成品, 半成品)")
    private String vendorProductType;

    /**
     * 父件单位
     */
    @Excel(name = "父件单位")
    private String materialUnit;

    /**
     * 子件编码
     */
    @Excel(name = "子件编码")
    private String subMaterialCode;

    /**
     * 子件名称
     */
    @Excel(name = "子件名称")
    private String subMaterialName;

    /**
     * 子件类型(半成品, 原材料)
     */
    @Excel(name = "子件类型(半成品, 原材料)")
    private String subMaterialType;

    /**
     * 子件单位
     */
    @Excel(name = "子件单位")
    private String subMaterialUnit;

    /**
     * 子件用量
     */
    @Excel(name = "子件用量")
    private Long subMaterialQuota;

    /**
     * BOM变更时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "BOM变更时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String dataUpdateTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
    @JsonIgnore
    private Boolean hasPush;

    public SupplierBom(Boolean hasPush) {
        this.hasPush = hasPush;
    }

}
