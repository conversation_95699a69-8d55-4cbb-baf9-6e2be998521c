package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 排序供货对象 supplier_pro_cschedul
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierProCschedul extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 车型
     */
    @Excel(name = "车型")
    private String models;

    /**
     * VIN
     */
    @Excel(name = "VIN")
    private String vin;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码")
    private String materialCode;

    /**
     * 物料描述
     */
    @Excel(name = "物料描述")
    private String materialDescription;

    /**
     * 排序日期:时间格式 yyyy-MM-dd
     */
    @Excel(name = "排序日期:时间格式 yyyy-MM-dd")
    private String sortDate;

    /**
     * 排序时间:时间格式 HH:mm:ss
     */
    @Excel(name = "排序时间:时间格式 HH:mm:ss")
    private String sortTime;

    /**
     * 上线日期
     */
    @Excel(name = "上线日期")
    private String onLineDate;

    /**
     * 上线时间:时间格式：20:34:12
     */
    @Excel(name = "上线时间:时间格式：20:34:12")
    private String onLineTime;

    /**
     * 车型类别
     */
    @Excel(name = "车型类别")
    private String modelCategory;

    /**
     * 动力总成物料号
     */
    @Excel(name = "动力总成物料号")
    private String assemblyMaterialCode;

    /**
     * 发动机物料号
     */
    @Excel(name = "发动机物料号")
    private String motorMaterialCode;

    /**
     * 工厂
     */
    @Excel(name = "工厂")
    private String plant;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;

    private String createTime;

    private String updateTime;
}
