package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 排产数据对象 supplier_pro_scheduling
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProScheduling extends PushBaseDomain {

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 供应商总成零件号
     */
    @Excel(name = "供应商总成零件号")
    private String vendorProductNo;

    /**
     * 供应商总成零件名称
     */
    @Excel(name = "供应商总成零件名称")
    private String vendorProductName;

    /**
     * 奇瑞零件号
     */
    @Excel(name = "奇瑞零件号")
    private String cheryProductNo;

    /**
     * 奇瑞零件名称
     */
    @Excel(name = "奇瑞零件名称")
    private String cheryProductName;

    /**
     * 计划单号
     */
    @Excel(name = "计划单号")
    private String planNo;

    /**
     * 生产工单号
     */
    @Excel(name = "生产工单号")
    private String manufactureNo;

    /**
     * 生产批次号
     */
    @Excel(name = "生产批次号")
    private String productBatchNo;

    /**
     * 批次计划数量
     */
    @Excel(name = "批次计划数量")
    private Long manufactureNum;

    /**
     * 批次投入数量
     */
    @Excel(name = "批次投入数量")
    private Long manufactureInputNum;

    /**
     * 批次产出数量
     */
    @Excel(name = "批次产出数量")
    private Long manufactureOutputNum;

    /**
     * 排产状态 , 0 未生产 1.生产中，2.已完工，3.已取消，4.已终止
     */
    @Excel(name = "排产状态 , 0 未生产 1.生产中，2.已完工，3.已取消，4.已终止")
    private String planStatus;

    /**
     * 计划开始时间 , 时间格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "计划开始时间 , 时间格式：yyyy-MM-dd HH:mm:ss")
    private String planBeginTime;

    /**
     * 计划结束时间 , 时间格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "计划结束时间 , 时间格式：yyyy-MM-dd HH:mm:ss")
    private String planEndTime;

    /**
     * 实际开始时间 , 时间格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "实际开始时间 , 时间格式：yyyy-MM-dd HH:mm:ss")
    private String actualBeginTime;

    /**
     * 实际结束时间 , 时间格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "实际结束时间 , 时间格式：yyyy-MM-dd HH:mm:ss")
    private String actualEndTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
   @JsonIgnore
private Boolean hasPush;

    public SupplierProScheduling(Boolean hasPush){
        this.hasPush = hasPush;
    }

}
