package com.hj.admin.domain.wxwork;


import com.hj.admin.config.WxWorkConfig;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BaseReq {

    private String touser;
    private String toparty;
    private String totag;
    private String msgtype;
    private int agentid;
    Integer enable_duplicate_check;
    Integer duplicate_check_interval;

    BaseReq(String toUser, String toParty, String toTag, String msgType, WxWorkConfig wxWorkConfig) {
        this.touser = toUser;
        this.toparty = toParty;
        this.totag = toTag;
        this.msgtype = msgType;
        this.agentid = wxWorkConfig.getAgentId();
        this.enable_duplicate_check = wxWorkConfig.getEnableDuplicateCheck();
        this.duplicate_check_interval = wxWorkConfig.getDuplicateCheckInterval();
    }

}
