package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 日MRP状态监控对象 supplier_mrp_state
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierMrpState extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 需求状态
     */
    @Excel(name = "需求状态")
    private String demandSrate;

    /**
     * 需求类型
     */
    @Excel(name = "需求类型")
    private String demandType;

    /**
     * 零件号:奇瑞零件号
     */
    @Excel(name = "零件号:奇瑞零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称")
    private String materialDescription;

    /**
     * 集货标识
     */
    @Excel(name = "集货标识")
    private String summarySign;

    /**
     * 需求日期-格式:yyyy-MM-dd
     */
    @Excel(name = "需求日期-格式:yyyy-MM-dd")
    private String dateRequired;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量")
    private Long quantityDemand;

    /**
     * 需求确认时间
     */
    @Excel(name = "需求确认时间")
    private String confirmTime;

    /**
     * 已建单数量
     */
    @Excel(name = "已建单数量")
    private Long creatQuantity;

    /**
     * 已发货数量
     */
    @Excel(name = "已发货数量")
    private Long quantityDelivery;

    /**
     * 已收货数量
     */
    @Excel(name = "已收货数量")
    private Long quantityReceive;

    /**
     * 在途数量
     */
    @Excel(name = "在途数量")
    private Long quantityInTransit;

    /**
     * 按时到货比
     */
    @Excel(name = "按时到货比")
    private Long onTimePercentage;

    /**
     * 集货件已建单数量
     */
    @Excel(name = "集货件已建单数量")
    private Long summaryCreatQuantity;

    /**
     * 集货件已发货数量
     */
    @Excel(name = "集货件已发货数量")
    private Long summaryQuantityDelivery;

    /**
     * 集货件已收货数量
     */
    @Excel(name = "集货件已收货数量")
    private Long summaryQuantityReceive;

    /**
     * 集货件已在途数量
     */
    @Excel(name = "集货件已在途数量")
    private Long summaryQuantityInTransit;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;

    private String createTime;

    private String updateTime;
}
