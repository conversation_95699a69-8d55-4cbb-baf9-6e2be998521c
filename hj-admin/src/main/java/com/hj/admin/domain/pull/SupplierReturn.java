package com.hj.admin.domain.pull;

import com.hj.admin.domain.base.BaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 退货单对象 supplier_return
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierReturn extends BaseDomain {

    /**
     * 主键：拉取数据供应商根据主键id判断是否存在，在新增和更新
     */
    private Long id;
    
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 退货单号
     */
    @Excel(name = "退货单号")
    private String returnNumber;

    /**
     * 行项目号
     */
    @Excel(name = "行项目号")
    private String serialNumber;

    /**
     * 退货单状态
     */
    @Excel(name = "退货单状态")
    private String serialSrate;

    /**
     * 取货地
     */
    @Excel(name = "取货地")
    private String pickUpLocation;

    /**
     * 需求取货时间:格式：yyyy-MM-dd HH:mm:ss
     */
    @Excel(name = "需求取货时间:格式：yyyy-MM-dd HH:mm:ss")
    private String demandPickupTime;

    /**
     * 取货道口
     */
    @Excel(name = "取货道口")
    private String pickUpCrossings;

    /**
     * 反馈信息:供应商在LES系统反馈的信息
     */
    @Excel(name = "反馈信息:供应商在LES系统反馈的信息")
    private String feedback;

    /**
     * 工厂:示例：1000-奇瑞汽车超一工厂
     */
    @Excel(name = "工厂:示例：1000-奇瑞汽车超一工厂")
    private String plant;

    /**
     * 零件号
     */
    @Excel(name = "零件号")
    private String materialCode;

    /**
     * 零件名称
     */
    @Excel(name = "零件名称")
    private String materialDescription;

    /**
     * 数量
     */
    @Excel(name = "数量 ")
    private Long quantityDelivery;

    /**
     * 退货类型:0-不合格品；1-合格品
     */
    @Excel(name = "退货类型:0-不合格品；1-合格品")
    private String returnType;

    /**
     * 批次号
     */
    @Excel(name = "批次号")
    private String lotNumber;

    /**
     * 判定人
     */
    @Excel(name = "判定人")
    private String judge;

    /**
     * 退货原因
     */
    @Excel(name = "退货原因")
    private String returnReason;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createByUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String updateByUser;

    /**
     * 是否删除(0:否,1是)(系统默认字段与业务数据无关)
     */
    @Excel(name = "是否删除(0:否,1是)(系统默认字段与业务数据无关)")
    private Long isDelete;

    /**
     * 版本号(系统默认字段与业务数据无关)
     */
    @Excel(name = "版本号(系统默认字段与业务数据无关)")
    private Long version;

    private String createTime;

    private String updateTime;
}
