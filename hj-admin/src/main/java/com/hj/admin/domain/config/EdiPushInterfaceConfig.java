package com.hj.admin.domain.config;

import com.hj.common.annotation.Excel;
import com.hj.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Push接口配置对象 edi_push_interface_config
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EdiPushInterfaceConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 接口路径 */
    @Excel(name = "接口路径")
    private String interfacePath;

    /** 接口名称 */
    @Excel(name = "接口名称")
    private String interfaceName;

    /** 功能维护者ID */
    @Excel(name = "功能维护者ID")
    private Long functionMaintainerId;

    /** 功能维护者姓名 */
    @Excel(name = "功能维护者姓名")
    private String functionMaintainerName;

    /** 接口维护者ID */
    @Excel(name = "接口维护者ID")
    private Long interfaceMaintainerId;

    /** 接口维护者姓名 */
    @Excel(name = "接口维护者姓名")
    private String interfaceMaintainerName;

    /** 接口描述 */
    @Excel(name = "接口描述")
    private String description;

}
