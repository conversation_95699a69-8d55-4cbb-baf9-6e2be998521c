package com.hj.admin.domain.wxwork;


import com.hj.admin.config.WxWorkConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TextCard extends BaseReq{

    private TextCardContent textcard;
    private int enable_id_trans;

    @Data
    public static class TextCardContent {
        private String title;
        private String description;
        private String url;
        private String btntxt;
    }

    public TextCard(String toUser, String toParty, String toTag, String title, String content, String url, int enable_id_trans, WxWorkConfig wxWorkConfig) {
        super(toUser, toParty, toTag, "textcard", wxWorkConfig);
        this.textcard = new TextCardContent();
        this.textcard.description = content;
        this.textcard.title = title;
        this.textcard.url = url;
        this.textcard.btntxt = "进入系统";
        this.enable_id_trans = enable_id_trans;
    }

}

