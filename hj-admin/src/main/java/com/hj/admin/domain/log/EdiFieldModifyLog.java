package com.hj.admin.domain.log;

import com.hj.common.annotation.Excel;
import com.hj.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字段维护者修改日志对象 edi_field_modify_log
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EdiFieldModifyLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 表名 */
    @Excel(name = "表名")
    private String tableName;

    /** 数据ID */
    @Excel(name = "数据ID")
    private Long dataId;

    /** 字段名 */
    @Excel(name = "字段名")
    private String fieldName;

    /** 修改前的值 */
    @Excel(name = "修改前的值")
    private String oldValue;

    /** 修改后的值 */
    @Excel(name = "修改后的值")
    private String newValue;

    /** 接口名称 */
    @Excel(name = "接口名称")
    private String interfaceName;

    /** 修改人ID */
    @Excel(name = "修改人ID")
    private Long modifyUserId;

    /** 修改人姓名 */
    @Excel(name = "修改人姓名")
    private String modifyUserName;

    /** 修改时间 */
    @Excel(name = "修改时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date modifyTime;

}
