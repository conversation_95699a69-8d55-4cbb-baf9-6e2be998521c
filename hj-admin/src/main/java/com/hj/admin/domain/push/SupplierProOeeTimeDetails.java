package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * OEE时间明细对象 supplier_pro_oee_time_details
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProOeeTimeDetails extends PushBaseDomain {

    /**
     * 记录ID
     */
    @Excel(name = "记录ID")
    private String recId;

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 工厂代码
     */
    @Excel(name = "工厂代码")
    private String plantId;

    /**
     * 工厂名称
     */
    @Excel(name = "工厂名称")
    private String plantName;

    /**
     * 车间代码
     */
    @Excel(name = "车间代码")
    private String workshopId;

    /**
     * 车间名称
     */
    @Excel(name = "车间名称")
    private String workshopName;

    /**
     * 产线代码
     */
    @Excel(name = "产线代码")
    private String productionLineId;

    /**
     * 产线名称
     */
    @Excel(name = "产线名称")
    private String productionLineName;

    /**
     * 工位代码
     */
    @Excel(name = "工位代码")
    private String stationId;

    /**
     * 工位名称
     */
    @Excel(name = "工位名称")
    private String stationName;

    /**
     * 工艺装备分类(1设备；2模具；)
     */
    @Excel(name = "工艺装备分类(1设备；2模具；)")
    private String deviceType;

    /**
     * 工艺装备代码
     */
    @Excel(name = "工艺装备代码")
    private String deviceId;

    /**
     * 工艺装备名称
     */
    @Excel(name = "工艺装备名称")
    private String deviceName;

    /**
     * 大类(1计划工作,2计划停机,3非计划停机)
     */
    @Excel(name = "大类(1计划工作,2计划停机,3非计划停机)")
    private String type;

    /**
     * 小类(1工作；2维修；3保养；4停机)
     */
    @Excel(name = "小类(1工作；2维修；3保养；4停机)")
    private String subType;

    /**
     * 小类描述
     */
    @Excel(name = "小类描述")
    private String subTypeDescription;

    /**
     * 计划开始时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "计划开始时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String planBeginTime;

    /**
     * 计划结束时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "计划结束时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String planEndTime;

    /**
     * 实际开始时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "实际开始时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String startTime;

    /**
     * 实际结束时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "实际结束时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String endTime;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
   @JsonIgnore
private Boolean hasPush;

    public SupplierProOeeTimeDetails(Boolean hasPush){
        this.hasPush = hasPush;
    }

}
