package com.hj.admin.domain.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hj.common.annotation.Excel;
import com.hj.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * EDI操作记录对象 edi_operation_log
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EdiOperationLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 操作类型（PULL/PUSH） */
    @Excel(name = "操作类型", readConverterExp = "PULL=拉取,PUSH=推送")
    private String operationType;

    /** 接口路径 */
    @Excel(name = "接口路径")
    private String interfacePath;

    /** 接口名称 */
    @Excel(name = "接口名称")
    private String interfaceName;

    /** 推送状态（0-待推送，1-推送中，2-推送成功，3-推送失败） */
    @Excel(name = "推送状态", readConverterExp = "0=待推送,1=推送中,2=推送成功,3=推送失败")
    private Integer pushStatus;

    /** 请求成功状态（0-失败，1-成功） */
    @Excel(name = "请求成功状态", readConverterExp = "0=失败,1=成功")
    private Integer requestStatus;

    /** 请求参数 */
    private String requestParams;

    /** 响应结果 */
    private String responseResult;

    /** 错误信息 */
    private String errorMessage;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 执行耗时（毫秒） */
    @Excel(name = "执行耗时(ms)")
    private Long executionTime;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 是否定时任务触发（0-手动，1-定时任务） */
    @Excel(name = "触发方式", readConverterExp = "0=手动,1=定时任务")
    private Integer isScheduled;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    @Excel(name = "操作人")
    private String operatorName;
}
