package com.hj.admin.domain.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PushBaseDomain extends BaseDomain{

    private Long id;

    /**
     * 入批次号
     */
    @JsonIgnore
    private String enterBatchNo;

    /**
     * 出批次号
     */
    @JsonIgnore
    private String outBatchNo;

    /**
     * 是否已确认字段
     */
    @JsonIgnore
    private Boolean fieldHasConfirm;

    /**
     * 是否已确认接口
     */
    @JsonIgnore
    private Boolean interfaceHasConfirm;

    /**
     * 是否已确认功能
     */
    @JsonIgnore
    private Boolean functionHasConfirm;

}
