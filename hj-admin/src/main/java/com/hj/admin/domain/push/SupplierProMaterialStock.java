package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 来料检验数据对象 supplier_pro_material_stock
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProMaterialStock extends PushBaseDomain {

    /**
     * 供应商代码
     */
    @Excel(name = "供应商代码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 子件编码
     */
    @Excel(name = "子件编码")
    private String supplierSubCode;

    /**
     * 子件名称
     */
    @Excel(name = "子件名称")
    private String supplierSubName;

    /**
     * 分供方代码
     */
    @Excel(name = "分供方代码")
    private String subSupplierCode;

    /**
     * 分供方名称
     */
    @Excel(name = "分供方名称")
    private String subSupplierName;

    /**
     * 分供方地址 , 分供方发货地址：省市区（县），不用于详细地址
     */
    @Excel(name = "分供方地址 , 分供方发货地址：省市区", readConverterExp = "县=")
    private String subSupplierAddress;

    /**
     * 分供方子件编码
     */
    @Excel(name = "分供方子件编码")
    private String componentCode;

    /**
     * 分供方子件名称
     */
    @Excel(name = "分供方子件名称")
    private String componentName;

    /**
     * 子件批次号
     */
    @Excel(name = "子件批次号")
    private String subBatchNo;

    /**
     * 子件批次数量
     */
    @Excel(name = "子件批次数量")
    private Long subBatchNum;

    /**
     * 子件SN码
     */
    @Excel(name = "子件SN码")
    private String subBatchSn;

    /**
     * 检验人员编号
     */
    @Excel(name = "检验人员编号")
    private String empCode;

    /**
     * 检验人员姓名
     */
    @Excel(name = "检验人员姓名")
    private String empName;

    /**
     * 检测设备编号
     */
    @Excel(name = "检测设备编号")
    private String deviceCode;

    /**
     * 检测设备名称
     */
    @Excel(name = "检测设备名称")
    private String deviceName;

    /**
     * 参数名称/特性名称
     */
    @Excel(name = "参数名称/特性名称")
    private String featureName;

    /**
     * 参数单位/特性单位
     */
    @Excel(name = "参数单位/特性单位")
    private String featureUnit;

    /**
     * 参数/特性标准值
     */
    @Excel(name = "参数/特性标准值")
    private String standardValue;

    /**
     * 参数/特性上限值
     */
    @Excel(name = "参数/特性上限值")
    private String featureUpper;

    /**
     * 参数/特性下限值
     */
    @Excel(name = "参数/特性下限值")
    private String featureLower;

    /**
     * 参数/特性实测值
     */
    @Excel(name = "参数/特性实测值")
    private String featureValue;

    /**
     * 来料检验单号
     */
    @Excel(name = "来料检验单号")
    private String checkNo;

    /**
     * 来料检验结果，OK合格/NG不合格
     */
    @Excel(name = "来料检验结果，OK合格/NG不合格")
    private String checkResult;

    /**
     * 检验时间,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "检验时间,格式(yyyy-MM-dd HH:mm:ss)")
    private String checkTime;

    /**
     * 控制项要求频率
     */
    @Excel(name = "控制项要求频率")
    private Long samplingRate;

    /**
     * 上下限更新时间，格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "上下限更新时间，格式(yyyy-MM-dd HH:mm:ss)")
    private String limitUpdateTime;

    /**
     * 控制项描述
     */
    @Excel(name = "控制项描述")
    private String vendorFieldDesc;

    /**
     * 控制项代码
     */
    @Excel(name = "控制项代码")
    private String vendorFieldCode;

    /**
     * 库存有效日期,格式(yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "库存有效日期,格式(yyyy-MM-dd HH:mm:ss)")
    private String deadLine;

    /**
     * 是否推送过数据 0未推送 1已推送
     */
    @Excel(name = "是否推送过数据 0未推送 1已推送")
   @JsonIgnore
private Boolean hasPush;

    public SupplierProMaterialStock(Boolean hasPush){
        this.hasPush = hasPush;
    }

}
