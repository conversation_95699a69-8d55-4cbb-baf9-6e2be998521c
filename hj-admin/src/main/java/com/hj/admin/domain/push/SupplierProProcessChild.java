package com.hj.admin.domain.push;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hj.admin.domain.base.BaseDomain;
import com.hj.admin.domain.base.PushBaseDomain;
import com.hj.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 工艺子集（关联supplier_pro_process）对象 supplier_pro_process_child
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierProProcessChild extends PushBaseDomain {

    /**
     * 父级id
     */
    @Excel(name = "父级id")
    private Long parentId;

    /**
     * 工序编码
     */
    @Excel(name = "工序编码")
    private String processCode;

    /**
     * 工序名称
     */
    @Excel(name = "工序名称")
    private String processName;

    /**
     * 工序顺序号
     */
    @Excel(name = "工序顺序号")
    private Long processOrder;

    /**
     * 工序节拍
     */
    @Excel(name = "工序节拍")
    private Long rhythm;

    /**
     * 委外工序供应商编码
     */
    @Excel(name = "委外工序供应商编码")
    private String processSupplierCode;

    /**
     * 委外工序供应商名称
     */
    @Excel(name = "委外工序供应商名称")
    private String processSupplierName;

    /**
     * 工序所在地
     */
    @Excel(name = "工序所在地")
    private String processLocation;

    /**
     * 工序间流转时间
     */
    @Excel(name = "工序间流转时间")
    private Long processCycleTime;

    /**
     * 工序良率目标
     */
    @Excel(name = "工序良率目标")
    private Long processYieldTarget;

    private List<SupplierProProcessGrandChild> subList;

}
