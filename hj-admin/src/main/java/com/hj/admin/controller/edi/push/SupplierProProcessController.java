package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProProcess;
import com.hj.admin.domain.push.SupplierProProcessChild;
import com.hj.admin.domain.push.SupplierProProcessGrandChild;
import com.hj.admin.service.push.ISupplierProProcessChildService;
import com.hj.admin.service.push.ISupplierProProcessGrandChildService;
import com.hj.admin.service.push.ISupplierProProcessService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 工艺 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/process")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProProcessController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProProcessService supplierProProcessService;

    @Resource
    private ISupplierProProcessChildService supplierProProcessChildService;

    @Resource
    private ISupplierProProcessGrandChildService supplierProProcessGrandChildService;

    //@RequiresPermissions("edi:process:view")
    @GetMapping()
    public String process() {
        return prefix + "/process";
    }

    /**
     * 查询工艺列表
     */
    //@RequiresPermissions("edi:process:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProProcess supplierProProcess) {
        startPage();
        List<SupplierProProcess> list = supplierProProcessService.selectSupplierProProcessList(supplierProProcess);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出工艺列表
     */
    //@RequiresPermissions("edi:process:export")
    @Log(title = "工艺", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProProcess supplierProProcess) {
        List<SupplierProProcess> list = supplierProProcessService.selectSupplierProProcessList(supplierProProcess);
        ExcelUtil<SupplierProProcess> util = new ExcelUtil<SupplierProProcess>(SupplierProProcess.class);
        return util.exportExcel(list, "工艺数据");
    }

    /**
     * 新增保存工艺
     */
    //@RequiresPermissions("edi:process:add")
    @Log(title = "工艺", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    @Transactional
    public AjaxResult addSave(@RequestBody SupplierProProcess supplierProProcess) {
        try {
            // 保存主数据
            int result = supplierProProcessService.insertSupplierProProcess(supplierProProcess);
            if (result > 0) {
                Long parentId = supplierProProcess.getId();

                // 保存子级数据
                if (supplierProProcess.getChildList() != null && !supplierProProcess.getChildList().isEmpty()) {
                    for (SupplierProProcessChild child : supplierProProcess.getChildList()) {
                        child.setParentId(parentId);
                        supplierProProcessChildService.insertSupplierProProcessChild(child);

                        // 保存孙级数据
                        if (child.getSubList() != null && !child.getSubList().isEmpty()) {
                            for (SupplierProProcessGrandChild grandChild : child.getSubList()) {
                                grandChild.setParentId(child.getId());
                                supplierProProcessGrandChildService.insertSupplierProProcessGrandChild(grandChild);
                            }
                        }
                    }
                }
            }
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 修改保存工艺
     */
    //@RequiresPermissions("edi:process:edit")
    @Log(title = "工艺", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    @Transactional
    public AjaxResult editSave(@RequestBody SupplierProProcess supplierProProcess) {
        try {
            // 更新主数据
            int result = supplierProProcessService.updateSupplierProProcess(supplierProProcess);
            if (result > 0) {
                Long parentId = supplierProProcess.getId();

                // 删除原有的子级和孙级数据
                SupplierProProcessChild childQuery = new SupplierProProcessChild();
                childQuery.setParentId(parentId);
                List<SupplierProProcessChild> existingChildren = supplierProProcessChildService.selectSupplierProProcessChildList(childQuery);

                for (SupplierProProcessChild existingChild : existingChildren) {
                    // 删除孙级数据
                    SupplierProProcessGrandChild grandChildQuery = new SupplierProProcessGrandChild();
                    grandChildQuery.setParentId(existingChild.getId());
                    List<SupplierProProcessGrandChild> existingGrandChildren = supplierProProcessGrandChildService.selectSupplierProProcessGrandChildList(grandChildQuery);
                    for (SupplierProProcessGrandChild grandChild : existingGrandChildren) {
                        supplierProProcessGrandChildService.deleteSupplierProProcessGrandChildById(grandChild.getId());
                    }
                    // 删除子级数据
                    supplierProProcessChildService.deleteSupplierProProcessChildById(existingChild.getId());
                }

                // 重新保存子级数据
                if (supplierProProcess.getChildList() != null && !supplierProProcess.getChildList().isEmpty()) {
                    for (SupplierProProcessChild child : supplierProProcess.getChildList()) {
                        child.setParentId(parentId);
                        child.setId(null); // 确保是新增
                        supplierProProcessChildService.insertSupplierProProcessChild(child);

                        // 保存孙级数据
                        if (child.getSubList() != null && !child.getSubList().isEmpty()) {
                            for (SupplierProProcessGrandChild grandChild : child.getSubList()) {
                                grandChild.setParentId(child.getId());
                                grandChild.setId(null); // 确保是新增
                                supplierProProcessGrandChildService.insertSupplierProProcessGrandChild(grandChild);
                            }
                        }
                    }
                }
            }
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除工艺
     */
    //@RequiresPermissions("edi:process:remove")
    @Log(title = "工艺", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    @Transactional
    public AjaxResult remove(String ids) {
        try {
            String[] idArray = ids.split(",");
            for (String idStr : idArray) {
                Long id = Long.parseLong(idStr.trim());

                // 查找并删除子级数据
                SupplierProProcessChild childQuery = new SupplierProProcessChild();
                childQuery.setParentId(id);
                List<SupplierProProcessChild> children = supplierProProcessChildService.selectSupplierProProcessChildList(childQuery);

                for (SupplierProProcessChild child : children) {
                    // 查找并删除孙级数据
                    SupplierProProcessGrandChild grandChildQuery = new SupplierProProcessGrandChild();
                    grandChildQuery.setParentId(child.getId());
                    List<SupplierProProcessGrandChild> grandChildren = supplierProProcessGrandChildService.selectSupplierProProcessGrandChildList(grandChildQuery);

                    for (SupplierProProcessGrandChild grandChild : grandChildren) {
                        supplierProProcessGrandChildService.deleteSupplierProProcessGrandChildById(grandChild.getId());
                    }

                    // 删除子级数据
                    supplierProProcessChildService.deleteSupplierProProcessChildById(child.getId());
                }

                // 删除主数据
                supplierProProcessService.deleteSupplierProProcessById(id);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 查看工艺详情（包含子级和孙级数据）
     */
    //@RequiresPermissions("edi:process:detail")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProProcess supplierProProcess = supplierProProcessService.selectSupplierProProcessById(id);

        // 查询子级数据
        SupplierProProcessChild childQuery = new SupplierProProcessChild();
        childQuery.setParentId(id);
        List<SupplierProProcessChild> childList = supplierProProcessChildService.selectSupplierProProcessChildList(childQuery);

        // 为每个子级查询孙级数据
        for (SupplierProProcessChild child : childList) {
            SupplierProProcessGrandChild grandChildQuery = new SupplierProProcessGrandChild();
            grandChildQuery.setParentId(child.getId());
            List<SupplierProProcessGrandChild> grandChildList = supplierProProcessGrandChildService.selectSupplierProProcessGrandChildList(grandChildQuery);
            child.setSubList(grandChildList);
        }

        supplierProProcess.setChildList(childList);
        mmap.put("supplierProProcess", supplierProProcess);
        return "edi/push/processDetail";
    }

}
