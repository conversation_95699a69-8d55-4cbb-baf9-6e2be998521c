package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierProCschedul;
import com.hj.admin.service.pull.ISupplierProCschedulService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 排序供货 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/cschedul")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProCschedulController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierProCschedulService supplierProCschedulService;

    //@RequiresPermissions("edi:cschedul:view")
    @GetMapping()
    public String cschedul()
    {
        return prefix + "/cschedul";
    }

    /**
     * 查询排序供货列表
     */
    //@RequiresPermissions("edi:cschedul:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProCschedul supplierProCschedul)
    {
        startPage();
        List<SupplierProCschedul> list = supplierProCschedulService.selectSupplierProCschedulList(supplierProCschedul);
        return getDataTable(list);
    }

    /**
     * 导出排序供货列表
     */
    //@RequiresPermissions("edi:cschedul:export")
    @Log(title = "排序供货", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProCschedul supplierProCschedul)
    {
        List<SupplierProCschedul> list = supplierProCschedulService.selectSupplierProCschedulList(supplierProCschedul);
        ExcelUtil<SupplierProCschedul> util = new ExcelUtil<SupplierProCschedul>(SupplierProCschedul.class);
        return util.exportExcel(list, "排序供货数据");
    }

    /**
     * 新增排序供货
     */
    //@RequiresPermissions("edi:cschedul:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存排序供货
     */
    //@RequiresPermissions("edi:cschedul:add")
    @Log(title = "排序供货", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProCschedul supplierProCschedul)
    {
        return toAjax(supplierProCschedulService.insertSupplierProCschedul(supplierProCschedul));
    }

    /**
     * 修改排序供货
     */
    //@RequiresPermissions("edi:cschedul:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProCschedul supplierProCschedul = supplierProCschedulService.selectSupplierProCschedulById(id);
        mmap.put("supplierProCschedul", supplierProCschedul);
        return prefix + "/edit";
    }

    /**
     * 修改保存排序供货
     */
    //@RequiresPermissions("edi:cschedul:edit")
    @Log(title = "排序供货", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProCschedul supplierProCschedul)
    {
        return toAjax(supplierProCschedulService.updateSupplierProCschedul(supplierProCschedul));
    }

    /**
     * 删除排序供货
     */
    //@RequiresPermissions("edi:cschedul:remove")
    @Log(title = "排序供货", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProCschedulService.deleteSupplierProCschedulByIds(ids));
    }
}
