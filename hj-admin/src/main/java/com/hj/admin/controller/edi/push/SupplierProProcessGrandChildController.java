package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProProcessGrandChild;
import com.hj.admin.service.push.ISupplierProProcessGrandChildService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺子子集（关联supplier_pro_process_child）Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/proProcessGrandChild")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProProcessGrandChildController extends BaseController
{
    private String prefix = "edi/proProcessGrandChild";

    @Resource
    private ISupplierProProcessGrandChildService supplierProProcessGrandChildService;

    //@RequiresPermissions("edi:child:view")
    @GetMapping()
    public String child()
    {
        return prefix + "/child";
    }

    /**
     * 查询工艺子子集（关联supplier_pro_process_child）列表
     */
    //@RequiresPermissions("edi:child:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        startPage();
        List<SupplierProProcessGrandChild> list = supplierProProcessGrandChildService.selectSupplierProProcessGrandChildList(supplierProProcessGrandChild);
        return getDataTable(list);
    }

    /**
     * 导出工艺子子集（关联supplier_pro_process_child）列表
     */
    //@RequiresPermissions("edi:child:export")
    @Log(title = "工艺子子集（关联supplier_pro_process_child）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        List<SupplierProProcessGrandChild> list = supplierProProcessGrandChildService.selectSupplierProProcessGrandChildList(supplierProProcessGrandChild);
        ExcelUtil<SupplierProProcessGrandChild> util = new ExcelUtil<SupplierProProcessGrandChild>(SupplierProProcessGrandChild.class);
        return util.exportExcel(list, "工艺子子集（关联supplier_pro_process_child）数据");
    }

    /**
     * 新增工艺子子集（关联supplier_pro_process_child）
     */
    //@RequiresPermissions("edi:child:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存工艺子子集（关联supplier_pro_process_child）
     */
    //@RequiresPermissions("edi:child:add")
    @Log(title = "工艺子子集（关联supplier_pro_process_child）", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        return toAjax(supplierProProcessGrandChildService.insertSupplierProProcessGrandChild(supplierProProcessGrandChild));
    }

    /**
     * 修改工艺子子集（关联supplier_pro_process_child）
     */
    //@RequiresPermissions("edi:child:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProProcessGrandChild supplierProProcessGrandChild = supplierProProcessGrandChildService.selectSupplierProProcessGrandChildById(id);
        mmap.put("supplierProProcessGrandChild", supplierProProcessGrandChild);
        return prefix + "/edit";
    }

    /**
     * 修改保存工艺子子集（关联supplier_pro_process_child）
     */
    //@RequiresPermissions("edi:child:edit")
    @Log(title = "工艺子子集（关联supplier_pro_process_child）", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProProcessGrandChild supplierProProcessGrandChild)
    {
        return toAjax(supplierProProcessGrandChildService.updateSupplierProProcessGrandChild(supplierProProcessGrandChild));
    }

    /**
     * 删除工艺子子集（关联supplier_pro_process_child）
     */
    //@RequiresPermissions("edi:child:remove")
    @Log(title = "工艺子子集（关联supplier_pro_process_child）", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProProcessGrandChildService.deleteSupplierProProcessGrandChildByIds(ids));
    }
}
