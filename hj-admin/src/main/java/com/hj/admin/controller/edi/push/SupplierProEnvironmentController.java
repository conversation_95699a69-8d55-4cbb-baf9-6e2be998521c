package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProEnvironment;
import com.hj.admin.service.push.ISupplierProEnvironmentService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 环境业务数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/environment")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProEnvironmentController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProEnvironmentService supplierProEnvironmentService;

    //@RequiresPermissions("edi:environment:view")
    @GetMapping()
    public String environment() {
        return prefix + "/environment";
    }

    /**
     * 查询环境业务数据列表
     */
    //@RequiresPermissions("edi:environment:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProEnvironment supplierProEnvironment) {
        startPage();
        List<SupplierProEnvironment> list = supplierProEnvironmentService.selectSupplierProEnvironmentList(supplierProEnvironment);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出环境业务数据列表
     */
    //@RequiresPermissions("edi:environment:export")
    @Log(title = "环境业务数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProEnvironment supplierProEnvironment) {
        List<SupplierProEnvironment> list = supplierProEnvironmentService.selectSupplierProEnvironmentList(supplierProEnvironment);
        ExcelUtil<SupplierProEnvironment> util = new ExcelUtil<SupplierProEnvironment>(SupplierProEnvironment.class);
        return util.exportExcel(list, "环境业务数据数据");
    }

    /**
     * 新增环境业务数据
     */
    //@RequiresPermissions("edi:environment:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存环境业务数据
     */
    //@RequiresPermissions("edi:environment:add")
    @Log(title = "环境业务数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProEnvironment supplierProEnvironment) {
        return toAjax(supplierProEnvironmentService.insertSupplierProEnvironment(supplierProEnvironment));
    }

    /**
     * 修改环境业务数据
     */
    //@RequiresPermissions("edi:environment:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProEnvironment supplierProEnvironment = supplierProEnvironmentService.selectSupplierProEnvironmentById(id);
        mmap.put("supplierProEnvironment", supplierProEnvironment);
        return prefix + "/edit";
    }

    /**
     * 修改保存环境业务数据
     */
    //@RequiresPermissions("edi:environment:edit")
    @Log(title = "环境业务数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProEnvironment supplierProEnvironment) {
        return toAjax(supplierProEnvironmentService.updateSupplierProEnvironment(supplierProEnvironment));
    }

    /**
     * 删除环境业务数据
     */
    //@RequiresPermissions("edi:environment:remove")
    @Log(title = "环境业务数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProEnvironmentService.deleteSupplierProEnvironmentByIds(ids));
    }

}
