package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierInvData;
import com.hj.admin.service.pull.ISupplierInvDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 奇瑞RDC共享库存 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/invData")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierInvDataController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierInvDataService supplierInvDataService;

    //@RequiresPermissions("edi:invData:view")
    @GetMapping()
    public String data()
    {
        return prefix + "/invData";
    }

    /**
     * 查询奇瑞RDC共享库存列表
     */
    //@RequiresPermissions("edi:invData:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierInvData supplierInvData)
    {
        startPage();
        List<SupplierInvData> list = supplierInvDataService.selectSupplierInvDataList(supplierInvData);
        return getDataTable(list);
    }

    /**
     * 导出奇瑞RDC共享库存列表
     */
    //@RequiresPermissions("edi:invData:export")
    @Log(title = "奇瑞RDC共享库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierInvData supplierInvData)
    {
        List<SupplierInvData> list = supplierInvDataService.selectSupplierInvDataList(supplierInvData);
        ExcelUtil<SupplierInvData> util = new ExcelUtil<SupplierInvData>(SupplierInvData.class);
        return util.exportExcel(list, "奇瑞RDC共享库存数据");
    }

    /**
     * 新增奇瑞RDC共享库存
     */
    //@RequiresPermissions("edi:invData:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存奇瑞RDC共享库存
     */
    //@RequiresPermissions("edi:invData:add")
    @Log(title = "奇瑞RDC共享库存", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierInvData supplierInvData)
    {
        return toAjax(supplierInvDataService.insertSupplierInvData(supplierInvData));
    }

    /**
     * 修改奇瑞RDC共享库存
     */
    //@RequiresPermissions("edi:invData:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierInvData supplierInvData = supplierInvDataService.selectSupplierInvDataById(id);
        mmap.put("supplierInvData", supplierInvData);
        return prefix + "/edit";
    }

    /**
     * 修改保存奇瑞RDC共享库存
     */
    //@RequiresPermissions("edi:invData:edit")
    @Log(title = "奇瑞RDC共享库存", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierInvData supplierInvData)
    {
        return toAjax(supplierInvDataService.updateSupplierInvData(supplierInvData));
    }

    /**
     * 删除奇瑞RDC共享库存
     */
    //@RequiresPermissions("edi:invData:remove")
    @Log(title = "奇瑞RDC共享库存", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierInvDataService.deleteSupplierInvDataByIds(ids));
    }
}
