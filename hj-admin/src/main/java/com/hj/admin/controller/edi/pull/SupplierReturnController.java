package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierReturn;
import com.hj.admin.service.pull.ISupplierReturnService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退货单 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/return")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierReturnController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierReturnService supplierReturnService;

    //@RequiresPermissions("edi:return:view")
    @GetMapping()
    public String returns()
    {
        return prefix + "/return";
    }

    /**
     * 查询退货单列表
     */
    //@RequiresPermissions("edi:return:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierReturn supplierReturn)
    {
        startPage();
        List<SupplierReturn> list = supplierReturnService.selectSupplierReturnList(supplierReturn);
        return getDataTable(list);
    }

    /**
     * 导出退货单列表
     */
    //@RequiresPermissions("edi:return:export")
    @Log(title = "退货单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierReturn supplierReturn)
    {
        List<SupplierReturn> list = supplierReturnService.selectSupplierReturnList(supplierReturn);
        ExcelUtil<SupplierReturn> util = new ExcelUtil<SupplierReturn>(SupplierReturn.class);
        return util.exportExcel(list, "退货单数据");
    }

    /**
     * 新增退货单
     */
    //@RequiresPermissions("edi:return:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存退货单
     */
    //@RequiresPermissions("edi:return:add")
    @Log(title = "退货单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierReturn supplierReturn)
    {
        return toAjax(supplierReturnService.insertSupplierReturn(supplierReturn));
    }

    /**
     * 修改退货单
     */
    //@RequiresPermissions("edi:return:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierReturn supplierReturn = supplierReturnService.selectSupplierReturnById(id);
        mmap.put("supplierReturn", supplierReturn);
        return prefix + "/edit";
    }

    /**
     * 修改保存退货单
     */
    //@RequiresPermissions("edi:return:edit")
    @Log(title = "退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierReturn supplierReturn)
    {
        return toAjax(supplierReturnService.updateSupplierReturn(supplierReturn));
    }

    /**
     * 删除退货单
     */
    //@RequiresPermissions("edi:return:remove")
    @Log(title = "退货单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierReturnService.deleteSupplierReturnByIds(ids));
    }
}
