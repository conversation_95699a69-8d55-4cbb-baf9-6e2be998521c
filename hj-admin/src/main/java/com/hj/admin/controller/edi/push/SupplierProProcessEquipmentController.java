package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProProcessEquipment;
import com.hj.admin.service.push.ISupplierProProcessEquipmentService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 工艺装备 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/equipment")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProProcessEquipmentController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProProcessEquipmentService supplierProProcessEquipmentService;

    //@RequiresPermissions("edi:equipment:view")
    @GetMapping()
    public String equipment() {
        return prefix + "/equipment";
    }

    /**
     * 查询工艺装备列表
     */
    //@RequiresPermissions("edi:equipment:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProProcessEquipment supplierProProcessEquipment) {
        startPage();
        List<SupplierProProcessEquipment> list = supplierProProcessEquipmentService.selectSupplierProProcessEquipmentList(supplierProProcessEquipment);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出工艺装备列表
     */
    //@RequiresPermissions("edi:equipment:export")
    @Log(title = "工艺装备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProProcessEquipment supplierProProcessEquipment) {
        List<SupplierProProcessEquipment> list = supplierProProcessEquipmentService.selectSupplierProProcessEquipmentList(supplierProProcessEquipment);
        ExcelUtil<SupplierProProcessEquipment> util = new ExcelUtil<SupplierProProcessEquipment>(SupplierProProcessEquipment.class);
        return util.exportExcel(list, "工艺装备数据");
    }

    /**
     * 新增工艺装备
     */
    //@RequiresPermissions("edi:equipment:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存工艺装备
     */
    //@RequiresPermissions("edi:equipment:add")
    @Log(title = "工艺装备", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProProcessEquipment supplierProProcessEquipment) {
        return toAjax(supplierProProcessEquipmentService.insertSupplierProProcessEquipment(supplierProProcessEquipment));
    }

    /**
     * 修改工艺装备
     */
    //@RequiresPermissions("edi:equipment:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProProcessEquipment supplierProProcessEquipment = supplierProProcessEquipmentService.selectSupplierProProcessEquipmentById(id);
        mmap.put("supplierProProcessEquipment", supplierProProcessEquipment);
        return prefix + "/edit";
    }

    /**
     * 修改保存工艺装备
     */
    //@RequiresPermissions("edi:equipment:edit")
    @Log(title = "工艺装备", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProProcessEquipment supplierProProcessEquipment) {
        return toAjax(supplierProProcessEquipmentService.updateSupplierProProcessEquipment(supplierProProcessEquipment));
    }

    /**
     * 删除工艺装备
     */
    //@RequiresPermissions("edi:equipment:remove")
    @Log(title = "工艺装备", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProProcessEquipmentService.deleteSupplierProProcessEquipmentByIds(ids));
    }

}
