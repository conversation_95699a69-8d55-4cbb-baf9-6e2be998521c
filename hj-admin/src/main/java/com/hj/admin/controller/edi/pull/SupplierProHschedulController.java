package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierProHschedul;
import com.hj.admin.service.pull.ISupplierProHschedulService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 过焊装未过总装 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/hschedul")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProHschedulController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierProHschedulService supplierProHschedulService;

    //@RequiresPermissions("edi:hschedul:view")
    @GetMapping()
    public String hschedul()
    {
        return prefix + "/hschedul";
    }

    /**
     * 查询过焊装未过总装列表
     */
    //@RequiresPermissions("edi:hschedul:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProHschedul supplierProHschedul)
    {
        startPage();
        List<SupplierProHschedul> list = supplierProHschedulService.selectSupplierProHschedulList(supplierProHschedul);
        return getDataTable(list);
    }

    /**
     * 导出过焊装未过总装列表
     */
    //@RequiresPermissions("edi:hschedul:export")
    @Log(title = "过焊装未过总装", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProHschedul supplierProHschedul)
    {
        List<SupplierProHschedul> list = supplierProHschedulService.selectSupplierProHschedulList(supplierProHschedul);
        ExcelUtil<SupplierProHschedul> util = new ExcelUtil<SupplierProHschedul>(SupplierProHschedul.class);
        return util.exportExcel(list, "过焊装未过总装数据");
    }

    /**
     * 新增过焊装未过总装
     */
    //@RequiresPermissions("edi:hschedul:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存过焊装未过总装
     */
    //@RequiresPermissions("edi:hschedul:add")
    @Log(title = "过焊装未过总装", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProHschedul supplierProHschedul)
    {
        return toAjax(supplierProHschedulService.insertSupplierProHschedul(supplierProHschedul));
    }

    /**
     * 修改过焊装未过总装
     */
    //@RequiresPermissions("edi:hschedul:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProHschedul supplierProHschedul = supplierProHschedulService.selectSupplierProHschedulById(id);
        mmap.put("supplierProHschedul", supplierProHschedul);
        return prefix + "/edit";
    }

    /**
     * 修改保存过焊装未过总装
     */
    //@RequiresPermissions("edi:hschedul:edit")
    @Log(title = "过焊装未过总装", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProHschedul supplierProHschedul)
    {
        return toAjax(supplierProHschedulService.updateSupplierProHschedul(supplierProHschedul));
    }

    /**
     * 删除过焊装未过总装
     */
    //@RequiresPermissions("edi:hschedul:remove")
    @Log(title = "过焊装未过总装", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProHschedulService.deleteSupplierProHschedulByIds(ids));
    }
}
