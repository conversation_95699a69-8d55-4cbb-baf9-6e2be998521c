package com.hj.admin.controller.edi.confirm;

import com.hj.admin.mapper.push.PushBaseMapper;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.enums.BusinessType;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 接口确认 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Controller
@RequestMapping("/edi/confirm/interfaceConfirm")
@RequiresRoles(value = {"interface", "admin"}, logical = Logical.OR)
public class EidInterfaceConfirmController extends BaseController
{
    @Resource
    private PushBaseMapper pushBaseMapper;

    //@RequiresPermissions("edi:interfaceConfirm:view")
    @GetMapping()
    public String interfaceConfirm()
    {
        return "edi/confirm/interfaceConfirm";
    }

    @Log(title = "BOM主数据", businessType = BusinessType.UPDATE)
    @PostMapping( "/confirm")
    @ResponseBody
    public AjaxResult confirm(String batchNo, String tableName)
    {
        return toAjax(pushBaseMapper.changeInterfaceConfirmStatus(batchNo, tableName));
    }

}
