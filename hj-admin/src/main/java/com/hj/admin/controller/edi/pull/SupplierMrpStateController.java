package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierMrpState;
import com.hj.admin.service.pull.ISupplierMrpStateService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日MRP状态监控 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/mrpState")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierMrpStateController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierMrpStateService supplierMrpStateService;

    //@RequiresPermissions("edi:mrpState:view")
    @GetMapping()
    public String state()
    {
        return prefix + "/mrpState";
    }

    /**
     * 查询日MRP状态监控列表
     */
    //@RequiresPermissions("edi:mrpState:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierMrpState supplierMrpState)
    {
        startPage();
        List<SupplierMrpState> list = supplierMrpStateService.selectSupplierMrpStateList(supplierMrpState);
        return getDataTable(list);
    }

    /**
     * 导出日MRP状态监控列表
     */
    //@RequiresPermissions("edi:mrpState:export")
    @Log(title = "日MRP状态监控", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierMrpState supplierMrpState)
    {
        List<SupplierMrpState> list = supplierMrpStateService.selectSupplierMrpStateList(supplierMrpState);
        ExcelUtil<SupplierMrpState> util = new ExcelUtil<SupplierMrpState>(SupplierMrpState.class);
        return util.exportExcel(list, "日MRP状态监控数据");
    }

    /**
     * 新增日MRP状态监控
     */
    //@RequiresPermissions("edi:mrpState:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存日MRP状态监控
     */
    //@RequiresPermissions("edi:mrpState:add")
    @Log(title = "日MRP状态监控", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierMrpState supplierMrpState)
    {
        return toAjax(supplierMrpStateService.insertSupplierMrpState(supplierMrpState));
    }

    /**
     * 修改日MRP状态监控
     */
    //@RequiresPermissions("edi:mrpState:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierMrpState supplierMrpState = supplierMrpStateService.selectSupplierMrpStateById(id);
        mmap.put("supplierMrpState", supplierMrpState);
        return prefix + "/edit";
    }

    /**
     * 修改保存日MRP状态监控
     */
    //@RequiresPermissions("edi:mrpState:edit")
    @Log(title = "日MRP状态监控", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierMrpState supplierMrpState)
    {
        return toAjax(supplierMrpStateService.updateSupplierMrpState(supplierMrpState));
    }

    /**
     * 删除日MRP状态监控
     */
    //@RequiresPermissions("edi:mrpState:remove")
    @Log(title = "日MRP状态监控", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierMrpStateService.deleteSupplierMrpStateByIds(ids));
    }
}
