package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProScheduling;
import com.hj.admin.service.push.ISupplierProSchedulingService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 排产数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/scheduling")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProSchedulingController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProSchedulingService supplierProSchedulingService;

    //@RequiresPermissions("edi:scheduling:view")
    @GetMapping()
    public String scheduling() {
        return prefix + "/scheduling";
    }

    /**
     * 查询排产数据列表
     */
    //@RequiresPermissions("edi:scheduling:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProScheduling supplierProScheduling) {
        startPage();
        List<SupplierProScheduling> list = supplierProSchedulingService.selectSupplierProSchedulingList(supplierProScheduling);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出排产数据列表
     */
    //@RequiresPermissions("edi:scheduling:export")
    @Log(title = "排产数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProScheduling supplierProScheduling) {
        List<SupplierProScheduling> list = supplierProSchedulingService.selectSupplierProSchedulingList(supplierProScheduling);
        ExcelUtil<SupplierProScheduling> util = new ExcelUtil<SupplierProScheduling>(SupplierProScheduling.class);
        return util.exportExcel(list, "排产数据数据");
    }

    /**
     * 新增排产数据
     */
    //@RequiresPermissions("edi:scheduling:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存排产数据
     */
    //@RequiresPermissions("edi:scheduling:add")
    @Log(title = "排产数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProScheduling supplierProScheduling) {
        return toAjax(supplierProSchedulingService.insertSupplierProScheduling(supplierProScheduling));
    }

    /**
     * 修改排产数据
     */
    //@RequiresPermissions("edi:scheduling:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProScheduling supplierProScheduling = supplierProSchedulingService.selectSupplierProSchedulingById(id);
        mmap.put("supplierProScheduling", supplierProScheduling);
        return prefix + "/edit";
    }

    /**
     * 修改保存排产数据
     */
    //@RequiresPermissions("edi:scheduling:edit")
    @Log(title = "排产数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProScheduling supplierProScheduling) {
        return toAjax(supplierProSchedulingService.updateSupplierProScheduling(supplierProScheduling));
    }

    /**
     * 删除排产数据
     */
    //@RequiresPermissions("edi:scheduling:remove")
    @Log(title = "排产数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProSchedulingService.deleteSupplierProSchedulingByIds(ids));
    }

}
