package com.hj.admin.controller.edi.confirm;

import com.hj.admin.domain.config.EdiPushFieldConfig;
import com.hj.admin.service.IEdiFieldModifyLogService;
import com.hj.admin.service.IEdiPushFieldConfigService;
import com.hj.admin.service.IEdiPushFieldConfirmService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.ShiroUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Push接口字段确认Controller
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Controller
@RequestMapping("/edi/confirm/pushFieldConfirm")
@RequiresRoles(value = {"field", "admin"}, logical = Logical.OR)
public class EdiPushFieldConfirmController extends BaseController {
    @Resource
    private IEdiPushFieldConfirmService iEdiPushFieldConfirmService;
    @Resource
    private IEdiPushFieldConfigService iEdiPushFieldConfigService;
    @Resource
    private IEdiFieldModifyLogService iEdiFieldModifyLogService;

    //@RequiresPermissions("edi:fieldConfirm:view")
    @GetMapping()
    public String fieldConfirm() {
        return "edi/confirm/fieldConfirm";
    }


    @Log(title = "Push接口字段确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    @ResponseBody
    public AjaxResult confirm(String batchNo, String interfacePath) {
        return toAjax(iEdiPushFieldConfirmService.confirm(batchNo, interfacePath));
    }

    /**
     * 获取当前用户当前接口可以确认的数据
     */
    //@RequiresPermissions("edi:fieldConfirm:confirm")
    @PostMapping("/getByInterfacePathAndUser")
    @ResponseBody
    public List<String> getByInterfacePathAndUser(String interfacePath) {
        try {
            Long currentUserId = ShiroUtils.getUserId();

            // 查询当前用户有权限维护的字段
            List<EdiPushFieldConfig> userFields = iEdiPushFieldConfigService.selectByInterfacePathAndUser(interfacePath, currentUserId);
            if (userFields.isEmpty()) {
                return new ArrayList<>();
            }
            return userFields.stream()
                    .map(EdiPushFieldConfig::getColumnName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取字段确认数据失败：" + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 记录字段修改日志
     */
    //@RequiresPermissions("edi:fieldConfirm:edit")
    @PostMapping("/recordModifyLog")
    @ResponseBody
    public AjaxResult recordModifyLog(String interfaceName, String tableName, Long dataId,
                                      String fieldName, String oldValue, String newValue) {
        try {
            Long currentUserId = ShiroUtils.getUserId();
            String currentUserName = ShiroUtils.getLoginName();

            // 验证用户是否有权限修改该字段
            EdiPushFieldConfig fieldQuery = new EdiPushFieldConfig();
            fieldQuery.setFieldMaintainerId(currentUserId);
            fieldQuery.setColumnName(fieldName);
            List<EdiPushFieldConfig> userFields = iEdiPushFieldConfigService.selectEdiPushFieldConfigList(fieldQuery);

            if (userFields.isEmpty()) {
                return AjaxResult.error("您没有权限修改该字段");
            }

            // 记录修改日志
            iEdiFieldModifyLogService.recordFieldModify(interfaceName, tableName, dataId,
                    fieldName, oldValue, newValue,
                    currentUserId, currentUserName);

            return AjaxResult.success("日志记录成功");
        } catch (Exception e) {
            logger.error("记录修改日志失败：" + e.getMessage(), e);
            return AjaxResult.error("记录日志失败：" + e.getMessage());
        }
    }
}
