package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierConMmrp;
import com.hj.admin.service.push.ISupplierConMmrpService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * M+6月物料需求计划风险确认 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/conMmrp")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierConMmrpController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierConMmrpService supplierConMmrpService;

    //@RequiresPermissions("edi:conMmrp:view")
    @GetMapping()
    public String mmrp() {
        return prefix + "/conMmrp";
    }

    /**
     * 查询M+6月物料需求计划风险确认列表
     */
    //@RequiresPermissions("edi:conMmrp:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierConMmrp supplierConMmrp) {
        startPage();
        List<SupplierConMmrp> list = supplierConMmrpService.selectSupplierConMmrpList(supplierConMmrp);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出M+6月物料需求计划风险确认列表
     */
    //@RequiresPermissions("edi:conMmrp:export")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierConMmrp supplierConMmrp) {
        List<SupplierConMmrp> list = supplierConMmrpService.selectSupplierConMmrpList(supplierConMmrp);
        ExcelUtil<SupplierConMmrp> util = new ExcelUtil<SupplierConMmrp>(SupplierConMmrp.class);
        return util.exportExcel(list, "M+6月物料需求计划风险确认数据");
    }

    /**
     * 新增M+6月物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conMmrp:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存M+6月物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conMmrp:add")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierConMmrp supplierConMmrp) {
        return toAjax(supplierConMmrpService.insertSupplierConMmrp(supplierConMmrp));
    }

    /**
     * 修改M+6月物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conMmrp:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierConMmrp supplierConMmrp = supplierConMmrpService.selectSupplierConMmrpById(id);
        mmap.put("supplierConMmrp", supplierConMmrp);
        return prefix + "/edit";
    }

    /**
     * 修改保存M+6月物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conMmrp:edit")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierConMmrp supplierConMmrp) {
        return toAjax(supplierConMmrpService.updateSupplierConMmrp(supplierConMmrp));
    }

    /**
     * 删除M+6月物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conMmrp:remove")
    @Log(title = "M+6月物料需求计划风险确认", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierConMmrpService.deleteSupplierConMmrpByIds(ids));
    }

}
