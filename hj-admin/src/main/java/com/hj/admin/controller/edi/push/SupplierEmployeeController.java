package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierEmployee;
import com.hj.admin.service.push.ISupplierEmployeeService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 人员资质信息 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/employee")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierEmployeeController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierEmployeeService supplierEmployeeService;

    //@RequiresPermissions("edi:employee:view")
    @GetMapping()
    public String employee() {
        return prefix + "/employee";
    }

    /**
     * 查询人员资质信息列表
     */
    //@RequiresPermissions("edi:employee:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierEmployee supplierEmployee) {
        startPage();
        List<SupplierEmployee> list = supplierEmployeeService.selectSupplierEmployeeList(supplierEmployee);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出人员资质信息列表
     */
    //@RequiresPermissions("edi:employee:export")
    @Log(title = "人员资质信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierEmployee supplierEmployee) {
        List<SupplierEmployee> list = supplierEmployeeService.selectSupplierEmployeeList(supplierEmployee);
        ExcelUtil<SupplierEmployee> util = new ExcelUtil<SupplierEmployee>(SupplierEmployee.class);
        return util.exportExcel(list, "人员资质信息数据");
    }

    /**
     * 新增人员资质信息
     */
    //@RequiresPermissions("edi:employee:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存人员资质信息
     */
    //@RequiresPermissions("edi:employee:add")
    @Log(title = "人员资质信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierEmployee supplierEmployee) {
        return toAjax(supplierEmployeeService.insertSupplierEmployee(supplierEmployee));
    }

    /**
     * 修改人员资质信息
     */
    //@RequiresPermissions("edi:employee:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierEmployee supplierEmployee = supplierEmployeeService.selectSupplierEmployeeById(id);
        mmap.put("supplierEmployee", supplierEmployee);
        return prefix + "/edit";
    }

    /**
     * 修改保存人员资质信息
     */
    //@RequiresPermissions("edi:employee:edit")
    @Log(title = "人员资质信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierEmployee supplierEmployee) {
        return toAjax(supplierEmployeeService.updateSupplierEmployee(supplierEmployee));
    }

    /**
     * 删除人员资质信息
     */
    //@RequiresPermissions("edi:employee:remove")
    @Log(title = "人员资质信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierEmployeeService.deleteSupplierEmployeeByIds(ids));
    }

}
