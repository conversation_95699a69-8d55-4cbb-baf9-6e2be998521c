package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierProPlaning;
import com.hj.admin.service.pull.ISupplierProPlaningService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 整车月度生产计划 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
@Controller
@RequestMapping("/edi/pull/planing")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProPlaningController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierProPlaningService supplierProPlaningService;

    //@RequiresPermissions("edi:planing:view")
    @GetMapping()
    public String planing()
    {
        return prefix + "/planing";
    }

    /**
     * 查询整车月度生产计划列表
     */
    //@RequiresPermissions("edi:planing:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProPlaning supplierProPlaning)
    {
        startPage();
        List<SupplierProPlaning> list = supplierProPlaningService.selectSupplierProPlaningList(supplierProPlaning);
        return getDataTable(list);
    }

    /**
     * 导出整车月度生产计划列表
     */
    //@RequiresPermissions("edi:planing:export")
    @Log(title = "整车月度生产计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProPlaning supplierProPlaning)
    {
        List<SupplierProPlaning> list = supplierProPlaningService.selectSupplierProPlaningList(supplierProPlaning);
        ExcelUtil<SupplierProPlaning> util = new ExcelUtil<SupplierProPlaning>(SupplierProPlaning.class);
        return util.exportExcel(list, "整车月度生产计划数据");
    }

    /**
     * 新增整车月度生产计划
     */
    //@RequiresPermissions("edi:planing:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存整车月度生产计划
     */
    //@RequiresPermissions("edi:planing:add")
    @Log(title = "整车月度生产计划", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProPlaning supplierProPlaning)
    {
        return toAjax(supplierProPlaningService.insertSupplierProPlaning(supplierProPlaning));
    }

    /**
     * 修改整车月度生产计划
     */
    //@RequiresPermissions("edi:planing:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProPlaning supplierProPlaning = supplierProPlaningService.selectSupplierProPlaningById(id);
        mmap.put("supplierProPlaning", supplierProPlaning);
        return prefix + "/edit";
    }

    /**
     * 修改保存整车月度生产计划
     */
    //@RequiresPermissions("edi:planing:edit")
    @Log(title = "整车月度生产计划", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProPlaning supplierProPlaning)
    {
        return toAjax(supplierProPlaningService.updateSupplierProPlaning(supplierProPlaning));
    }

    /**
     * 删除整车月度生产计划
     */
    //@RequiresPermissions("edi:planing:remove")
    @Log(title = "整车月度生产计划", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProPlaningService.deleteSupplierProPlaningByIds(ids));
    }
}
