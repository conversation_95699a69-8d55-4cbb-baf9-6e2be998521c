package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProFlaw;
import com.hj.admin.service.push.ISupplierProFlawService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 缺陷业务数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/proFlaw")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProFlawController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProFlawService supplierProFlawService;

    //@RequiresPermissions("edi:proFlaw:view")
    @GetMapping()
    public String flaw() {
        return prefix + "/proFlaw";
    }

    /**
     * 查询缺陷业务数据列表
     */
    //@RequiresPermissions("edi:proFlaw:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProFlaw supplierProFlaw) {
        startPage();
        List<SupplierProFlaw> list = supplierProFlawService.selectSupplierProFlawList(supplierProFlaw);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出缺陷业务数据列表
     */
    //@RequiresPermissions("edi:proFlaw:export")
    @Log(title = "缺陷业务数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProFlaw supplierProFlaw) {
        List<SupplierProFlaw> list = supplierProFlawService.selectSupplierProFlawList(supplierProFlaw);
        ExcelUtil<SupplierProFlaw> util = new ExcelUtil<SupplierProFlaw>(SupplierProFlaw.class);
        return util.exportExcel(list, "缺陷业务数据数据");
    }

    /**
     * 新增缺陷业务数据
     */
    //@RequiresPermissions("edi:proFlaw:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存缺陷业务数据
     */
    //@RequiresPermissions("edi:proFlaw:add")
    @Log(title = "缺陷业务数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProFlaw supplierProFlaw) {
        return toAjax(supplierProFlawService.insertSupplierProFlaw(supplierProFlaw));
    }

    /**
     * 修改缺陷业务数据
     */
    //@RequiresPermissions("edi:proFlaw:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProFlaw supplierProFlaw = supplierProFlawService.selectSupplierProFlawById(id);
        mmap.put("supplierProFlaw", supplierProFlaw);
        return prefix + "/edit";
    }

    /**
     * 修改保存缺陷业务数据
     */
    //@RequiresPermissions("edi:proFlaw:edit")
    @Log(title = "缺陷业务数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProFlaw supplierProFlaw) {
        return toAjax(supplierProFlawService.updateSupplierProFlaw(supplierProFlaw));
    }

    /**
     * 删除缺陷业务数据
     */
    //@RequiresPermissions("edi:proFlaw:remove")
    @Log(title = "缺陷业务数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProFlawService.deleteSupplierProFlawByIds(ids));
    }

}
