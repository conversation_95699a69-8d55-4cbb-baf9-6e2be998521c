package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierBom;
import com.hj.admin.service.push.ISupplierBomService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * BOM主数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/bom")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierBomController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierBomService supplierBomService;

    //@RequiresPermissions("edi:bom:view")
    @GetMapping()
    public String bom() {
        return prefix + "/bom";
    }

    /**
     * 查询BOM主数据列表
     */
    //@RequiresPermissions("edi:bom:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierBom supplierBom) {
        startPage();
        List<SupplierBom> list = supplierBomService.selectSupplierBomList(supplierBom);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出BOM主数据列表
     */
    //@RequiresPermissions("edi:bom:export")
    @Log(title = "BOM主数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierBom supplierBom) {
        List<SupplierBom> list = supplierBomService.selectSupplierBomList(supplierBom);
        ExcelUtil<SupplierBom> util = new ExcelUtil<SupplierBom>(SupplierBom.class);
        return util.exportExcel(list, "BOM主数据数据");
    }

    /**
     * 新增BOM主数据
     */
    //@RequiresPermissions("edi:bom:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存BOM主数据
     */
    //@RequiresPermissions("edi:bom:add")
    @Log(title = "BOM主数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierBom supplierBom) {
        return toAjax(supplierBomService.insertSupplierBom(supplierBom));
    }

    /**
     * 修改BOM主数据
     */
    //@RequiresPermissions("edi:bom:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierBom supplierBom = supplierBomService.selectSupplierBomById(id);
        mmap.put("supplierBom", supplierBom);
        return prefix + "/edit";
    }

    /**
     * 修改保存BOM主数据
     */
    //@RequiresPermissions("edi:bom:edit")
    @Log(title = "BOM主数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierBom supplierBom) {
        return toAjax(supplierBomService.updateSupplierBom(supplierBom));
    }

    /**
     * 删除BOM主数据
     */
    //@RequiresPermissions("edi:bom:remove")
    @Log(title = "BOM主数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierBomService.deleteSupplierBomByIds(ids));
    }

}
