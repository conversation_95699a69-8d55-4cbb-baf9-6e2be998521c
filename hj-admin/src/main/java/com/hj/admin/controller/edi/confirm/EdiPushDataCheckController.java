package com.hj.admin.controller.edi.confirm;

import com.hj.common.core.controller.BaseController;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 推送数据查看
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/edi/confirm/pushDataCheck")
@RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
public class EdiPushDataCheckController extends BaseController
{
    //@RequiresPermissions("edi:pushDataCheck:view")
    @GetMapping()
    public String pushDataCheck()
    {
        return "edi/confirm/pushDataCheck";
    }
}
