package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProMaterialStock;
import com.hj.admin.service.push.ISupplierProMaterialStockService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 来料检验数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Controller
@RequestMapping("/edi/push/materialStock")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProMaterialStockController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProMaterialStockService supplierProMaterialStockService;

    //@RequiresPermissions("edi:materialStock:view")
    @GetMapping()
    public String stock() {
        return prefix + "/materialStock";
    }

    /**
     * 查询来料检验数据列表
     */
    //@RequiresPermissions("edi:materialStock:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProMaterialStock supplierProMaterialStock) {
        startPage();
        List<SupplierProMaterialStock> list = supplierProMaterialStockService.selectSupplierProMaterialStockList(supplierProMaterialStock);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出来料检验数据列表
     */
    //@RequiresPermissions("edi:materialStock:export")
    @Log(title = "来料检验数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProMaterialStock supplierProMaterialStock) {
        List<SupplierProMaterialStock> list = supplierProMaterialStockService.selectSupplierProMaterialStockList(supplierProMaterialStock);
        ExcelUtil<SupplierProMaterialStock> util = new ExcelUtil<SupplierProMaterialStock>(SupplierProMaterialStock.class);
        return util.exportExcel(list, "来料检验数据数据");
    }

    /**
     * 新增来料检验数据
     */
    //@RequiresPermissions("edi:materialStock:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存来料检验数据
     */
    //@RequiresPermissions("edi:materialStock:add")
    @Log(title = "来料检验数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProMaterialStock supplierProMaterialStock) {
        return toAjax(supplierProMaterialStockService.insertSupplierProMaterialStock(supplierProMaterialStock));
    }

    /**
     * 修改来料检验数据
     */
    //@RequiresPermissions("edi:materialStock:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProMaterialStock supplierProMaterialStock = supplierProMaterialStockService.selectSupplierProMaterialStockById(id);
        mmap.put("supplierProMaterialStock", supplierProMaterialStock);
        return prefix + "/edit";
    }

    /**
     * 修改保存来料检验数据
     */
    //@RequiresPermissions("edi:materialStock:edit")
    @Log(title = "来料检验数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProMaterialStock supplierProMaterialStock) {
        return toAjax(supplierProMaterialStockService.updateSupplierProMaterialStock(supplierProMaterialStock));
    }

    /**
     * 删除来料检验数据
     */
    //@RequiresPermissions("edi:materialStock:remove")
    @Log(title = "来料检验数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProMaterialStockService.deleteSupplierProMaterialStockByIds(ids));
    }

}
