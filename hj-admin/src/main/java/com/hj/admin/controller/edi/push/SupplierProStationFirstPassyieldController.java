package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProStationFirstPassyield;
import com.hj.admin.service.push.ISupplierProStationFirstPassyieldService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 工位一次合格率 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/stationFirstPassyield")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProStationFirstPassyieldController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProStationFirstPassyieldService supplierProStationFirstPassyieldService;

    //@RequiresPermissions("edi:stationFirstPassyield:view")
    @GetMapping()
    public String passyield() {
        return prefix + "/stationFirstPassyield";
    }

    /**
     * 查询工位一次合格率列表
     */
    //@RequiresPermissions("edi:stationFirstPassyield:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProStationFirstPassyield supplierProStationFirstPassyield) {
        startPage();
        List<SupplierProStationFirstPassyield> list = supplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldList(supplierProStationFirstPassyield);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出工位一次合格率列表
     */
    //@RequiresPermissions("edi:stationFirstPassyield:export")
    @Log(title = "工位一次合格率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProStationFirstPassyield supplierProStationFirstPassyield) {
        List<SupplierProStationFirstPassyield> list = supplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldList(supplierProStationFirstPassyield);
        ExcelUtil<SupplierProStationFirstPassyield> util = new ExcelUtil<SupplierProStationFirstPassyield>(SupplierProStationFirstPassyield.class);
        return util.exportExcel(list, "工位一次合格率数据");
    }

    /**
     * 新增工位一次合格率
     */
    //@RequiresPermissions("edi:stationFirstPassyield:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存工位一次合格率
     */
    //@RequiresPermissions("edi:stationFirstPassyield:add")
    @Log(title = "工位一次合格率", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProStationFirstPassyield supplierProStationFirstPassyield) {
        return toAjax(supplierProStationFirstPassyieldService.insertSupplierProStationFirstPassyield(supplierProStationFirstPassyield));
    }

    /**
     * 修改工位一次合格率
     */
    //@RequiresPermissions("edi:stationFirstPassyield:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProStationFirstPassyield supplierProStationFirstPassyield = supplierProStationFirstPassyieldService.selectSupplierProStationFirstPassyieldById(id);
        mmap.put("supplierProStationFirstPassyield", supplierProStationFirstPassyield);
        return prefix + "/edit";
    }

    /**
     * 修改保存工位一次合格率
     */
    //@RequiresPermissions("edi:stationFirstPassyield:edit")
    @Log(title = "工位一次合格率", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProStationFirstPassyield supplierProStationFirstPassyield) {
        return toAjax(supplierProStationFirstPassyieldService.updateSupplierProStationFirstPassyield(supplierProStationFirstPassyield));
    }

    /**
     * 删除工位一次合格率
     */
    //@RequiresPermissions("edi:stationFirstPassyield:remove")
    @Log(title = "工位一次合格率", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProStationFirstPassyieldService.deleteSupplierProStationFirstPassyieldByIds(ids));
    }

}
