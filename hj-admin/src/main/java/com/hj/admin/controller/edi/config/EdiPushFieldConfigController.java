package com.hj.admin.controller.edi.config;

import com.hj.admin.domain.config.EdiPushFieldConfig;
import com.hj.admin.service.IEdiPushFieldConfigService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Push接口字段配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Controller
@RequestMapping("/edi/pushFieldConfig")
public class EdiPushFieldConfigController extends BaseController
{
    private final String prefix = "edi/config";

    @Autowired
    private IEdiPushFieldConfigService ediPushFieldConfigService;

    //@RequiresPermissions("edi:pushFieldConfig:view")
    @GetMapping()
    public String config()
    {
        return prefix + "/pushFieldConfig";
    }

    /**
     * 查询Push接口字段配置列表
     */
    //@RequiresPermissions("edi:pushFieldConfig:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(EdiPushFieldConfig ediPushFieldConfig)
    {
        startPage();
        List<EdiPushFieldConfig> list = ediPushFieldConfigService.selectEdiPushFieldConfigList(ediPushFieldConfig);
        return getDataTable(list);
    }

    /**
     * 导出Push接口字段配置列表
     */
    //@RequiresPermissions("edi:pushFieldConfig:export")
    @Log(title = "Push接口字段配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(EdiPushFieldConfig ediPushFieldConfig)
    {
        List<EdiPushFieldConfig> list = ediPushFieldConfigService.selectEdiPushFieldConfigList(ediPushFieldConfig);
        ExcelUtil<EdiPushFieldConfig> util = new ExcelUtil<EdiPushFieldConfig>(EdiPushFieldConfig.class);
        return util.exportExcel(list, "Push接口字段配置数据");
    }

    /**
     * 新增Push接口字段配置
     */
    //@RequiresPermissions("edi:pushFieldConfig:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/pushFieldConfigAdd";
    }

    /**
     * 新增保存Push接口字段配置
     */
    //@RequiresPermissions("edi:pushFieldConfig:add")
    @Log(title = "Push接口字段配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(EdiPushFieldConfig ediPushFieldConfig)
    {
        return toAjax(ediPushFieldConfigService.insertEdiPushFieldConfig(ediPushFieldConfig));
    }

    /**
     * 修改Push接口字段配置
     */
    //@RequiresPermissions("edi:pushFieldConfig:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        EdiPushFieldConfig ediPushFieldConfig = ediPushFieldConfigService.selectEdiPushFieldConfigById(id);
        mmap.put("ediPushFieldConfig", ediPushFieldConfig);
        return prefix + "/pushFieldConfigEdit";
    }

    /**
     * 修改保存Push接口字段配置
     */
    //@RequiresPermissions("edi:pushFieldConfig:edit")
    @Log(title = "Push接口字段配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(EdiPushFieldConfig ediPushFieldConfig)
    {
        return toAjax(ediPushFieldConfigService.updateEdiPushFieldConfig(ediPushFieldConfig));
    }

    /**
     * 删除Push接口字段配置
     */
    //@RequiresPermissions("edi:pushFieldConfig:remove")
    @Log(title = "Push接口字段配置", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(ediPushFieldConfigService.deleteEdiPushFieldConfigByIds(ids));
    }

    /**
     * 批量新增Push接口字段配置
     */
    //@RequiresPermissions("edi:pushFieldConfig:add")
    @Log(title = "Push接口字段配置", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    @ResponseBody
    @Transactional
    public AjaxResult batchAdd(@RequestBody List<EdiPushFieldConfig> fieldConfigs)
    {
        try {
            int successCount = 0;
            for (EdiPushFieldConfig config : fieldConfigs) {
                // 检查是否已存在相同配置
                EdiPushFieldConfig existingConfig = new EdiPushFieldConfig();
                existingConfig.setInterfaceConfigId(config.getInterfaceConfigId());
                existingConfig.setColumnName(config.getColumnName());
                List<EdiPushFieldConfig> existingList = ediPushFieldConfigService.selectEdiPushFieldConfigList(existingConfig);

                if (existingList.isEmpty()) {
                    ediPushFieldConfigService.insertEdiPushFieldConfig(config);
                    successCount++;
                }
            }
            return AjaxResult.success("成功配置 " + successCount + " 个字段");
        } catch (Exception e) {
            return AjaxResult.error("批量配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取接口已配置的字段
     */
    //@RequiresPermissions("edi:pushFieldConfig:list")
    @GetMapping("/getConfiguredFields/{interfaceConfigId}")
    @ResponseBody
    public AjaxResult getConfiguredFields(@PathVariable("interfaceConfigId") Long interfaceConfigId)
    {
        EdiPushFieldConfig query = new EdiPushFieldConfig();
        query.setInterfaceConfigId(interfaceConfigId);
        List<EdiPushFieldConfig> configuredFields = ediPushFieldConfigService.selectEdiPushFieldConfigList(query);
        return AjaxResult.success(configuredFields);
    }
}
