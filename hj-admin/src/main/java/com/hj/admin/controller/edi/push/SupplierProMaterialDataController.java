package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProMaterialData;
import com.hj.admin.service.push.ISupplierProMaterialDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 物料主数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/materialData")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProMaterialDataController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProMaterialDataService supplierProMaterialDataService;

    //@RequiresPermissions("edi:materialData:view")
    @GetMapping()
    public String data() {
        return prefix + "/materialData";
    }

    /**
     * 查询物料主数据列表
     */
    //@RequiresPermissions("edi:materialData:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProMaterialData supplierProMaterialData) {
        startPage();
        List<SupplierProMaterialData> list = supplierProMaterialDataService.selectSupplierProMaterialDataList(supplierProMaterialData);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出物料主数据列表
     */
    //@RequiresPermissions("edi:materialData:export")
    @Log(title = "物料主数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProMaterialData supplierProMaterialData) {
        List<SupplierProMaterialData> list = supplierProMaterialDataService.selectSupplierProMaterialDataList(supplierProMaterialData);
        ExcelUtil<SupplierProMaterialData> util = new ExcelUtil<SupplierProMaterialData>(SupplierProMaterialData.class);
        return util.exportExcel(list, "物料主数据数据");
    }

    /**
     * 新增物料主数据
     */
    //@RequiresPermissions("edi:materialData:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存物料主数据
     */
    //@RequiresPermissions("edi:materialData:add")
    @Log(title = "物料主数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProMaterialData supplierProMaterialData) {
        return toAjax(supplierProMaterialDataService.insertSupplierProMaterialData(supplierProMaterialData));
    }

    /**
     * 修改物料主数据
     */
    //@RequiresPermissions("edi:materialData:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProMaterialData supplierProMaterialData = supplierProMaterialDataService.selectSupplierProMaterialDataById(id);
        mmap.put("supplierProMaterialData", supplierProMaterialData);
        return prefix + "/edit";
    }

    /**
     * 修改保存物料主数据
     */
    //@RequiresPermissions("edi:materialData:edit")
    @Log(title = "物料主数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProMaterialData supplierProMaterialData) {
        return toAjax(supplierProMaterialDataService.updateSupplierProMaterialData(supplierProMaterialData));
    }

    /**
     * 删除物料主数据
     */
    //@RequiresPermissions("edi:materialData:remove")
    @Log(title = "物料主数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProMaterialDataService.deleteSupplierProMaterialDataByIds(ids));
    }

}
