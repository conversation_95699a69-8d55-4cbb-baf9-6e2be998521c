package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierMrpWarning;
import com.hj.admin.service.pull.ISupplierMrpWarningService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日MRP预警推移 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/mrpWarning")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierMrpWarningController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierMrpWarningService supplierMrpWarningService;

    //@RequiresPermissions("edi:mrpWarning:view")
    @GetMapping()
    public String warning()
    {
        return prefix + "/mrpWarning";
    }

    /**
     * 查询日MRP预警推移列表
     */
    //@RequiresPermissions("edi:mrpWarning:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierMrpWarning supplierMrpWarning)
    {
        startPage();
        List<SupplierMrpWarning> list = supplierMrpWarningService.selectSupplierMrpWarningList(supplierMrpWarning);
        return getDataTable(list);
    }

    /**
     * 导出日MRP预警推移列表
     */
    //@RequiresPermissions("edi:mrpWarning:export")
    @Log(title = "日MRP预警推移", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierMrpWarning supplierMrpWarning)
    {
        List<SupplierMrpWarning> list = supplierMrpWarningService.selectSupplierMrpWarningList(supplierMrpWarning);
        ExcelUtil<SupplierMrpWarning> util = new ExcelUtil<SupplierMrpWarning>(SupplierMrpWarning.class);
        return util.exportExcel(list, "日MRP预警推移数据");
    }

    /**
     * 新增日MRP预警推移
     */
    //@RequiresPermissions("edi:mrpWarning:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存日MRP预警推移
     */
    //@RequiresPermissions("edi:mrpWarning:add")
    @Log(title = "日MRP预警推移", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierMrpWarning supplierMrpWarning)
    {
        return toAjax(supplierMrpWarningService.insertSupplierMrpWarning(supplierMrpWarning));
    }

    /**
     * 修改日MRP预警推移
     */
    //@RequiresPermissions("edi:mrpWarning:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierMrpWarning supplierMrpWarning = supplierMrpWarningService.selectSupplierMrpWarningById(id);
        mmap.put("supplierMrpWarning", supplierMrpWarning);
        return prefix + "/edit";
    }

    /**
     * 修改保存日MRP预警推移
     */
    //@RequiresPermissions("edi:mrpWarning:edit")
    @Log(title = "日MRP预警推移", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierMrpWarning supplierMrpWarning)
    {
        return toAjax(supplierMrpWarningService.updateSupplierMrpWarning(supplierMrpWarning));
    }

    /**
     * 删除日MRP预警推移
     */
    //@RequiresPermissions("edi:mrpWarning:remove")
    @Log(title = "日MRP预警推移", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierMrpWarningService.deleteSupplierMrpWarningByIds(ids));
    }
}
