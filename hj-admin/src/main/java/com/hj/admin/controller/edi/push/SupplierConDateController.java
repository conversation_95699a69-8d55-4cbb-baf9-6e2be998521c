package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierConDate;
import com.hj.admin.service.push.ISupplierConDateService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 日物料需求计划风险确认 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/conDate")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierConDateController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierConDateService supplierConDateService;

    //@RequiresPermissions("edi:conDate:view")
    @GetMapping()
    public String date() {
        return prefix + "/conDate";
    }

    /**
     * 查询日物料需求计划风险确认列表
     */
    //@RequiresPermissions("edi:conDate:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierConDate supplierConDate) {
        startPage();
        List<SupplierConDate> list = supplierConDateService.selectSupplierConDateList(supplierConDate);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出日物料需求计划风险确认列表
     */
    //@RequiresPermissions("edi:conDate:export")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierConDate supplierConDate) {
        List<SupplierConDate> list = supplierConDateService.selectSupplierConDateList(supplierConDate);
        ExcelUtil<SupplierConDate> util = new ExcelUtil<SupplierConDate>(SupplierConDate.class);
        return util.exportExcel(list, "日物料需求计划风险确认数据");
    }

    /**
     * 新增日物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conDate:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存日物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conDate:add")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierConDate supplierConDate) {
        return toAjax(supplierConDateService.insertSupplierConDate(supplierConDate));
    }

    /**
     * 修改日物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conDate:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierConDate supplierConDate = supplierConDateService.selectSupplierConDateById(id);
        mmap.put("supplierConDate", supplierConDate);
        return prefix + "/edit";
    }

    /**
     * 修改保存日物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conDate:edit")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierConDate supplierConDate) {
        return toAjax(supplierConDateService.updateSupplierConDate(supplierConDate));
    }

    /**
     * 删除日物料需求计划风险确认
     */
    //@RequiresPermissions("edi:conDate:remove")
    @Log(title = "日物料需求计划风险确认", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierConDateService.deleteSupplierConDateByIds(ids));
    }

}
