package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierSinvData;
import com.hj.admin.service.push.ISupplierSinvDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 供应商共享库存 Controller
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Controller
@RequestMapping("/edi/push/sinvData")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierSinvDataController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierSinvDataService supplierSinvDataService;

    //@RequiresPermissions("sinv:data:view")
    @GetMapping()
    public String data() {
        return prefix + "/sinvData";
    }

    /**
     * 查询供应商共享库存列表
     */
    //@RequiresPermissions("sinv:data:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierSinvData supplierSinvData) {
        startPage();
        List<SupplierSinvData> list = supplierSinvDataService.selectSupplierSinvDataList(supplierSinvData);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出供应商共享库存列表
     */
    //@RequiresPermissions("sinv:data:export")
    @Log(title = "供应商共享库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierSinvData supplierSinvData) {
        List<SupplierSinvData> list = supplierSinvDataService.selectSupplierSinvDataList(supplierSinvData);
        ExcelUtil<SupplierSinvData> util = new ExcelUtil<SupplierSinvData>(SupplierSinvData.class);
        return util.exportExcel(list, "供应商共享库存数据");
    }

    /**
     * 新增供应商共享库存
     */
    //@RequiresPermissions("sinv:data:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存供应商共享库存
     */
    //@RequiresPermissions("sinv:data:add")
    @Log(title = "供应商共享库存", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierSinvData supplierSinvData) {
        return toAjax(supplierSinvDataService.insertSupplierSinvData(supplierSinvData));
    }

    /**
     * 修改供应商共享库存
     */
    //@RequiresPermissions("sinv:data:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierSinvData supplierSinvData = supplierSinvDataService.selectSupplierSinvDataById(id);
        mmap.put("supplierSinvData", supplierSinvData);
        return prefix + "/edit";
    }

    /**
     * 修改保存供应商共享库存
     */
    //@RequiresPermissions("sinv:data:edit")
    @Log(title = "供应商共享库存", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierSinvData supplierSinvData) {
        return toAjax(supplierSinvDataService.updateSupplierSinvData(supplierSinvData));
    }

    /**
     * 删除供应商共享库存
     */
    //@RequiresPermissions("sinv:data:remove")
    @Log(title = "供应商共享库存", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierSinvDataService.deleteSupplierSinvDataByIds(ids));
    }

}
