package com.hj.admin.controller.edi.pull;

import com.hj.admin.domain.pull.SupplierPo;
import com.hj.admin.service.pull.ISupplierPoService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购订单 Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/pull/po")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierPoController extends BaseController
{
    private final String prefix = "edi/pull";

    @Resource
    private ISupplierPoService supplierPoService;

    //@RequiresPermissions("edi:po:view")
    @GetMapping()
    public String po()
    {
        return prefix + "/po";
    }

    /**
     * 查询采购订单列表
     */
    //@RequiresPermissions("edi:po:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierPo supplierPo)
    {
        startPage();
        List<SupplierPo> list = supplierPoService.selectSupplierPoList(supplierPo);
        return getDataTable(list);
    }

    /**
     * 导出采购订单列表
     */
    //@RequiresPermissions("edi:po:export")
    @Log(title = "采购订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierPo supplierPo)
    {
        List<SupplierPo> list = supplierPoService.selectSupplierPoList(supplierPo);
        ExcelUtil<SupplierPo> util = new ExcelUtil<SupplierPo>(SupplierPo.class);
        return util.exportExcel(list, "采购订单数据");
    }

    /**
     * 新增采购订单
     */
    //@RequiresPermissions("edi:po:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存采购订单
     */
    //@RequiresPermissions("edi:po:add")
    @Log(title = "采购订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierPo supplierPo)
    {
        return toAjax(supplierPoService.insertSupplierPo(supplierPo));
    }

    /**
     * 修改采购订单
     */
    //@RequiresPermissions("edi:po:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierPo supplierPo = supplierPoService.selectSupplierPoById(id);
        mmap.put("supplierPo", supplierPo);
        return prefix + "/edit";
    }

    /**
     * 修改保存采购订单
     */
    //@RequiresPermissions("edi:po:edit")
    @Log(title = "采购订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierPo supplierPo)
    {
        return toAjax(supplierPoService.updateSupplierPo(supplierPo));
    }

    /**
     * 删除采购订单
     */
    //@RequiresPermissions("edi:po:remove")
    @Log(title = "采购订单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierPoService.deleteSupplierPoByIds(ids));
    }
}
