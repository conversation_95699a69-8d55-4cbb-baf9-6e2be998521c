package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProData;
import com.hj.admin.service.push.ISupplierProDataService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 生产过程数据 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/proData")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProDataController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProDataService supplierProDataService;

    //@RequiresPermissions("edi:proData:view")
    @GetMapping()
    public String data() {
        return prefix + "/proData";
    }

    /**
     * 查询生产过程数据列表
     */
    //@RequiresPermissions("edi:proData:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProData supplierProData) {
        startPage();
        List<SupplierProData> list = supplierProDataService.selectSupplierProDataList(supplierProData);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出生产过程数据列表
     */
    //@RequiresPermissions("edi:proData:export")
    @Log(title = "生产过程数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProData supplierProData) {
        List<SupplierProData> list = supplierProDataService.selectSupplierProDataList(supplierProData);
        ExcelUtil<SupplierProData> util = new ExcelUtil<SupplierProData>(SupplierProData.class);
        return util.exportExcel(list, "生产过程数据数据");
    }

    /**
     * 新增生产过程数据
     */
    //@RequiresPermissions("edi:proData:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存生产过程数据
     */
    //@RequiresPermissions("edi:proData:add")
    @Log(title = "生产过程数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProData supplierProData) {
        return toAjax(supplierProDataService.insertSupplierProData(supplierProData));
    }

    /**
     * 修改生产过程数据
     */
    //@RequiresPermissions("edi:proData:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProData supplierProData = supplierProDataService.selectSupplierProDataById(id);
        mmap.put("supplierProData", supplierProData);
        return prefix + "/edit";
    }

    /**
     * 修改保存生产过程数据
     */
    //@RequiresPermissions("edi:proData:edit")
    @Log(title = "生产过程数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProData supplierProData) {
        return toAjax(supplierProDataService.updateSupplierProData(supplierProData));
    }

    /**
     * 删除生产过程数据
     */
    //@RequiresPermissions("edi:proData:remove")
    @Log(title = "生产过程数据", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProDataService.deleteSupplierProDataByIds(ids));
    }

}
