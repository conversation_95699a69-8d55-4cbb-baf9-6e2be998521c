package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProOeeAchievementRate;
import com.hj.admin.service.push.ISupplierProOeeAchievementRateService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 设备OEE达成率 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/oeeAchievementRate")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProOeeAchievementRateController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProOeeAchievementRateService supplierProOeeAchievementRateService;

    //@RequiresPermissions("edi:oeeAchievementRate:view")
    @GetMapping()
    public String rate() {
        return prefix + "/oeeAchievementRate";
    }

    /**
     * 查询设备OEE达成率列表
     */
    //@RequiresPermissions("edi:oeeAchievementRate:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProOeeAchievementRate supplierProOeeAchievementRate) {
        startPage();
        List<SupplierProOeeAchievementRate> list = supplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateList(supplierProOeeAchievementRate);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出设备OEE达成率列表
     */
    //@RequiresPermissions("edi:oeeAchievementRate:export")
    @Log(title = "设备OEE达成率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProOeeAchievementRate supplierProOeeAchievementRate) {
        List<SupplierProOeeAchievementRate> list = supplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateList(supplierProOeeAchievementRate);
        ExcelUtil<SupplierProOeeAchievementRate> util = new ExcelUtil<SupplierProOeeAchievementRate>(SupplierProOeeAchievementRate.class);
        return util.exportExcel(list, "设备OEE达成率数据");
    }

    /**
     * 新增设备OEE达成率
     */
    //@RequiresPermissions("edi:oeeAchievementRate:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存设备OEE达成率
     */
    //@RequiresPermissions("edi:oeeAchievementRate:add")
    @Log(title = "设备OEE达成率", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProOeeAchievementRate supplierProOeeAchievementRate) {
        return toAjax(supplierProOeeAchievementRateService.insertSupplierProOeeAchievementRate(supplierProOeeAchievementRate));
    }

    /**
     * 修改设备OEE达成率
     */
    //@RequiresPermissions("edi:oeeAchievementRate:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProOeeAchievementRate supplierProOeeAchievementRate = supplierProOeeAchievementRateService.selectSupplierProOeeAchievementRateById(id);
        mmap.put("supplierProOeeAchievementRate", supplierProOeeAchievementRate);
        return prefix + "/edit";
    }

    /**
     * 修改保存设备OEE达成率
     */
    //@RequiresPermissions("edi:oeeAchievementRate:edit")
    @Log(title = "设备OEE达成率", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProOeeAchievementRate supplierProOeeAchievementRate) {
        return toAjax(supplierProOeeAchievementRateService.updateSupplierProOeeAchievementRate(supplierProOeeAchievementRate));
    }

    /**
     * 删除设备OEE达成率
     */
    //@RequiresPermissions("edi:oeeAchievementRate:remove")
    @Log(title = "设备OEE达成率", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProOeeAchievementRateService.deleteSupplierProOeeAchievementRateByIds(ids));
    }

}
