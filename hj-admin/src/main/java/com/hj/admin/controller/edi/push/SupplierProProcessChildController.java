package com.hj.admin.controller.edi.push;

import com.hj.admin.domain.push.SupplierProProcessChild;
import com.hj.admin.service.push.ISupplierProProcessChildService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工艺子集（关联supplier_pro_process）Controller
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/proProcessChild")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProProcessChildController extends BaseController
{
    private String prefix = "edi/proProcessChild";

    @Resource
    private ISupplierProProcessChildService supplierProProcessChildService;

    //@RequiresPermissions("edi:child:view")
    @GetMapping()
    public String child()
    {
        return prefix + "/child";
    }

    /**
     * 查询工艺子集（关联supplier_pro_process）列表
     */
    //@RequiresPermissions("edi:child:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProProcessChild supplierProProcessChild)
    {
        startPage();
        List<SupplierProProcessChild> list = supplierProProcessChildService.selectSupplierProProcessChildList(supplierProProcessChild);
        return getDataTable(list);
    }

    /**
     * 导出工艺子集（关联supplier_pro_process）列表
     */
    //@RequiresPermissions("edi:child:export")
    @Log(title = "工艺子集（关联supplier_pro_process）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProProcessChild supplierProProcessChild)
    {
        List<SupplierProProcessChild> list = supplierProProcessChildService.selectSupplierProProcessChildList(supplierProProcessChild);
        ExcelUtil<SupplierProProcessChild> util = new ExcelUtil<SupplierProProcessChild>(SupplierProProcessChild.class);
        return util.exportExcel(list, "工艺子集（关联supplier_pro_process）数据");
    }

    /**
     * 新增工艺子集（关联supplier_pro_process）
     */
    //@RequiresPermissions("edi:child:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存工艺子集（关联supplier_pro_process）
     */
    //@RequiresPermissions("edi:child:add")
    @Log(title = "工艺子集（关联supplier_pro_process）", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProProcessChild supplierProProcessChild)
    {
        return toAjax(supplierProProcessChildService.insertSupplierProProcessChild(supplierProProcessChild));
    }

    /**
     * 修改工艺子集（关联supplier_pro_process）
     */
    //@RequiresPermissions("edi:child:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SupplierProProcessChild supplierProProcessChild = supplierProProcessChildService.selectSupplierProProcessChildById(id);
        mmap.put("supplierProProcessChild", supplierProProcessChild);
        return prefix + "/edit";
    }

    /**
     * 修改保存工艺子集（关联supplier_pro_process）
     */
    //@RequiresPermissions("edi:child:edit")
    @Log(title = "工艺子集（关联supplier_pro_process）", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProProcessChild supplierProProcessChild)
    {
        return toAjax(supplierProProcessChildService.updateSupplierProProcessChild(supplierProProcessChild));
    }

    /**
     * 删除工艺子集（关联supplier_pro_process）
     */
    //@RequiresPermissions("edi:child:remove")
    @Log(title = "工艺子集（关联supplier_pro_process）", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(supplierProProcessChildService.deleteSupplierProProcessChildByIds(ids));
    }
}
