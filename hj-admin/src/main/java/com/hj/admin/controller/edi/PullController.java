package com.hj.admin.controller.edi;

import com.hj.admin.task.EdiScheduleTask;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("pull")
public class PullController {

    @Resource
    private EdiScheduleTask ediScheduleTask;

    @PostMapping("pullSupplierDelState")
    public void pullSupplierDelState() throws Exception {
        ediScheduleTask.pullSupplierDelState();
    }

    @PostMapping("pullSupplierInvData")
    public void pullSupplierInvData() throws Exception {
        ediScheduleTask.pullSupplierInvData();
    }

    @PostMapping("pullSupplierMrpDate")
    public void pullSupplierMrpDate() throws Exception {
        ediScheduleTask.pullSupplierMrpDate();
    }

    @PostMapping("pullSupplierMrpMonth")
    public void pullSupplierMrpMonth() throws Exception {
        ediScheduleTask.pullSupplierMrpMonth();
    }

    @PostMapping("pullSupplierMrpState")
    public void pullSupplierMrpState() throws Exception {
        ediScheduleTask.pullSupplierMrpState();
    }

    @PostMapping("pullSupplierMrpWarning")
    public void pullSupplierMrpWarning() throws Exception {
        ediScheduleTask.pullSupplierMrpWarning();
    }

    @PostMapping("pullSupplierPo")
    public void pullSupplierPo() throws Exception {
        ediScheduleTask.pullSupplierPo();
    }

    @PostMapping("pullSupplierProCschedul")
    public void pullSupplierProCschedul() throws Exception {
        ediScheduleTask.pullSupplierProCschedul();
    }

    @PostMapping("pullSupplierProHschedul")
    public void pullSupplierProHschedul() throws Exception {
        ediScheduleTask.pullSupplierProHschedul();
    }

    @PostMapping("pullSupplierProPlaning")
    public void pullSupplierProPlaning() throws Exception {
        ediScheduleTask.pullSupplierProPlaning();
    }

    @PostMapping("pullSupplierProTschedul")
    public void pullSupplierProTschedul() throws Exception {
        ediScheduleTask.pullSupplierProTschedul();
    }

    @PostMapping("pullSupplierReturn")
    public void pullSupplierReturn() throws Exception {
        ediScheduleTask.pullSupplierReturn();
    }

    @PostMapping("pullSupplierSaWeek")
    public void pullSupplierSaWeek() throws Exception {
        ediScheduleTask.pullSupplierSaWeek();
    }


}
