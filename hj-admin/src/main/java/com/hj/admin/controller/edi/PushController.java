package com.hj.admin.controller.edi;

import com.hj.admin.domain.EdiPushFieldConfirm;
import com.hj.admin.enums.InterfaceEnum;
import com.hj.admin.mapper.push.PushBaseMapper;
import com.hj.admin.service.IEdiPushFieldConfirmService;
import com.hj.admin.task.EdiScheduleTask;
import com.hj.common.utils.ShiroUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("push")
public class PushController {

    @Resource
    private EdiScheduleTask ediScheduleTask;

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierBom")
    public void directlyPushSupplierBom(String batchNo) {
        ediScheduleTask.directlyPushSupplierBom(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierConDate")
    public void directlyPushSupplierConDate(String batchNo) {
        ediScheduleTask.directlyPushSupplierConDate(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierConMmrp")
    public void directlyPushSupplierConMmrp(String batchNo) {
        ediScheduleTask.directlyPushSupplierConMmrp(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierConPo")
    public void directlyPushSupplierConPo(String batchNo) {
        ediScheduleTask.directlyPushSupplierConPo(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierEmployee")
    public void directlyPushSupplierEmployee(String batchNo) {
        ediScheduleTask.directlyPushSupplierEmployee(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierInfo")
    public void directlyPushSupplierInfo(String batchNo) {
        ediScheduleTask.directlyPushSupplierInfo(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProAttachmentData")
    public void directlyPushSupplierProAttachmentData(String batchNo) {
        ediScheduleTask.directlyPushSupplierProAttachmentData(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProCps")
    public void directlyPushSupplierProCps(String batchNo) {
        ediScheduleTask.directlyPushSupplierProCps(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProData")
    public void directlyPushSupplierProData(String batchNo) {
        ediScheduleTask.directlyPushSupplierProData(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProEnvironment")
    public void directlyPushSupplierProEnvironment(String batchNo) {
        ediScheduleTask.directlyPushSupplierProEnvironment(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProFirstPassyield")
    public void directlyPushSupplierProFirstPassyield(String batchNo) {
        ediScheduleTask.directlyPushSupplierProFirstPassyield(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProFlaw")
    public void directlyPushSupplierProFlaw(String batchNo) {
        ediScheduleTask.directlyPushSupplierProFlaw(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProMaterialData")
    public void directlyPushSupplierProMaterialData(String batchNo) {
        ediScheduleTask.directlyPushSupplierProMaterialData(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProMaterialStock")
    public void directlyPushSupplierProMaterialStock(String batchNo) {
        ediScheduleTask.directlyPushSupplierProMaterialStock(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProOeeAchievementRate")
    public void directlyPushSupplierProOeeAchievementRate(String batchNo) {
        ediScheduleTask.directlyPushSupplierProOeeAchievementRate(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProOeeTimeDetails")
    public void directlyPushSupplierProOeeTimeDetails(String batchNo) {
        ediScheduleTask.directlyPushSupplierProOeeTimeDetails(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProProcess")
    public void directlyPushSupplierProProcess(String batchNo) {
        ediScheduleTask.directlyPushSupplierProProcess(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProProcessEquipment")
    public void directlyPushSupplierProProcessEquipment(String batchNo) {
        ediScheduleTask.directlyPushSupplierProProcessEquipment(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProScheduling")
    public void directlyPushSupplierProScheduling(String batchNo) {
        ediScheduleTask.directlyPushSupplierProScheduling(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierProStationFirstPassyield")
    public void directlyPushSupplierProStationFirstPassyield(String batchNo) {
        ediScheduleTask.directlyPushSupplierProStationFirstPassyield(batchNo);
    }

    @RequiresRoles(value = {"function", "admin"}, logical = Logical.OR)
    @PostMapping("directlyPushSupplierSinvData")
    public void directlyPushSupplierSinvData(String batchNo) {
        ediScheduleTask.directlyPushSupplierSinvData(batchNo);
    }

    // ——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————


    @PostMapping("pushSupplierBom")
    public void pushSupplierBom() {
        ediScheduleTask.pushSupplierBom();
    }

    @PostMapping("pushSupplierConDate")
    public void pushSupplierConDate() {
        ediScheduleTask.pushSupplierConDate();
    }

    @PostMapping("pushSupplierConMmrp")
    public void pushSupplierConMmrp() {
        ediScheduleTask.pushSupplierConMmrp();
    }

    @PostMapping("pushSupplierConPo")
    public void pushSupplierConPo() {
        ediScheduleTask.pushSupplierConPo();
    }

    @PostMapping("pushSupplierEmployee")
    public void pushSupplierEmployee() {
        ediScheduleTask.pushSupplierEmployee();
    }

    @PostMapping("pushSupplierInfo")
    public void pushSupplierInfo() {
        ediScheduleTask.pushSupplierInfo();
    }

    @PostMapping("pushSupplierProAttachmentData")
    public void pushSupplierProAttachmentData() {
        ediScheduleTask.pushSupplierProAttachmentData();
    }

    @PostMapping("pushSupplierProCps")
    public void pushSupplierProCps() {
        ediScheduleTask.pushSupplierProCps();
    }

    @PostMapping("pushSupplierProData")
    public void pushSupplierProData() {
        ediScheduleTask.pushSupplierProData();
    }

    @PostMapping("pushSupplierProEnvironment")
    public void pushSupplierProEnvironment() {
        ediScheduleTask.pushSupplierProEnvironment();
    }

    @PostMapping("pushSupplierProFirstPassyield")
    public void pushSupplierProFirstPassyield() {
        ediScheduleTask.pushSupplierProFirstPassyield();
    }

    @PostMapping("pushSupplierProFlaw")
    public void pushSupplierProFlaw() {
        ediScheduleTask.pushSupplierProFlaw();
    }

    @PostMapping("pushSupplierProMaterialData")
    public void pushSupplierProMaterialData() {
        ediScheduleTask.pushSupplierProMaterialData();
    }

    @PostMapping("pushSupplierProMaterialStock")
    public void pushSupplierProMaterialStock() {
        ediScheduleTask.pushSupplierProMaterialStock();
    }

    @PostMapping("pushSupplierProOeeAchievementRate")
    public void pushSupplierProOeeAchievementRate() {
        ediScheduleTask.pushSupplierProOeeAchievementRate();
    }

    @PostMapping("pushSupplierProOeeTimeDetails")
    public void pushSupplierProOeeTimeDetails() {
        ediScheduleTask.pushSupplierProOeeTimeDetails();
    }

    @PostMapping("pushSupplierProProcess")
    public void pushSupplierProProcess() {
        ediScheduleTask.pushSupplierProProcess();
    }

    @PostMapping("pushSupplierProProcessEquipment")
    public void pushSupplierProProcessEquipment() {
        ediScheduleTask.pushSupplierProProcessEquipment();
    }

    @PostMapping("pushSupplierProScheduling")
    public void pushSupplierProScheduling() {
        ediScheduleTask.pushSupplierProScheduling();
    }

    @PostMapping("pushSupplierProStationFirstPassyield")
    public void pushSupplierProStationFirstPassyield() {
        ediScheduleTask.pushSupplierProStationFirstPassyield();
    }

    @PostMapping("pushSupplierSinvData")
    public void pushSupplierSinvData() {
        ediScheduleTask.pushSupplierSinvData();
    }

    @GetMapping("allInterface")
    public Map<String, String> allInterface() {
        InterfaceEnum[] values = InterfaceEnum.values();
        return Arrays.stream(values).collect(Collectors.toMap(InterfaceEnum::getCode, InterfaceEnum::getName));
    }

    @GetMapping("allInterfaceField")
    public Map<String, List<String>> allInterfaceField() {
        Map<String, List<String>> map = new HashMap<>();
        InterfaceEnum[] values = InterfaceEnum.values();
        for (InterfaceEnum value : values) {
            map.put(value.getCode(), Arrays.asList(value.getFields().split(",")));
        }
        return map;
    }

    @Resource
    private PushBaseMapper pushBaseMapper;
    @Resource
    private IEdiPushFieldConfirmService iEdiPushFieldConfirmService;

    @GetMapping("/listAllBatchNo")
    @ResponseBody
    public Set<String> listAllBatchNo(String tableName, Integer type) {
        List<Map<String, Object>> list = pushBaseMapper.listUnPushData(tableName);
        if (list.isEmpty()) {
            return new HashSet<>();
        }
        if (type == 1) {
            // 查询未确认字段的批次号
            Long userId = ShiroUtils.getUserId();
            EdiPushFieldConfirm ediPushFieldConfirm = new EdiPushFieldConfirm();
            ediPushFieldConfirm.setFieldMaintainerId(userId);
            List<String> confirmedBatchNo = iEdiPushFieldConfirmService.selectEdiPushFieldConfirmList(ediPushFieldConfirm).stream().map(EdiPushFieldConfirm::getBatchNo).collect(Collectors.toList());
            return list.stream().filter(s -> !(Boolean) s.get("field_has_confirm") || !confirmedBatchNo.contains((String) s.get("out_batch_no"))).sorted(Comparator.comparing(s -> ((Date) s.get("create_time")), Comparator.nullsLast(Comparator.reverseOrder()))).map(s -> (String) s.get("out_batch_no")).collect(Collectors.toSet());
        } else if (type == 2) {
            //查询未确认接口的批次号
            return list.stream().filter(s -> !(Boolean) s.get("interface_has_confirm")).sorted(Comparator.comparing(s -> ((Date) s.get("create_time")), Comparator.nullsLast(Comparator.reverseOrder()))).map(s -> (String) s.get("out_batch_no")).collect(Collectors.toSet());
        } else {
            //查询未确认功能的批次号
            return list.stream().filter(s -> !(Boolean) s.get("function_has_confirm")).sorted(Comparator.comparing(s -> ((Date) s.get("create_time")), Comparator.nullsLast(Comparator.reverseOrder()))).map(s -> (String) s.get("out_batch_no")).collect(Collectors.toSet());
        }
    }

}
