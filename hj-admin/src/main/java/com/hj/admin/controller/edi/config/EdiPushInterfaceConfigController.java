package com.hj.admin.controller.edi.config;

import com.hj.admin.domain.config.EdiPushFieldConfig;
import com.hj.admin.domain.config.EdiPushInterfaceConfig;
import com.hj.admin.service.IEdiPushFieldConfigService;
import com.hj.admin.service.IEdiPushInterfaceConfigService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.ShiroUtils;
import com.hj.common.utils.poi.ExcelUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Push接口配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Controller
@RequestMapping("/edi/pushInterfaceConfig")
public class EdiPushInterfaceConfigController extends BaseController
{
    private final String prefix = "edi/config";

    @Resource
    private IEdiPushFieldConfigService iEdiPushFieldConfigService;
    @Resource
    private IEdiPushInterfaceConfigService iEdiPushInterfaceConfigService;

    //@RequiresPermissions("edi:interfaceConfig:view")
    @GetMapping()
    public String config()
    {
        return prefix + "/pushInterfaceConfig";
    }

    /**
     * 查询Push接口配置列表
     */
    //@RequiresPermissions("edi:interfaceConfig:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        startPage();
        List<EdiPushInterfaceConfig> list = iEdiPushInterfaceConfigService.selectEdiPushInterfaceConfigList(ediPushInterfaceConfig);
        return getDataTable(list);
    }

    /**
     * 导出Push接口配置列表
     */
    //@RequiresPermissions("edi:interfaceConfig:export")
    @Log(title = "Push接口配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        List<EdiPushInterfaceConfig> list = iEdiPushInterfaceConfigService.selectEdiPushInterfaceConfigList(ediPushInterfaceConfig);
        ExcelUtil<EdiPushInterfaceConfig> util = new ExcelUtil<EdiPushInterfaceConfig>(EdiPushInterfaceConfig.class);
        return util.exportExcel(list, "Push接口配置数据");
    }

    /**
     * 新增Push接口配置
     */
    //@RequiresPermissions("edi:interfaceConfig:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/pushInterfaceConfigAdd";
    }

    /**
     * 新增保存Push接口配置
     */
    //@RequiresPermissions("edi:interfaceConfig:add")
    @Log(title = "Push接口配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        return toAjax(iEdiPushInterfaceConfigService.insertEdiPushInterfaceConfig(ediPushInterfaceConfig));
    }

    /**
     * 修改Push接口配置
     */
    //@RequiresPermissions("edi:interfaceConfig:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        EdiPushInterfaceConfig ediPushInterfaceConfig = iEdiPushInterfaceConfigService.selectEdiPushInterfaceConfigById(id);
        mmap.put("ediPushInterfaceConfig", ediPushInterfaceConfig);
        return prefix + "/pushInterfaceConfigEdit";
    }

    /**
     * 修改保存Push接口配置
     */
    //@RequiresPermissions("edi:interfaceConfig:edit")
    @Log(title = "Push接口配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(EdiPushInterfaceConfig ediPushInterfaceConfig)
    {
        return toAjax(iEdiPushInterfaceConfigService.updateEdiPushInterfaceConfig(ediPushInterfaceConfig));
    }

    /**
     * 删除Push接口配置
     */
    //@RequiresPermissions("edi:interfaceConfig:remove")
    @Log(title = "Push接口配置", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(iEdiPushInterfaceConfigService.deleteEdiPushInterfaceConfigByIds(ids));
    }

    @PostMapping("/listFieldInterfaceForCurrentUser")
    @ResponseBody
    public List<String> listFieldInterfaceForCurrentUser()
    {
        EdiPushFieldConfig fieldConfig = new EdiPushFieldConfig();
        fieldConfig.setFieldMaintainerId(ShiroUtils.getUserId());
        List<EdiPushFieldConfig> fieldConfigs = iEdiPushFieldConfigService.selectEdiPushFieldConfigList(fieldConfig);
        List<Long> interfaceIds = fieldConfigs.stream().map(EdiPushFieldConfig::getInterfaceConfigId).collect(Collectors.toList());
        return iEdiPushInterfaceConfigService.selectInterfaceNameByIds(interfaceIds);
    }

    @PostMapping("/listInterfaceForCurrentUser")
    @ResponseBody
    public List<String> listInterfaceForCurrentUser()
    {
        EdiPushInterfaceConfig ediPushInterfaceConfig = new EdiPushInterfaceConfig();
        ediPushInterfaceConfig.setInterfaceMaintainerId(ShiroUtils.getUserId());
        List<EdiPushInterfaceConfig> list = iEdiPushInterfaceConfigService.selectEdiPushInterfaceConfigList(ediPushInterfaceConfig);
        return list.stream().map(EdiPushInterfaceConfig::getInterfaceName).collect(Collectors.toList());
    }

    @PostMapping("/listFunctionForCurrentUser")
    @ResponseBody
    public List<String> listFunctionForCurrentUser()
    {
        EdiPushInterfaceConfig ediPushInterfaceConfig = new EdiPushInterfaceConfig();
        ediPushInterfaceConfig.setFunctionMaintainerId(ShiroUtils.getUserId());
        List<EdiPushInterfaceConfig> list = iEdiPushInterfaceConfigService.selectEdiPushInterfaceConfigList(ediPushInterfaceConfig);
        return list.stream().map(EdiPushInterfaceConfig::getInterfaceName).collect(Collectors.toList());
    }
}
