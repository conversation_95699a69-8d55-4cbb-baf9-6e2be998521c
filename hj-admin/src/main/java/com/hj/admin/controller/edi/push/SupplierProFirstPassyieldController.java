package com.hj.admin.controller.edi.push;

import com.alibaba.fastjson.JSON;
import com.hj.admin.domain.push.SupplierProFirstPassyield;
import com.hj.admin.service.push.ISupplierProFirstPassyieldService;
import com.hj.common.annotation.Log;
import com.hj.common.core.controller.BaseController;
import com.hj.common.core.domain.AjaxResult;
import com.hj.common.core.page.TableDataInfo;
import com.hj.common.enums.BusinessType;
import com.hj.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 产品一次合格率 Controller
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@Controller
@RequestMapping("/edi/push/firstPassyield")
@RequiresRoles(value = {"function", "interface", "field", "admin"}, logical = Logical.OR)
public class SupplierProFirstPassyieldController extends BaseController {
    private final String prefix = "edi/push";

    @Resource
    private ISupplierProFirstPassyieldService supplierProFirstPassyieldService;

    //@RequiresPermissions("edi:firstPassyield:view")
    @GetMapping()
    public String passyield() {
        return prefix + "/firstPassyield";
    }

    /**
     * 查询产品一次合格率列表
     */
    //@RequiresPermissions("edi:firstPassyield:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SupplierProFirstPassyield supplierProFirstPassyield) {
        startPage();
        List<SupplierProFirstPassyield> list = supplierProFirstPassyieldService.selectSupplierProFirstPassyieldList(supplierProFirstPassyield);
        return getDataTable(JSON.parseArray(JSON.toJSONString(list), Map.class));
    }

    /**
     * 导出产品一次合格率列表
     */
    //@RequiresPermissions("edi:firstPassyield:export")
    @Log(title = "产品一次合格率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SupplierProFirstPassyield supplierProFirstPassyield) {
        List<SupplierProFirstPassyield> list = supplierProFirstPassyieldService.selectSupplierProFirstPassyieldList(supplierProFirstPassyield);
        ExcelUtil<SupplierProFirstPassyield> util = new ExcelUtil<SupplierProFirstPassyield>(SupplierProFirstPassyield.class);
        return util.exportExcel(list, "产品一次合格率数据");
    }

    /**
     * 新增产品一次合格率
     */
    //@RequiresPermissions("edi:firstPassyield:add")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存产品一次合格率
     */
    //@RequiresPermissions("edi:firstPassyield:add")
    @Log(title = "产品一次合格率", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SupplierProFirstPassyield supplierProFirstPassyield) {
        return toAjax(supplierProFirstPassyieldService.insertSupplierProFirstPassyield(supplierProFirstPassyield));
    }

    /**
     * 修改产品一次合格率
     */
    //@RequiresPermissions("edi:firstPassyield:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        SupplierProFirstPassyield supplierProFirstPassyield = supplierProFirstPassyieldService.selectSupplierProFirstPassyieldById(id);
        mmap.put("supplierProFirstPassyield", supplierProFirstPassyield);
        return prefix + "/edit";
    }

    /**
     * 修改保存产品一次合格率
     */
    //@RequiresPermissions("edi:firstPassyield:edit")
    @Log(title = "产品一次合格率", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SupplierProFirstPassyield supplierProFirstPassyield) {
        return toAjax(supplierProFirstPassyieldService.updateSupplierProFirstPassyield(supplierProFirstPassyield));
    }

    /**
     * 删除产品一次合格率
     */
    //@RequiresPermissions("edi:firstPassyield:remove")
    @Log(title = "产品一次合格率", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(supplierProFirstPassyieldService.deleteSupplierProFirstPassyieldByIds(ids));
    }

}
