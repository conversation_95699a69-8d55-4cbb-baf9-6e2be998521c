package com.hj.admin.enums;

import lombok.Getter;

@Getter
public enum InterfaceEnum {
    SUPPLIER_BOM("supplier_bom", "BOM主数据", "supplierCode,supplierName,bomCode,bomName,bomVersion,cheryProductNo,cheryProductName,vendorProductNo,vendorProductName,vendorProductType,materialUnit,subMaterialCode,subMaterialName,subMaterialType,subMaterialUnit,subMaterialQuota,dataUpdateTime"),
    SUPPLIER_CON_DATE("supplier_con_date", "日物料需求计划风险确认", "supplierCode,releaseEdition,materialCode,plantId,feedbackResults,ventureType,ventureSpecific,measures,startMonth,quantityMeet1,quantityMeet2,quantityMeet3,quantityMeet4,quantityMeet5,quantityMeet6,quantityMeet7,quantityMeet8,quantityMeet9,quantityMeet10,quantityMeet11,quantityMeet12,quantityMeet13,quantityMeet14,quantityMeet15,quantityMeet16,quantityMeet17,quantityMeet18,quantityMeet19,quantityMeet20,quantityMeet21,quantityMeet22,quantityMeet23,quantityMeet24,quantityMeet25,quantityMeet26,quantityMeet27,quantityMeet28,quantityMeet29,quantityMeet30,quantityMeet31"),
    SUPPLIER_CON_MMRP("supplier_con_mmrp", "M+6月物料需求计划风险确认", "supplierCode,releaseEdition,materialCode,plantId,feedbackResults,ventureType,ventureSpecific,measures,startMonth,quantityMeet1,quantityMeet2,quantityMeet3,quantityMeet4,quantityMeet5,quantityMeet6,quantityMeet7,quantityMeet8,quantityMeet9,quantityMeet10,quantityMeet11,quantityMeet12"),
    SUPPLIER_CON_PO("supplier_con_po", "采购订单风险确认", "supplierCode,purchaseOrder,serialNumber,quantityMeet,feedbackResults,ventureType,ventureSpecific,measures"),
    SUPPLIER_EMPLOYEE("supplier_employee", "人员资质信息", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,operatorId,operatorName,haveQuantity,dataUpdateTime,positionId,positionName,qualificationLevel,checkInTime,checkOutTime"),
    SUPPLIER_INFO("supplier_info", "供应商基础信息", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,keyStation,dataUpdateTime,productionLineOrder,stationOrder,vendorProductNo,vendorProductName,cheryProductNo,cheryProductName"),
    SUPPLIER_PRO_ATTACHMENT_DATA("supplier_pro_attachment_data", "附件类数据", "supplierCode,supplierName,type,fileName,fileUrl,dateTime,productionLineName,productionLineId,stationName,stationId,deviceName,deviceId,vendorProductNo,vendorProductName,cheryProductNo,cheryProductName,vendorProductSn"),
    SUPPLIER_PRO_CPS("supplier_pro_cps", "过程控制项质量数据", "supplierCode,supplierName,vendorProductNo,vendorProductName,vendorProductSn,vendorProductBatch,cheryProductNo,cheryProductName,cheryProductSn,productBatchNo,manufactureNo,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,empCode,empName,vendorFieldName,vendorFieldCode,gatherSpot,samplingRate,limitUpdateTime,vendorFieldDesc,carrierCode,intputQty,fttQty,parameter,characteristic,cc,sc,spc,standardValue,upperLimit,lowerLimit,decimalValue,unitCn,unitEn,checkResult,detectionMode,workShift,collectTime,checkMode,deviceCode,deviceName"),
    SUPPLIER_PRO_DATA("supplier_pro_data", "生产过程数据", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,empCode,empName,vendorProductName,vendorProductNo,vendorProductBatch,vendorProductSn,subProdNo,subProdName,subBatchNo,childPackageInfo,subProdNum,subProdSn,childSource,subSupplierCode,subSupplierName,cheryProductNo,cheryProductName,cheryProductSn,manufactureNo,productBatchNo,workShift,materialInputTime,materialOutputTime,vendorFieldNum,vendorFieldName,instrumentQualityStatus,manualQualityStatus,finalQualityStatus,collectTime,dateTime,parentHardwareRevision,parentSoftwareRevision,childHardwareRevision,childSoftwareRevision"),
    SUPPLIER_PRO_ENVIRONMENT("supplier_pro_environment", "环境业务数据", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,envIndicatorName,numValue,upperLimit,lowerLimit,chineseUnit,equipmentCode,equipmentName,dataCollectionPoint,collectTime"),
    SUPPLIER_PRO_FIRST_PASSYIELD("supplier_pro_first_passyield", "产品一次合格率", "supplierCode,supplierName,vendorProductNo,vendorProductName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,cheryProductNo,cheryProductName,manufactureNo,productBatchNo,workOrderNumber,defectiveNumber,acceptableNumber,oncePassRateRealValue,oncePassRateTagValue,workShift,statisticalTime,dateTime"),
    SUPPLIER_PRO_FLAW("supplier_pro_flaw", "缺陷业务数据", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,defectsCode,defectsName,classOfName,vendorProductNo,vendorProductName,vendorProductBatch,vendorProductSn,cheryProductNo,cheryProductName,cheryProductSn,productBatchNo,manufactureNo,workShift,numberofdefect,defectsDesc,defectsLevel,statisticalTime"),
    SUPPLIER_PRO_MATERIAL_DATA("supplier_pro_material_data", "物料主数据", "supplierCode,supplierName,vendorProductNo,vendorProductName,type,vendorHardwareRevision,cheryProductNo,cheryProductName,oemHardwareRevision,oemSoftwareRevision,oemModel,oemProjectName,launched,dateTime,plantId,plantName,procurementType,mpnCode,mpnName,validDays"),
    SUPPLIER_PRO_MATERIAL_STOCK("supplier_pro_material_stock", "来料检验数据", "supplierCode,supplierName,supplierSubCode,supplierSubName,subSupplierCode,subSupplierName,subSupplierAddress,componentCode,componentName,subBatchNo,subBatchNum,subBatchSn,empCode,empName,deviceCode,deviceName,featureName,featureUnit,standardValue,featureUpper,featureLower,featureValue,checkNo,checkResult,checkTime,samplingRate,limitUpdateTime,vendorFieldDesc,vendorFieldCode,deadLine"),
    SUPPLIER_PRO_OEE_ACHIEVEMENT_RATE("supplier_pro_oee_achievement_rate", "设备OEE达成率", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,deviceId,deviceName,cheryProductNo,cheryProductName,vendorProductNo,vendorProductName,productBatchNo,manufactureNo,rate,rateTagValue,workShift,statisticalTime,dateTime"),
    SUPPLIER_PRO_OEE_TIME_DETAILS("supplier_pro_oee_time_details", "OEE时间明细", "recId,supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,deviceType,deviceId,deviceName,type,subType,subTypeDescription,planBeginTime,planEndTime,startTime,endTime"),
    SUPPLIER_PRO_PROCESS("supplier_pro_process", "工艺", "supplierCode,supplierName,cheryProductNo,cheryProductName,vendorProductNo,vendorProductName,techCode,techName,techVersion,isEnabled,maxProcessingCapacity,promiseRatio,makeSamePeriod"),
    SUPPLIER_PRO_PROCESS_EQUIPMENT("supplier_pro_process_equipment", "工艺装备", "supplierCode,supplierName,deviceType,deviceId,deviceName,manufacturer,modelNumber,productionDate,material,currentLocation,deviceStatus,designLifeUnits,designLifeValue,currentUsageCount,effectiveDays,maxProcessHours,regularProcessHours,deviceStartDate,deviceEndDate,machineCosts,machinePurchasePeriod,machineType,unitperHour,cavityCount,moldSize,copyMoldCosts,overhaulCount,calibrationDate,calibrationDueDays,unitType"),
    SUPPLIER_PRO_SCHEDULING("supplier_pro_scheduling", "排产数据", "supplierCode,supplierName,plantId,plantName,vendorProductNo,vendorProductName,cheryProductNo,cheryProductName,planNo,manufactureNo,productBatchNo,manufactureNum,manufactureInputNum,manufactureOutputNum,planStatus,planBeginTime,planEndTime,actualBeginTime,actualEndTime"),
    SUPPLIER_PRO_STATION_FIRST_PASSYIELD("supplier_pro_station_first_passyield", "工位一次合格率", "supplierCode,supplierName,plantId,plantName,workshopId,workshopName,productionLineId,productionLineName,stationId,stationName,cheryProductNo,cheryProductName,vendorProductNo,vendorProductName,productBatchNo,manufactureNo,workOrderNumber,defectiveNumber,acceptableNumber,oncePassRateRealValue,oncePassRateTagValue,workShift,statisticalTime,dateTime"),
    SUPPLIER_SINV_DATA("supplier_sinv_data", "供应商共享库存", "supplierCode,supplierName,materialCode,materialDescription,materialType,quantityCurrent,quantityPlan,inventoryStatus,safetyStock,productionCycle,dataUpdateTime,supplierBatch,supplieryxqDate");

    private final String code;
    private final String name;
    private final String fields;

    InterfaceEnum(String code, String name, String fields) {
        this.code = code;
        this.name = name;
        this.fields = fields;
    }

}
