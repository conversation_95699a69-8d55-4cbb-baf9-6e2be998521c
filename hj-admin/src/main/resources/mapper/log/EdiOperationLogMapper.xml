<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.log.EdiOperationLogMapper">
    
    <resultMap type="EdiOperationLog" id="EdiOperationLogResult">
        <result property="id"               column="id" />
        <result property="batchNo"          column="batch_no" />
        <result property="operationType"    column="operation_type" />
        <result property="interfacePath"    column="interface_path" />
        <result property="interfaceName"    column="interface_name" />
        <result property="pushStatus"       column="push_status" />
        <result property="requestStatus"    column="request_status" />
        <result property="requestParams"    column="request_params" />
        <result property="responseResult"   column="response_result" />
        <result property="errorMessage"     column="error_message" />
        <result property="startTime"        column="start_time" />
        <result property="endTime"          column="end_time" />
        <result property="executionTime"    column="execution_time" />
        <result property="retryCount"       column="retry_count" />
        <result property="isScheduled"      column="is_scheduled" />
        <result property="operatorId"       column="operator_id" />
        <result property="operatorName"     column="operator_name" />
        <result property="createBy"         column="create_by" />
        <result property="createTime"       column="create_time" />
        <result property="updateBy"         column="update_by" />
        <result property="updateTime"       column="update_time" />
        <result property="remark"           column="remark" />
    </resultMap>

    <sql id="selectEdiOperationLogVo">
        select id, batch_no, operation_type, interface_path, interface_name, push_status, request_status, 
               request_params, response_result, error_message, start_time, end_time, execution_time, 
               retry_count, is_scheduled, operator_id, operator_name, create_by, create_time, 
               update_by, update_time, remark 
        from edi_operation_log
    </sql>

    <select id="selectEdiOperationLogList" parameterType="EdiOperationLog" resultMap="EdiOperationLogResult">
        <include refid="selectEdiOperationLogVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no like concat('%', #{batchNo}, '%')</if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="interfacePath != null  and interfacePath != ''"> and interface_path like concat('%', #{interfacePath}, '%')</if>
            <if test="interfaceName != null  and interfaceName != ''"> and interface_name like concat('%', #{interfaceName}, '%')</if>
            <if test="pushStatus != null "> and push_status = #{pushStatus}</if>
            <if test="requestStatus != null "> and request_status = #{requestStatus}</if>
            <if test="isScheduled != null "> and is_scheduled = #{isScheduled}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(start_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(start_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectEdiOperationLogById" parameterType="Long" resultMap="EdiOperationLogResult">
        <include refid="selectEdiOperationLogVo"/>
        where id = #{id}
    </select>

    <select id="selectEdiOperationLogByBatchNo" parameterType="String" resultMap="EdiOperationLogResult">
        <include refid="selectEdiOperationLogVo"/>
        where batch_no = #{batchNo}
        order by create_time desc
        limit 1
    </select>

    <select id="selectLatestLogByPathAndType" resultMap="EdiOperationLogResult">
        <include refid="selectEdiOperationLogVo"/>
        where interface_path = #{interfacePath} and operation_type = #{operationType}
        order by create_time desc
        limit 1
    </select>
        
    <insert id="insertEdiOperationLog" parameterType="EdiOperationLog" useGeneratedKeys="true" keyProperty="id">
        insert into edi_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="interfacePath != null and interfacePath != ''">interface_path,</if>
            <if test="interfaceName != null and interfaceName != ''">interface_name,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="requestStatus != null">request_status,</if>
            <if test="requestParams != null">request_params,</if>
            <if test="responseResult != null">response_result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="isScheduled != null">is_scheduled,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="interfacePath != null and interfacePath != ''">#{interfacePath},</if>
            <if test="interfaceName != null and interfaceName != ''">#{interfaceName},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="requestStatus != null">#{requestStatus},</if>
            <if test="requestParams != null">#{requestParams},</if>
            <if test="responseResult != null">#{responseResult},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="executionTime != null">#{executionTime},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="isScheduled != null">#{isScheduled},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEdiOperationLog" parameterType="EdiOperationLog">
        update edi_operation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="interfacePath != null and interfacePath != ''">interface_path = #{interfacePath},</if>
            <if test="interfaceName != null and interfaceName != ''">interface_name = #{interfaceName},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="requestStatus != null">request_status = #{requestStatus},</if>
            <if test="requestParams != null">request_params = #{requestParams},</if>
            <if test="responseResult != null">response_result = #{responseResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="executionTime != null">execution_time = #{executionTime},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="isScheduled != null">is_scheduled = #{isScheduled},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEdiOperationLogById" parameterType="Long">
        delete from edi_operation_log where id = #{id}
    </delete>

    <delete id="deleteEdiOperationLogByIds" parameterType="String">
        delete from edi_operation_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
