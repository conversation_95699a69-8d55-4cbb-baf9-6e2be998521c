<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.log.EdiFieldModifyLogMapper">
    
    <resultMap type="EdiFieldModifyLog" id="EdiFieldModifyLogResult">
        <result property="id"                column="id"                />
        <result property="interfaceName"     column="interface_name"    />
        <result property="tableName"         column="table_name"        />
        <result property="dataId"            column="data_id"           />
        <result property="fieldName"         column="field_name"        />
        <result property="oldValue"          column="old_value"         />
        <result property="newValue"          column="new_value"         />
        <result property="modifyUserId"      column="modify_user_id"    />
        <result property="modifyUserName"    column="modify_user_name"  />
        <result property="modifyTime"        column="modify_time"       />
        <result property="createBy"          column="create_by"         />
        <result property="createTime"        column="create_time"       />
    </resultMap>

    <sql id="selectEdiFieldModifyLogVo">
        select id, interface_name, table_name, data_id, field_name, old_value, new_value, 
               modify_user_id, modify_user_name, modify_time, create_by, create_time 
        from edi_field_modify_log
    </sql>

    <insert id="insertEdiFieldModifyLog" parameterType="EdiFieldModifyLog" useGeneratedKeys="true" keyProperty="id">
        insert into edi_field_modify_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceName != null and interfaceName != ''">interface_name,</if>
            <if test="tableName != null and tableName != ''">table_name,</if>
            <if test="dataId != null">data_id,</if>
            <if test="fieldName != null and fieldName != ''">field_name,</if>
            <if test="oldValue != null">old_value,</if>
            <if test="newValue != null">new_value,</if>
            <if test="modifyUserId != null">modify_user_id,</if>
            <if test="modifyUserName != null and modifyUserName != ''">modify_user_name,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="interfaceName != null and interfaceName != ''">#{interfaceName},</if>
            <if test="tableName != null and tableName != ''">#{tableName},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="fieldName != null and fieldName != ''">#{fieldName},</if>
            <if test="oldValue != null">#{oldValue},</if>
            <if test="newValue != null">#{newValue},</if>
            <if test="modifyUserId != null">#{modifyUserId},</if>
            <if test="modifyUserName != null and modifyUserName != ''">#{modifyUserName},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

</mapper>
