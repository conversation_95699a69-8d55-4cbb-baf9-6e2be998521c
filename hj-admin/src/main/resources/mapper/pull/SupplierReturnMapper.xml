<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.pull.SupplierReturnMapper">
    
    <resultMap type="SupplierReturn" id="SupplierReturnResult">
        <result property="id"    column="id"    />
        <result property="returnNumber"    column="return_number"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="serialSrate"    column="serial_srate"    />
        <result property="pickUpLocation"    column="pick_up_location"    />
        <result property="demandPickupTime"    column="demand_pickup_time"    />
        <result property="pickUpCrossings"    column="pick_up_crossings"    />
        <result property="feedback"    column="feedback"    />
        <result property="plant"    column="plant"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="quantityDelivery"    column="quantity_delivery"    />
        <result property="returnType"    column="return_type"    />
        <result property="lotNumber"    column="lot_number"    />
        <result property="judge"    column="judge"    />
        <result property="returnReason"    column="return_reason"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierReturnVo">
        select id, return_number, serial_number, serial_srate, pick_up_location, demand_pickup_time, pick_up_crossings, feedback, plant, material_code, material_description, quantity_delivery, return_type, lot_number, judge, return_reason, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_return
    </sql>

    <select id="selectSupplierReturnList" parameterType="SupplierReturn" resultMap="SupplierReturnResult">
        <include refid="selectSupplierReturnVo"/>
        <where>  
            <if test="returnNumber != null  and returnNumber != ''"> and return_number = #{returnNumber}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number = #{serialNumber}</if>
            <if test="serialSrate != null  and serialSrate != ''"> and serial_srate = #{serialSrate}</if>
            <if test="pickUpLocation != null  and pickUpLocation != ''"> and pick_up_location = #{pickUpLocation}</if>
            <if test="demandPickupTime != null  and demandPickupTime != ''"> and demand_pickup_time = #{demandPickupTime}</if>
            <if test="pickUpCrossings != null  and pickUpCrossings != ''"> and pick_up_crossings = #{pickUpCrossings}</if>
            <if test="feedback != null  and feedback != ''"> and feedback = #{feedback}</if>
            <if test="plant != null  and plant != ''"> and plant = #{plant}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="quantityDelivery != null "> and quantity_delivery = #{quantityDelivery}</if>
            <if test="returnType != null  and returnType != ''"> and return_type = #{returnType}</if>
            <if test="lotNumber != null  and lotNumber != ''"> and lot_number = #{lotNumber}</if>
            <if test="judge != null  and judge != ''"> and judge = #{judge}</if>
            <if test="returnReason != null  and returnReason != ''"> and return_reason = #{returnReason}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierReturnById" parameterType="Long" resultMap="SupplierReturnResult">
        <include refid="selectSupplierReturnVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierReturn" parameterType="SupplierReturn">
        insert into supplier_return
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="returnNumber != null">return_number,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="serialSrate != null">serial_srate,</if>
            <if test="pickUpLocation != null">pick_up_location,</if>
            <if test="demandPickupTime != null">demand_pickup_time,</if>
            <if test="pickUpCrossings != null">pick_up_crossings,</if>
            <if test="feedback != null">feedback,</if>
            <if test="plant != null">plant,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="quantityDelivery != null">quantity_delivery,</if>
            <if test="returnType != null">return_type,</if>
            <if test="lotNumber != null">lot_number,</if>
            <if test="judge != null">judge,</if>
            <if test="returnReason != null">return_reason,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="returnNumber != null">#{returnNumber},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="serialSrate != null">#{serialSrate},</if>
            <if test="pickUpLocation != null">#{pickUpLocation},</if>
            <if test="demandPickupTime != null">#{demandPickupTime},</if>
            <if test="pickUpCrossings != null">#{pickUpCrossings},</if>
            <if test="feedback != null">#{feedback},</if>
            <if test="plant != null">#{plant},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="quantityDelivery != null">#{quantityDelivery},</if>
            <if test="returnType != null">#{returnType},</if>
            <if test="lotNumber != null">#{lotNumber},</if>
            <if test="judge != null">#{judge},</if>
            <if test="returnReason != null">#{returnReason},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierReturn" parameterType="SupplierReturn">
        update supplier_return
        <trim prefix="SET" suffixOverrides=",">
            <if test="returnNumber != null">return_number = #{returnNumber},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="serialSrate != null">serial_srate = #{serialSrate},</if>
            <if test="pickUpLocation != null">pick_up_location = #{pickUpLocation},</if>
            <if test="demandPickupTime != null">demand_pickup_time = #{demandPickupTime},</if>
            <if test="pickUpCrossings != null">pick_up_crossings = #{pickUpCrossings},</if>
            <if test="feedback != null">feedback = #{feedback},</if>
            <if test="plant != null">plant = #{plant},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="quantityDelivery != null">quantity_delivery = #{quantityDelivery},</if>
            <if test="returnType != null">return_type = #{returnType},</if>
            <if test="lotNumber != null">lot_number = #{lotNumber},</if>
            <if test="judge != null">judge = #{judge},</if>
            <if test="returnReason != null">return_reason = #{returnReason},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierReturnById" parameterType="Long">
        delete from supplier_return where id = #{id}
    </delete>

    <delete id="deleteSupplierReturnByIds" parameterType="String">
        delete from supplier_return where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        insert into supplier_return (
            id, return_number, serial_number, serial_srate, pick_up_location, demand_pickup_time,
            pick_up_crossings, feedback, plant, material_code, material_description, quantity_delivery,
            return_type, lot_number, judge, return_reason, create_by_user, create_time, update_by_user,
            update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.returnNumber}, #{item.serialNumber}, #{item.serialSrate},
                #{item.pickUpLocation}, #{item.demandPickupTime}, #{item.pickUpCrossings}, #{item.feedback},
                #{item.plant}, #{item.materialCode}, #{item.materialDescription}, #{item.quantityDelivery},
                #{item.returnType}, #{item.lotNumber}, #{item.judge}, #{item.returnReason},
                #{item.createByUser}, #{item.createTime}, #{item.updateByUser}, #{item.updateTime},
                #{item.isDelete}, #{item.version}, #{item.insertTime}, #{item.modifyTime},
                #{item.insertBy}, #{item.modifyBy}
            )
        </foreach>
        on duplicate key update
            return_number = values(return_number),
            serial_number = values(serial_number),
            serial_srate = values(serial_srate),
            pick_up_location = values(pick_up_location),
            demand_pickup_time = values(demand_pickup_time),
            pick_up_crossings = values(pick_up_crossings),
            feedback = values(feedback),
            plant = values(plant),
            material_code = values(material_code),
            material_description = values(material_description),
            quantity_delivery = values(quantity_delivery),
            return_type = values(return_type),
            lot_number = values(lot_number),
            judge = values(judge),
            return_reason = values(return_reason),
            create_by_user = values(create_by_user),
            create_time = values(create_time),
            update_by_user = values(update_by_user),
            update_time = values(update_time),
            is_delete = values(is_delete),
            version = values(version),
            modify_time = values(modify_time),
            modify_by = values(modify_by)
    </insert>

</mapper>