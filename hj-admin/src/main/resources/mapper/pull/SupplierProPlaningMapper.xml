<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.pull.SupplierProPlaningMapper">

    <resultMap type="SupplierProPlaning" id="SupplierProPlaningResult">
        <result property="id" column="id" />
        <result property="releaseEdition" column="release_edition" />
        <result property="models" column="models" />
        <result property="salseDepartment" column="salse_department" />
        <result property="type" column="type" />
        <result property="assembly" column="assembly" />
        <result property="pattern" column="pattern" />
        <result property="omterior" column="omterior" />
        <result property="materialCode" column="material_code" />
        <result property="startMonth" column="start_month" />
        <result property="quantity1" column="quantity1" />
        <result property="quantity2" column="quantity2" />
        <result property="quantity3" column="quantity3" />
        <result property="quantity4" column="quantity4" />
        <result property="quantity5" column="quantity5" />
        <result property="quantity6" column="quantity6" />
        <result property="plant" column="plant" />
        <result property="models2" column="models2" />
        <result property="displacement" column="displacement" />
        <result property="createByUser" column="create_by_user" />
        <result property="createTime" column="create_time" />
        <result property="updateByUser" column="update_by_user" />
        <result property="updateTime" column="update_time" />
        <result property="isDelete" column="is_delete" />
        <result property="version" column="version" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierProPlaningVo"> select id, release_edition, models, salse_department,
        type, assembly, pattern, omterior, material_code, start_month, quantity1, quantity2,
        quantity3, quantity4, quantity5, quantity6, plant, models2, displacement, create_by_user,
        create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time,
        insert_by, modify_by from supplier_pro_planing </sql>

    <select id="selectSupplierProPlaningList" parameterType="SupplierProPlaning"
        resultMap="SupplierProPlaningResult">
        <include refid="selectSupplierProPlaningVo" />
        <where>
            <if test="releaseEdition != null  and releaseEdition != ''"> and release_edition =
        #{releaseEdition}</if>
            <if test="models != null  and models != ''"> and models = #{models}</if>
            <if
                test="salseDepartment != null  and salseDepartment != ''"> and salse_department =
        #{salseDepartment}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if
                test="assembly != null  and assembly != ''"> and assembly = #{assembly}</if>
            <if
                test="pattern != null  and pattern != ''"> and pattern = #{pattern}</if>
            <if
                test="omterior != null  and omterior != ''"> and omterior = #{omterior}</if>
            <if
                test="materialCode != null  and materialCode != ''"> and material_code =
        #{materialCode}</if>
            <if test="startMonth != null  and startMonth != ''"> and start_month =
        #{startMonth}</if>
            <if test="quantity1 != null "> and quantity1 = #{quantity1}</if>
            <if
                test="quantity2 != null "> and quantity2 = #{quantity2}</if>
            <if
                test="quantity3 != null "> and quantity3 = #{quantity3}</if>
            <if
                test="quantity4 != null "> and quantity4 = #{quantity4}</if>
            <if
                test="quantity5 != null "> and quantity5 = #{quantity5}</if>
            <if
                test="quantity6 != null "> and quantity6 = #{quantity6}</if>
            <if
                test="plant != null  and plant != ''"> and plant = #{plant}</if>
            <if
                test="models2 != null  and models2 != ''"> and models2 = #{models2}</if>
            <if
                test="displacement != null  and displacement != ''"> and displacement =
        #{displacement}</if>
            <if test="createByUser != null  and createByUser != ''"> and
        create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''">
        and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete =
        #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if
                test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>

    <select id="selectSupplierProPlaningById" parameterType="Long"
        resultMap="SupplierProPlaningResult">
        <include refid="selectSupplierProPlaningVo" /> where id = #{id} </select>

    <select id="selectSupplierProPlaningByIds" resultMap="SupplierProPlaningResult">
        <include refid="selectSupplierProPlaningVo" /> where id in <foreach item="id"
            collection="list" open="(" separator="," close=")"> #{id} </foreach>
    </select>

    <insert id="insertSupplierProPlaning" parameterType="SupplierProPlaning"> insert into
        supplier_pro_planing <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="releaseEdition != null">release_edition,</if>
            <if
                test="models != null">models,</if>
            <if test="salseDepartment != null">
        salse_department,</if>
            <if test="type != null">type,</if>
            <if test="assembly != null">
        assembly,</if>
            <if test="pattern != null">pattern,</if>
            <if test="omterior != null">omterior,</if>
            <if
                test="materialCode != null">material_code,</if>
            <if test="startMonth != null">
        start_month,</if>
            <if test="quantity1 != null">quantity1,</if>
            <if test="quantity2 != null">
        quantity2,</if>
            <if test="quantity3 != null">quantity3,</if>
            <if test="quantity4 != null">
        quantity4,</if>
            <if test="quantity5 != null">quantity5,</if>
            <if test="quantity6 != null">
        quantity6,</if>
            <if test="plant != null">plant,</if>
            <if test="models2 != null">models2,</if>
            <if
                test="displacement != null">displacement,</if>
            <if test="createByUser != null">
        create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if
                test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">
        update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">
        version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">
        modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="releaseEdition != null">#{releaseEdition},</if>
            <if
                test="models != null">#{models},</if>
            <if test="salseDepartment != null">
        #{salseDepartment},</if>
            <if test="type != null">#{type},</if>
            <if test="assembly != null">
        #{assembly},</if>
            <if test="pattern != null">#{pattern},</if>
            <if test="omterior != null">
        #{omterior},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if
                test="startMonth != null">#{startMonth},</if>
            <if test="quantity1 != null">
        #{quantity1},</if>
            <if test="quantity2 != null">#{quantity2},</if>
            <if
                test="quantity3 != null">#{quantity3},</if>
            <if test="quantity4 != null">
        #{quantity4},</if>
            <if test="quantity5 != null">#{quantity5},</if>
            <if
                test="quantity6 != null">#{quantity6},</if>
            <if test="plant != null">#{plant},</if>
            <if
                test="models2 != null">#{models2},</if>
            <if test="displacement != null">
        #{displacement},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if
                test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">
        #{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if
                test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if
                test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">
        #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierProPlaning" parameterType="SupplierProPlaning"> update
        supplier_pro_planing <trim prefix="SET" suffixOverrides=",">
            <if test="releaseEdition != null">release_edition = #{releaseEdition},</if>
            <if
                test="models != null">models = #{models},</if>
            <if test="salseDepartment != null">salse_department
        = #{salseDepartment},</if>
            <if test="type != null">type = #{type},</if>
            <if
                test="assembly != null">assembly = #{assembly},</if>
            <if test="pattern != null">pattern
        = #{pattern},</if>
            <if test="omterior != null">omterior = #{omterior},</if>
            <if
                test="materialCode != null">material_code = #{materialCode},</if>
            <if
                test="startMonth != null">start_month = #{startMonth},</if>
            <if
                test="quantity1 != null">quantity1 = #{quantity1},</if>
            <if test="quantity2 != null">quantity2
        = #{quantity2},</if>
            <if test="quantity3 != null">quantity3 = #{quantity3},</if>
            <if
                test="quantity4 != null">quantity4 = #{quantity4},</if>
            <if test="quantity5 != null">quantity5
        = #{quantity5},</if>
            <if test="quantity6 != null">quantity6 = #{quantity6},</if>
            <if
                test="plant != null">plant = #{plant},</if>
            <if test="models2 != null">models2 =
        #{models2},</if>
            <if test="displacement != null">displacement = #{displacement},</if>
            <if
                test="createByUser != null">create_by_user = #{createByUser},</if>
            <if
                test="createTime != null">create_time = #{createTime},</if>
            <if
                test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if
                test="updateTime != null">update_time = #{updateTime},</if>
            <if
                test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version
        = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierProPlaningById" parameterType="Long"> delete from supplier_pro_planing
        where id = #{id} </delete>

    <delete id="deleteSupplierProPlaningByIds" parameterType="String"> delete from
        supplier_pro_planing where id in <foreach item="id" collection="array" open="("
            separator="," close=")"> #{id} </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List"> insert into
        supplier_pro_planing ( id, release_edition, models, salse_department, type, assembly,
        pattern, omterior, material_code, start_month, quantity1, quantity2, quantity3, quantity4,
        quantity5, quantity6, plant, models2, displacement, create_by_user, create_time,
        update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by,
        modify_by ) values <foreach collection="list" item="item" separator=","> ( #{item.id},
        #{item.releaseEdition}, #{item.models}, #{item.salseDepartment}, #{item.type},
        #{item.assembly}, #{item.pattern}, #{item.omterior}, #{item.materialCode},
        #{item.startMonth}, #{item.quantity1}, #{item.quantity2}, #{item.quantity3},
        #{item.quantity4}, #{item.quantity5}, #{item.quantity6}, #{item.plant}, #{item.models2},
        #{item.displacement}, #{item.createByUser}, #{item.createTime}, #{item.updateByUser},
        #{item.updateTime}, #{item.isDelete}, #{item.version}, #{item.insertTime},
        #{item.modifyTime}, #{item.insertBy}, #{item.modifyBy} ) </foreach> on duplicate key update
        release_edition = values(release_edition), models = values(models), salse_department =
        values(salse_department), type = values(type), assembly = values(assembly), pattern =
        values(pattern), omterior = values(omterior), material_code = values(material_code),
        start_month = values(start_month), quantity1 = values(quantity1), quantity2 =
        values(quantity2), quantity3 = values(quantity3), quantity4 = values(quantity4), quantity5 =
        values(quantity5), quantity6 = values(quantity6), plant = values(plant), models2 =
        values(models2), displacement = values(displacement), create_by_user =
        values(create_by_user), create_time = values(create_time), update_by_user =
        values(update_by_user), update_time = values(update_time), is_delete = values(is_delete),
        version = values(version), modify_time = values(modify_time), modify_by = values(modify_by) </insert>

</mapper>