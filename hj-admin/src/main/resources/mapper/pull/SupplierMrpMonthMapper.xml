<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.pull.SupplierMrpMonthMapper">
    
    <resultMap type="SupplierMrpMonth" id="SupplierMrpMonthResult">
        <result property="id"    column="id"    />
        <result property="releaseEdition"    column="release_edition"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="startMonth"    column="start_month"    />
        <result property="quantityDemand1"    column="quantity_demand1"    />
        <result property="quantityDemand2"    column="quantity_demand2"    />
        <result property="quantityDemand3"    column="quantity_demand3"    />
        <result property="quantityDemand4"    column="quantity_demand4"    />
        <result property="quantityDemand5"    column="quantity_demand5"    />
        <result property="quantityDemand6"    column="quantity_demand6"    />
        <result property="quantityDemand7"    column="quantity_demand7"    />
        <result property="quantityDemand8"    column="quantity_demand8"    />
        <result property="quantityDemand9"    column="quantity_demand9"    />
        <result property="quantityDemand10"    column="quantity_demand10"    />
        <result property="quantityDemand11"    column="quantity_demand11"    />
        <result property="quantityDemand12"    column="quantity_demand12"    />
        <result property="isUpdate"    column="is_update"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierMrpMonthVo">
        select id, release_edition, material_code, material_description, plant_id, plant_name, start_month, quantity_demand1, quantity_demand2, quantity_demand3, quantity_demand4, quantity_demand5, quantity_demand6, quantity_demand7, quantity_demand8, quantity_demand9, quantity_demand10, quantity_demand11, quantity_demand12, is_update, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_mrp_month
    </sql>

    <select id="selectSupplierMrpMonthList" parameterType="SupplierMrpMonth" resultMap="SupplierMrpMonthResult">
        <include refid="selectSupplierMrpMonthVo"/>
        <where>  
            <if test="releaseEdition != null  and releaseEdition != ''"> and release_edition = #{releaseEdition}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="startMonth != null  and startMonth != ''"> and start_month = #{startMonth}</if>
            <if test="quantityDemand1 != null "> and quantity_demand1 = #{quantityDemand1}</if>
            <if test="quantityDemand2 != null "> and quantity_demand2 = #{quantityDemand2}</if>
            <if test="quantityDemand3 != null "> and quantity_demand3 = #{quantityDemand3}</if>
            <if test="quantityDemand4 != null "> and quantity_demand4 = #{quantityDemand4}</if>
            <if test="quantityDemand5 != null "> and quantity_demand5 = #{quantityDemand5}</if>
            <if test="quantityDemand6 != null "> and quantity_demand6 = #{quantityDemand6}</if>
            <if test="quantityDemand7 != null "> and quantity_demand7 = #{quantityDemand7}</if>
            <if test="quantityDemand8 != null "> and quantity_demand8 = #{quantityDemand8}</if>
            <if test="quantityDemand9 != null "> and quantity_demand9 = #{quantityDemand9}</if>
            <if test="quantityDemand10 != null "> and quantity_demand10 = #{quantityDemand10}</if>
            <if test="quantityDemand11 != null "> and quantity_demand11 = #{quantityDemand11}</if>
            <if test="quantityDemand12 != null "> and quantity_demand12 = #{quantityDemand12}</if>
            <if test="isUpdate != null  and isUpdate != ''"> and is_update = #{isUpdate}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierMrpMonthById" parameterType="Long" resultMap="SupplierMrpMonthResult">
        <include refid="selectSupplierMrpMonthVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierMrpMonth" parameterType="SupplierMrpMonth">
        insert into supplier_mrp_month
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="releaseEdition != null">release_edition,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="plantName != null">plant_name,</if>
            <if test="startMonth != null">start_month,</if>
            <if test="quantityDemand1 != null">quantity_demand1,</if>
            <if test="quantityDemand2 != null">quantity_demand2,</if>
            <if test="quantityDemand3 != null">quantity_demand3,</if>
            <if test="quantityDemand4 != null">quantity_demand4,</if>
            <if test="quantityDemand5 != null">quantity_demand5,</if>
            <if test="quantityDemand6 != null">quantity_demand6,</if>
            <if test="quantityDemand7 != null">quantity_demand7,</if>
            <if test="quantityDemand8 != null">quantity_demand8,</if>
            <if test="quantityDemand9 != null">quantity_demand9,</if>
            <if test="quantityDemand10 != null">quantity_demand10,</if>
            <if test="quantityDemand11 != null">quantity_demand11,</if>
            <if test="quantityDemand12 != null">quantity_demand12,</if>
            <if test="isUpdate != null">is_update,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="releaseEdition != null">#{releaseEdition},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="plantName != null">#{plantName},</if>
            <if test="startMonth != null">#{startMonth},</if>
            <if test="quantityDemand1 != null">#{quantityDemand1},</if>
            <if test="quantityDemand2 != null">#{quantityDemand2},</if>
            <if test="quantityDemand3 != null">#{quantityDemand3},</if>
            <if test="quantityDemand4 != null">#{quantityDemand4},</if>
            <if test="quantityDemand5 != null">#{quantityDemand5},</if>
            <if test="quantityDemand6 != null">#{quantityDemand6},</if>
            <if test="quantityDemand7 != null">#{quantityDemand7},</if>
            <if test="quantityDemand8 != null">#{quantityDemand8},</if>
            <if test="quantityDemand9 != null">#{quantityDemand9},</if>
            <if test="quantityDemand10 != null">#{quantityDemand10},</if>
            <if test="quantityDemand11 != null">#{quantityDemand11},</if>
            <if test="quantityDemand12 != null">#{quantityDemand12},</if>
            <if test="isUpdate != null">#{isUpdate},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierMrpMonth" parameterType="SupplierMrpMonth">
        update supplier_mrp_month
        <trim prefix="SET" suffixOverrides=",">
            <if test="releaseEdition != null">release_edition = #{releaseEdition},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="plantName != null">plant_name = #{plantName},</if>
            <if test="startMonth != null">start_month = #{startMonth},</if>
            <if test="quantityDemand1 != null">quantity_demand1 = #{quantityDemand1},</if>
            <if test="quantityDemand2 != null">quantity_demand2 = #{quantityDemand2},</if>
            <if test="quantityDemand3 != null">quantity_demand3 = #{quantityDemand3},</if>
            <if test="quantityDemand4 != null">quantity_demand4 = #{quantityDemand4},</if>
            <if test="quantityDemand5 != null">quantity_demand5 = #{quantityDemand5},</if>
            <if test="quantityDemand6 != null">quantity_demand6 = #{quantityDemand6},</if>
            <if test="quantityDemand7 != null">quantity_demand7 = #{quantityDemand7},</if>
            <if test="quantityDemand8 != null">quantity_demand8 = #{quantityDemand8},</if>
            <if test="quantityDemand9 != null">quantity_demand9 = #{quantityDemand9},</if>
            <if test="quantityDemand10 != null">quantity_demand10 = #{quantityDemand10},</if>
            <if test="quantityDemand11 != null">quantity_demand11 = #{quantityDemand11},</if>
            <if test="quantityDemand12 != null">quantity_demand12 = #{quantityDemand12},</if>
            <if test="isUpdate != null">is_update = #{isUpdate},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierMrpMonthById" parameterType="Long">
        delete from supplier_mrp_month where id = #{id}
    </delete>

    <delete id="deleteSupplierMrpMonthByIds" parameterType="String">
        delete from supplier_mrp_month where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        insert into supplier_mrp_month (
            id, release_edition, material_code, material_description, plant_id, plant_name, start_month,
            quantity_demand1, quantity_demand2, quantity_demand3, quantity_demand4, quantity_demand5,
            quantity_demand6, quantity_demand7, quantity_demand8, quantity_demand9, quantity_demand10,
            quantity_demand11, quantity_demand12, is_update, create_by_user, create_time, update_by_user,
            update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.releaseEdition}, #{item.materialCode}, #{item.materialDescription},
                #{item.plantId}, #{item.plantName}, #{item.startMonth}, #{item.quantityDemand1},
                #{item.quantityDemand2}, #{item.quantityDemand3}, #{item.quantityDemand4}, #{item.quantityDemand5},
                #{item.quantityDemand6}, #{item.quantityDemand7}, #{item.quantityDemand8}, #{item.quantityDemand9},
                #{item.quantityDemand10}, #{item.quantityDemand11}, #{item.quantityDemand12}, #{item.isUpdate},
                #{item.createByUser}, #{item.createTime}, #{item.updateByUser}, #{item.updateTime},
                #{item.isDelete}, #{item.version}, #{item.insertTime}, #{item.modifyTime},
                #{item.insertBy}, #{item.modifyBy}
            )
        </foreach>
        on duplicate key update
            release_edition = values(release_edition),
            material_code = values(material_code),
            material_description = values(material_description),
            plant_id = values(plant_id),
            plant_name = values(plant_name),
            start_month = values(start_month),
            quantity_demand1 = values(quantity_demand1),
            quantity_demand2 = values(quantity_demand2),
            quantity_demand3 = values(quantity_demand3),
            quantity_demand4 = values(quantity_demand4),
            quantity_demand5 = values(quantity_demand5),
            quantity_demand6 = values(quantity_demand6),
            quantity_demand7 = values(quantity_demand7),
            quantity_demand8 = values(quantity_demand8),
            quantity_demand9 = values(quantity_demand9),
            quantity_demand10 = values(quantity_demand10),
            quantity_demand11 = values(quantity_demand11),
            quantity_demand12 = values(quantity_demand12),
            is_update = values(is_update),
            create_by_user = values(create_by_user),
            create_time = values(create_time),
            update_by_user = values(update_by_user),
            update_time = values(update_time),
            is_delete = values(is_delete),
            version = values(version),
            modify_time = values(modify_time),
            modify_by = values(modify_by)
    </insert>

</mapper>