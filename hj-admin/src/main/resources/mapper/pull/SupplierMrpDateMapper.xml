<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierMrpDateMapper">
    
    <resultMap type="SupplierMrpDate" id="SupplierMrpDateResult">
        <result property="id"    column="id"    />
        <result property="releaseEdition"    column="release_edition"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="startDate"    column="start_date"    />
        <result property="quantityDemand1"    column="quantity_demand1"    />
        <result property="quantityDemand2"    column="quantity_demand2"    />
        <result property="quantityDemand3"    column="quantity_demand3"    />
        <result property="quantityDemand4"    column="quantity_demand4"    />
        <result property="quantityDemand5"    column="quantity_demand5"    />
        <result property="quantityDemand6"    column="quantity_demand6"    />
        <result property="quantityDemand7"    column="quantity_demand7"    />
        <result property="quantityDemand8"    column="quantity_demand8"    />
        <result property="quantityDemand9"    column="quantity_demand9"    />
        <result property="quantityDemand10"    column="quantity_demand10"    />
        <result property="quantityDemand11"    column="quantity_demand11"    />
        <result property="quantityDemand12"    column="quantity_demand12"    />
        <result property="quantityDemand13"    column="quantity_demand13"    />
        <result property="quantityDemand14"    column="quantity_demand14"    />
        <result property="quantityDemand15"    column="quantity_demand15"    />
        <result property="quantityDemand16"    column="quantity_demand16"    />
        <result property="quantityDemand17"    column="quantity_demand17"    />
        <result property="quantityDemand18"    column="quantity_demand18"    />
        <result property="quantityDemand19"    column="quantity_demand19"    />
        <result property="quantityDemand20"    column="quantity_demand20"    />
        <result property="quantityDemand21"    column="quantity_demand21"    />
        <result property="quantityDemand22"    column="quantity_demand22"    />
        <result property="quantityDemand23"    column="quantity_demand23"    />
        <result property="quantityDemand24"    column="quantity_demand24"    />
        <result property="quantityDemand25"    column="quantity_demand25"    />
        <result property="quantityDemand26"    column="quantity_demand26"    />
        <result property="quantityDemand27"    column="quantity_demand27"    />
        <result property="quantityDemand28"    column="quantity_demand28"    />
        <result property="quantityDemand29"    column="quantity_demand29"    />
        <result property="quantityDemand30"    column="quantity_demand30"    />
        <result property="quantityDemand31"    column="quantity_demand31"    />
        <result property="isUpdate"    column="is_update"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierMrpDateVo">
        select id, release_edition, material_code, material_description, plant_id, plant_name, start_date, quantity_demand1, quantity_demand2, quantity_demand3, quantity_demand4, quantity_demand5, quantity_demand6, quantity_demand7, quantity_demand8, quantity_demand9, quantity_demand10, quantity_demand11, quantity_demand12, quantity_demand13, quantity_demand14, quantity_demand15, quantity_demand16, quantity_demand17, quantity_demand18, quantity_demand19, quantity_demand20, quantity_demand21, quantity_demand22, quantity_demand23, quantity_demand24, quantity_demand25, quantity_demand26, quantity_demand27, quantity_demand28, quantity_demand29, quantity_demand30, quantity_demand31, is_update, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_mrp_date
    </sql>

    <select id="selectSupplierMrpDateList" parameterType="SupplierMrpDate" resultMap="SupplierMrpDateResult">
        <include refid="selectSupplierMrpDateVo"/>
        <where>  
            <if test="releaseEdition != null  and releaseEdition != ''"> and release_edition = #{releaseEdition}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="startDate != null  and startDate != ''"> and start_date = #{startDate}</if>
            <if test="quantityDemand1 != null "> and quantity_demand1 = #{quantityDemand1}</if>
            <if test="quantityDemand2 != null "> and quantity_demand2 = #{quantityDemand2}</if>
            <if test="quantityDemand3 != null "> and quantity_demand3 = #{quantityDemand3}</if>
            <if test="quantityDemand4 != null "> and quantity_demand4 = #{quantityDemand4}</if>
            <if test="quantityDemand5 != null "> and quantity_demand5 = #{quantityDemand5}</if>
            <if test="quantityDemand6 != null "> and quantity_demand6 = #{quantityDemand6}</if>
            <if test="quantityDemand7 != null "> and quantity_demand7 = #{quantityDemand7}</if>
            <if test="quantityDemand8 != null "> and quantity_demand8 = #{quantityDemand8}</if>
            <if test="quantityDemand9 != null "> and quantity_demand9 = #{quantityDemand9}</if>
            <if test="quantityDemand10 != null "> and quantity_demand10 = #{quantityDemand10}</if>
            <if test="quantityDemand11 != null "> and quantity_demand11 = #{quantityDemand11}</if>
            <if test="quantityDemand12 != null "> and quantity_demand12 = #{quantityDemand12}</if>
            <if test="quantityDemand13 != null "> and quantity_demand13 = #{quantityDemand13}</if>
            <if test="quantityDemand14 != null "> and quantity_demand14 = #{quantityDemand14}</if>
            <if test="quantityDemand15 != null "> and quantity_demand15 = #{quantityDemand15}</if>
            <if test="quantityDemand16 != null "> and quantity_demand16 = #{quantityDemand16}</if>
            <if test="quantityDemand17 != null "> and quantity_demand17 = #{quantityDemand17}</if>
            <if test="quantityDemand18 != null "> and quantity_demand18 = #{quantityDemand18}</if>
            <if test="quantityDemand19 != null "> and quantity_demand19 = #{quantityDemand19}</if>
            <if test="quantityDemand20 != null "> and quantity_demand20 = #{quantityDemand20}</if>
            <if test="quantityDemand21 != null "> and quantity_demand21 = #{quantityDemand21}</if>
            <if test="quantityDemand22 != null "> and quantity_demand22 = #{quantityDemand22}</if>
            <if test="quantityDemand23 != null "> and quantity_demand23 = #{quantityDemand23}</if>
            <if test="quantityDemand24 != null "> and quantity_demand24 = #{quantityDemand24}</if>
            <if test="quantityDemand25 != null "> and quantity_demand25 = #{quantityDemand25}</if>
            <if test="quantityDemand26 != null "> and quantity_demand26 = #{quantityDemand26}</if>
            <if test="quantityDemand27 != null "> and quantity_demand27 = #{quantityDemand27}</if>
            <if test="quantityDemand28 != null "> and quantity_demand28 = #{quantityDemand28}</if>
            <if test="quantityDemand29 != null "> and quantity_demand29 = #{quantityDemand29}</if>
            <if test="quantityDemand30 != null "> and quantity_demand30 = #{quantityDemand30}</if>
            <if test="quantityDemand31 != null "> and quantity_demand31 = #{quantityDemand31}</if>
            <if test="isUpdate != null  and isUpdate != ''"> and is_update = #{isUpdate}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierMrpDateById" parameterType="Long" resultMap="SupplierMrpDateResult">
        <include refid="selectSupplierMrpDateVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierMrpDate" parameterType="SupplierMrpDate">
        insert into supplier_mrp_date
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="releaseEdition != null">release_edition,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="plantName != null">plant_name,</if>
            <if test="startDate != null">start_date,</if>
            <if test="quantityDemand1 != null">quantity_demand1,</if>
            <if test="quantityDemand2 != null">quantity_demand2,</if>
            <if test="quantityDemand3 != null">quantity_demand3,</if>
            <if test="quantityDemand4 != null">quantity_demand4,</if>
            <if test="quantityDemand5 != null">quantity_demand5,</if>
            <if test="quantityDemand6 != null">quantity_demand6,</if>
            <if test="quantityDemand7 != null">quantity_demand7,</if>
            <if test="quantityDemand8 != null">quantity_demand8,</if>
            <if test="quantityDemand9 != null">quantity_demand9,</if>
            <if test="quantityDemand10 != null">quantity_demand10,</if>
            <if test="quantityDemand11 != null">quantity_demand11,</if>
            <if test="quantityDemand12 != null">quantity_demand12,</if>
            <if test="quantityDemand13 != null">quantity_demand13,</if>
            <if test="quantityDemand14 != null">quantity_demand14,</if>
            <if test="quantityDemand15 != null">quantity_demand15,</if>
            <if test="quantityDemand16 != null">quantity_demand16,</if>
            <if test="quantityDemand17 != null">quantity_demand17,</if>
            <if test="quantityDemand18 != null">quantity_demand18,</if>
            <if test="quantityDemand19 != null">quantity_demand19,</if>
            <if test="quantityDemand20 != null">quantity_demand20,</if>
            <if test="quantityDemand21 != null">quantity_demand21,</if>
            <if test="quantityDemand22 != null">quantity_demand22,</if>
            <if test="quantityDemand23 != null">quantity_demand23,</if>
            <if test="quantityDemand24 != null">quantity_demand24,</if>
            <if test="quantityDemand25 != null">quantity_demand25,</if>
            <if test="quantityDemand26 != null">quantity_demand26,</if>
            <if test="quantityDemand27 != null">quantity_demand27,</if>
            <if test="quantityDemand28 != null">quantity_demand28,</if>
            <if test="quantityDemand29 != null">quantity_demand29,</if>
            <if test="quantityDemand30 != null">quantity_demand30,</if>
            <if test="quantityDemand31 != null">quantity_demand31,</if>
            <if test="isUpdate != null">is_update,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="releaseEdition != null">#{releaseEdition},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="plantName != null">#{plantName},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="quantityDemand1 != null">#{quantityDemand1},</if>
            <if test="quantityDemand2 != null">#{quantityDemand2},</if>
            <if test="quantityDemand3 != null">#{quantityDemand3},</if>
            <if test="quantityDemand4 != null">#{quantityDemand4},</if>
            <if test="quantityDemand5 != null">#{quantityDemand5},</if>
            <if test="quantityDemand6 != null">#{quantityDemand6},</if>
            <if test="quantityDemand7 != null">#{quantityDemand7},</if>
            <if test="quantityDemand8 != null">#{quantityDemand8},</if>
            <if test="quantityDemand9 != null">#{quantityDemand9},</if>
            <if test="quantityDemand10 != null">#{quantityDemand10},</if>
            <if test="quantityDemand11 != null">#{quantityDemand11},</if>
            <if test="quantityDemand12 != null">#{quantityDemand12},</if>
            <if test="quantityDemand13 != null">#{quantityDemand13},</if>
            <if test="quantityDemand14 != null">#{quantityDemand14},</if>
            <if test="quantityDemand15 != null">#{quantityDemand15},</if>
            <if test="quantityDemand16 != null">#{quantityDemand16},</if>
            <if test="quantityDemand17 != null">#{quantityDemand17},</if>
            <if test="quantityDemand18 != null">#{quantityDemand18},</if>
            <if test="quantityDemand19 != null">#{quantityDemand19},</if>
            <if test="quantityDemand20 != null">#{quantityDemand20},</if>
            <if test="quantityDemand21 != null">#{quantityDemand21},</if>
            <if test="quantityDemand22 != null">#{quantityDemand22},</if>
            <if test="quantityDemand23 != null">#{quantityDemand23},</if>
            <if test="quantityDemand24 != null">#{quantityDemand24},</if>
            <if test="quantityDemand25 != null">#{quantityDemand25},</if>
            <if test="quantityDemand26 != null">#{quantityDemand26},</if>
            <if test="quantityDemand27 != null">#{quantityDemand27},</if>
            <if test="quantityDemand28 != null">#{quantityDemand28},</if>
            <if test="quantityDemand29 != null">#{quantityDemand29},</if>
            <if test="quantityDemand30 != null">#{quantityDemand30},</if>
            <if test="quantityDemand31 != null">#{quantityDemand31},</if>
            <if test="isUpdate != null">#{isUpdate},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierMrpDate" parameterType="SupplierMrpDate">
        update supplier_mrp_date
        <trim prefix="SET" suffixOverrides=",">
            <if test="releaseEdition != null">release_edition = #{releaseEdition},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="plantName != null">plant_name = #{plantName},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="quantityDemand1 != null">quantity_demand1 = #{quantityDemand1},</if>
            <if test="quantityDemand2 != null">quantity_demand2 = #{quantityDemand2},</if>
            <if test="quantityDemand3 != null">quantity_demand3 = #{quantityDemand3},</if>
            <if test="quantityDemand4 != null">quantity_demand4 = #{quantityDemand4},</if>
            <if test="quantityDemand5 != null">quantity_demand5 = #{quantityDemand5},</if>
            <if test="quantityDemand6 != null">quantity_demand6 = #{quantityDemand6},</if>
            <if test="quantityDemand7 != null">quantity_demand7 = #{quantityDemand7},</if>
            <if test="quantityDemand8 != null">quantity_demand8 = #{quantityDemand8},</if>
            <if test="quantityDemand9 != null">quantity_demand9 = #{quantityDemand9},</if>
            <if test="quantityDemand10 != null">quantity_demand10 = #{quantityDemand10},</if>
            <if test="quantityDemand11 != null">quantity_demand11 = #{quantityDemand11},</if>
            <if test="quantityDemand12 != null">quantity_demand12 = #{quantityDemand12},</if>
            <if test="quantityDemand13 != null">quantity_demand13 = #{quantityDemand13},</if>
            <if test="quantityDemand14 != null">quantity_demand14 = #{quantityDemand14},</if>
            <if test="quantityDemand15 != null">quantity_demand15 = #{quantityDemand15},</if>
            <if test="quantityDemand16 != null">quantity_demand16 = #{quantityDemand16},</if>
            <if test="quantityDemand17 != null">quantity_demand17 = #{quantityDemand17},</if>
            <if test="quantityDemand18 != null">quantity_demand18 = #{quantityDemand18},</if>
            <if test="quantityDemand19 != null">quantity_demand19 = #{quantityDemand19},</if>
            <if test="quantityDemand20 != null">quantity_demand20 = #{quantityDemand20},</if>
            <if test="quantityDemand21 != null">quantity_demand21 = #{quantityDemand21},</if>
            <if test="quantityDemand22 != null">quantity_demand22 = #{quantityDemand22},</if>
            <if test="quantityDemand23 != null">quantity_demand23 = #{quantityDemand23},</if>
            <if test="quantityDemand24 != null">quantity_demand24 = #{quantityDemand24},</if>
            <if test="quantityDemand25 != null">quantity_demand25 = #{quantityDemand25},</if>
            <if test="quantityDemand26 != null">quantity_demand26 = #{quantityDemand26},</if>
            <if test="quantityDemand27 != null">quantity_demand27 = #{quantityDemand27},</if>
            <if test="quantityDemand28 != null">quantity_demand28 = #{quantityDemand28},</if>
            <if test="quantityDemand29 != null">quantity_demand29 = #{quantityDemand29},</if>
            <if test="quantityDemand30 != null">quantity_demand30 = #{quantityDemand30},</if>
            <if test="quantityDemand31 != null">quantity_demand31 = #{quantityDemand31},</if>
            <if test="isUpdate != null">is_update = #{isUpdate},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierMrpDateById" parameterType="Long">
        delete from supplier_mrp_date where id = #{id}
    </delete>

    <delete id="deleteSupplierMrpDateByIds" parameterType="String">
        delete from supplier_mrp_date where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        insert into supplier_mrp_date (
            id, release_edition, material_code, material_description, plant_id, plant_name, start_date,
            quantity_demand1, quantity_demand2, quantity_demand3, quantity_demand4, quantity_demand5,
            quantity_demand6, quantity_demand7, quantity_demand8, quantity_demand9, quantity_demand10,
            quantity_demand11, quantity_demand12, quantity_demand13, quantity_demand14, quantity_demand15,
            quantity_demand16, quantity_demand17, quantity_demand18, quantity_demand19, quantity_demand20,
            quantity_demand21, quantity_demand22, quantity_demand23, quantity_demand24, quantity_demand25,
            quantity_demand26, quantity_demand27, quantity_demand28, quantity_demand29, quantity_demand30,
            quantity_demand31, is_update, create_by_user, create_time, update_by_user, update_time,
            is_delete, version, insert_time, modify_time, insert_by, modify_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.releaseEdition}, #{item.materialCode}, #{item.materialDescription},
                #{item.plantId}, #{item.plantName}, #{item.startDate}, #{item.quantityDemand1},
                #{item.quantityDemand2}, #{item.quantityDemand3}, #{item.quantityDemand4}, #{item.quantityDemand5},
                #{item.quantityDemand6}, #{item.quantityDemand7}, #{item.quantityDemand8}, #{item.quantityDemand9},
                #{item.quantityDemand10}, #{item.quantityDemand11}, #{item.quantityDemand12}, #{item.quantityDemand13},
                #{item.quantityDemand14}, #{item.quantityDemand15}, #{item.quantityDemand16}, #{item.quantityDemand17},
                #{item.quantityDemand18}, #{item.quantityDemand19}, #{item.quantityDemand20}, #{item.quantityDemand21},
                #{item.quantityDemand22}, #{item.quantityDemand23}, #{item.quantityDemand24}, #{item.quantityDemand25},
                #{item.quantityDemand26}, #{item.quantityDemand27}, #{item.quantityDemand28}, #{item.quantityDemand29},
                #{item.quantityDemand30}, #{item.quantityDemand31}, #{item.isUpdate}, #{item.createByUser},
                #{item.createTime}, #{item.updateByUser}, #{item.updateTime}, #{item.isDelete}, #{item.version},
                #{item.insertTime}, #{item.modifyTime}, #{item.insertBy}, #{item.modifyBy}
            )
        </foreach>
        on duplicate key update
            release_edition = values(release_edition),
            material_code = values(material_code),
            material_description = values(material_description),
            plant_id = values(plant_id),
            plant_name = values(plant_name),
            start_date = values(start_date),
            quantity_demand1 = values(quantity_demand1),
            quantity_demand2 = values(quantity_demand2),
            quantity_demand3 = values(quantity_demand3),
            quantity_demand4 = values(quantity_demand4),
            quantity_demand5 = values(quantity_demand5),
            quantity_demand6 = values(quantity_demand6),
            quantity_demand7 = values(quantity_demand7),
            quantity_demand8 = values(quantity_demand8),
            quantity_demand9 = values(quantity_demand9),
            quantity_demand10 = values(quantity_demand10),
            quantity_demand11 = values(quantity_demand11),
            quantity_demand12 = values(quantity_demand12),
            quantity_demand13 = values(quantity_demand13),
            quantity_demand14 = values(quantity_demand14),
            quantity_demand15 = values(quantity_demand15),
            quantity_demand16 = values(quantity_demand16),
            quantity_demand17 = values(quantity_demand17),
            quantity_demand18 = values(quantity_demand18),
            quantity_demand19 = values(quantity_demand19),
            quantity_demand20 = values(quantity_demand20),
            quantity_demand21 = values(quantity_demand21),
            quantity_demand22 = values(quantity_demand22),
            quantity_demand23 = values(quantity_demand23),
            quantity_demand24 = values(quantity_demand24),
            quantity_demand25 = values(quantity_demand25),
            quantity_demand26 = values(quantity_demand26),
            quantity_demand27 = values(quantity_demand27),
            quantity_demand28 = values(quantity_demand28),
            quantity_demand29 = values(quantity_demand29),
            quantity_demand30 = values(quantity_demand30),
            quantity_demand31 = values(quantity_demand31),
            is_update = values(is_update),
            create_by_user = values(create_by_user),
            create_time = values(create_time),
            update_by_user = values(update_by_user),
            update_time = values(update_time),
            is_delete = values(is_delete),
            version = values(version),
            modify_time = values(modify_time),
            modify_by = values(modify_by)
    </insert>

</mapper>