<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.pull.SupplierMrpStateMapper">
    
    <resultMap type="SupplierMrpState" id="SupplierMrpStateResult">
        <result property="id"    column="id"    />
        <result property="plantId"    column="plant_id"    />
        <result property="plantName"    column="plant_name"    />
        <result property="demandSrate"    column="demand_srate"    />
        <result property="demandType"    column="demand_type"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="summarySign"    column="summary_sign"    />
        <result property="dateRequired"    column="date_required"    />
        <result property="quantityDemand"    column="quantity_demand"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="creatQuantity"    column="creat_quantity"    />
        <result property="quantityDelivery"    column="quantity_delivery"    />
        <result property="quantityReceive"    column="quantity_receive"    />
        <result property="quantityInTransit"    column="quantity_in_transit"    />
        <result property="onTimePercentage"    column="on_time_percentage"    />
        <result property="summaryCreatQuantity"    column="summary_creat_quantity"    />
        <result property="summaryQuantityDelivery"    column="summary_quantity_delivery"    />
        <result property="summaryQuantityReceive"    column="summary_quantity_receive"    />
        <result property="summaryQuantityInTransit"    column="summary_quantity_in_transit"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierMrpStateVo">
        select id, plant_id, plant_name, demand_srate, demand_type, material_code, material_description, summary_sign, date_required, quantity_demand, confirm_time, creat_quantity, quantity_delivery, quantity_receive, quantity_in_transit, on_time_percentage, summary_creat_quantity, summary_quantity_delivery, summary_quantity_receive, summary_quantity_in_transit, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_mrp_state
    </sql>

    <select id="selectSupplierMrpStateList" parameterType="SupplierMrpState" resultMap="SupplierMrpStateResult">
        <include refid="selectSupplierMrpStateVo"/>
        <where>  
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="demandSrate != null  and demandSrate != ''"> and demand_srate = #{demandSrate}</if>
            <if test="demandType != null  and demandType != ''"> and demand_type = #{demandType}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="summarySign != null  and summarySign != ''"> and summary_sign = #{summarySign}</if>
            <if test="dateRequired != null  and dateRequired != ''"> and date_required = #{dateRequired}</if>
            <if test="quantityDemand != null "> and quantity_demand = #{quantityDemand}</if>
            <if test="confirmTime != null  and confirmTime != ''"> and confirm_time = #{confirmTime}</if>
            <if test="creatQuantity != null "> and creat_quantity = #{creatQuantity}</if>
            <if test="quantityDelivery != null "> and quantity_delivery = #{quantityDelivery}</if>
            <if test="quantityReceive != null "> and quantity_receive = #{quantityReceive}</if>
            <if test="quantityInTransit != null "> and quantity_in_transit = #{quantityInTransit}</if>
            <if test="onTimePercentage != null "> and on_time_percentage = #{onTimePercentage}</if>
            <if test="summaryCreatQuantity != null "> and summary_creat_quantity = #{summaryCreatQuantity}</if>
            <if test="summaryQuantityDelivery != null "> and summary_quantity_delivery = #{summaryQuantityDelivery}</if>
            <if test="summaryQuantityReceive != null "> and summary_quantity_receive = #{summaryQuantityReceive}</if>
            <if test="summaryQuantityInTransit != null "> and summary_quantity_in_transit = #{summaryQuantityInTransit}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierMrpStateById" parameterType="Long" resultMap="SupplierMrpStateResult">
        <include refid="selectSupplierMrpStateVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierMrpState" parameterType="SupplierMrpState" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_mrp_state
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="plantId != null">plant_id,</if>
            <if test="plantName != null">plant_name,</if>
            <if test="demandSrate != null">demand_srate,</if>
            <if test="demandType != null">demand_type,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="summarySign != null">summary_sign,</if>
            <if test="dateRequired != null">date_required,</if>
            <if test="quantityDemand != null">quantity_demand,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="creatQuantity != null">creat_quantity,</if>
            <if test="quantityDelivery != null">quantity_delivery,</if>
            <if test="quantityReceive != null">quantity_receive,</if>
            <if test="quantityInTransit != null">quantity_in_transit,</if>
            <if test="onTimePercentage != null">on_time_percentage,</if>
            <if test="summaryCreatQuantity != null">summary_creat_quantity,</if>
            <if test="summaryQuantityDelivery != null">summary_quantity_delivery,</if>
            <if test="summaryQuantityReceive != null">summary_quantity_receive,</if>
            <if test="summaryQuantityInTransit != null">summary_quantity_in_transit,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="plantId != null">#{plantId},</if>
            <if test="plantName != null">#{plantName},</if>
            <if test="demandSrate != null">#{demandSrate},</if>
            <if test="demandType != null">#{demandType},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="summarySign != null">#{summarySign},</if>
            <if test="dateRequired != null">#{dateRequired},</if>
            <if test="quantityDemand != null">#{quantityDemand},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="creatQuantity != null">#{creatQuantity},</if>
            <if test="quantityDelivery != null">#{quantityDelivery},</if>
            <if test="quantityReceive != null">#{quantityReceive},</if>
            <if test="quantityInTransit != null">#{quantityInTransit},</if>
            <if test="onTimePercentage != null">#{onTimePercentage},</if>
            <if test="summaryCreatQuantity != null">#{summaryCreatQuantity},</if>
            <if test="summaryQuantityDelivery != null">#{summaryQuantityDelivery},</if>
            <if test="summaryQuantityReceive != null">#{summaryQuantityReceive},</if>
            <if test="summaryQuantityInTransit != null">#{summaryQuantityInTransit},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierMrpState" parameterType="SupplierMrpState">
        update supplier_mrp_state
        <trim prefix="SET" suffixOverrides=",">
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="plantName != null">plant_name = #{plantName},</if>
            <if test="demandSrate != null">demand_srate = #{demandSrate},</if>
            <if test="demandType != null">demand_type = #{demandType},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="summarySign != null">summary_sign = #{summarySign},</if>
            <if test="dateRequired != null">date_required = #{dateRequired},</if>
            <if test="quantityDemand != null">quantity_demand = #{quantityDemand},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="creatQuantity != null">creat_quantity = #{creatQuantity},</if>
            <if test="quantityDelivery != null">quantity_delivery = #{quantityDelivery},</if>
            <if test="quantityReceive != null">quantity_receive = #{quantityReceive},</if>
            <if test="quantityInTransit != null">quantity_in_transit = #{quantityInTransit},</if>
            <if test="onTimePercentage != null">on_time_percentage = #{onTimePercentage},</if>
            <if test="summaryCreatQuantity != null">summary_creat_quantity = #{summaryCreatQuantity},</if>
            <if test="summaryQuantityDelivery != null">summary_quantity_delivery = #{summaryQuantityDelivery},</if>
            <if test="summaryQuantityReceive != null">summary_quantity_receive = #{summaryQuantityReceive},</if>
            <if test="summaryQuantityInTransit != null">summary_quantity_in_transit = #{summaryQuantityInTransit},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierMrpStateById" parameterType="Long">
        delete from supplier_mrp_state where id = #{id}
    </delete>

    <delete id="deleteSupplierMrpStateByIds" parameterType="String">
        delete from supplier_mrp_state where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        insert into supplier_mrp_state (
            id, plant_id, plant_name, demand_srate, demand_type, material_code, material_description,
            summary_sign, date_required, quantity_demand, confirm_time, creat_quantity, quantity_delivery,
            quantity_receive, quantity_in_transit, on_time_percentage, summary_creat_quantity,
            summary_quantity_delivery, summary_quantity_receive, summary_quantity_in_transit,
            create_by_user, create_time, update_by_user, update_time, is_delete, version,
            insert_time, modify_time, insert_by, modify_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.plantId}, #{item.plantName}, #{item.demandSrate}, #{item.demandType},
                #{item.materialCode}, #{item.materialDescription}, #{item.summarySign}, #{item.dateRequired},
                #{item.quantityDemand}, #{item.confirmTime}, #{item.creatQuantity}, #{item.quantityDelivery},
                #{item.quantityReceive}, #{item.quantityInTransit}, #{item.onTimePercentage},
                #{item.summaryCreatQuantity}, #{item.summaryQuantityDelivery}, #{item.summaryQuantityReceive},
                #{item.summaryQuantityInTransit}, #{item.createByUser}, #{item.createTime},
                #{item.updateByUser}, #{item.updateTime}, #{item.isDelete}, #{item.version},
                #{item.insertTime}, #{item.modifyTime}, #{item.insertBy}, #{item.modifyBy}
            )
        </foreach>
        on duplicate key update
            plant_id = values(plant_id),
            plant_name = values(plant_name),
            demand_srate = values(demand_srate),
            demand_type = values(demand_type),
            material_code = values(material_code),
            material_description = values(material_description),
            summary_sign = values(summary_sign),
            date_required = values(date_required),
            quantity_demand = values(quantity_demand),
            confirm_time = values(confirm_time),
            creat_quantity = values(creat_quantity),
            quantity_delivery = values(quantity_delivery),
            quantity_receive = values(quantity_receive),
            quantity_in_transit = values(quantity_in_transit),
            on_time_percentage = values(on_time_percentage),
            summary_creat_quantity = values(summary_creat_quantity),
            summary_quantity_delivery = values(summary_quantity_delivery),
            summary_quantity_receive = values(summary_quantity_receive),
            summary_quantity_in_transit = values(summary_quantity_in_transit),
            create_by_user = values(create_by_user),
            create_time = values(create_time),
            update_by_user = values(update_by_user),
            update_time = values(update_time),
            is_delete = values(is_delete),
            version = values(version),
            modify_time = values(modify_time),
            modify_by = values(modify_by)
    </insert>

</mapper>