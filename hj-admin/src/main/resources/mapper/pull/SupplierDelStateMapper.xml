<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.pull.SupplierDelStateMapper">
    
    <resultMap type="SupplierDelState" id="SupplierDelStateResult">
        <result property="id"    column="id"    />
        <result property="deliveryNumber"    column="delivery_number"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="serialSrate"    column="serial_srate"    />
        <result property="materialCode"    column="material_code"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="plantId"    column="plant_id"    />
        <result property="receivingCrossings"    column="receiving_crossings"    />
        <result property="quantityDelivery"    column="quantity_delivery"    />
        <result property="dataCreateTime"    column="data_create_time"    />
        <result property="supplierReceiveTime"    column="supplier_receive_time"    />
        <result property="roadShippedTime"    column="road_shipped_time"    />
        <result property="roadReceiveTime"    column="road_receive_time"    />
        <result property="lastRequrieTime"    column="last_requrie_time"    />
        <result property="createByUser"    column="create_by_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateByUser"    column="update_by_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="version"    column="version"    />
        <result property="insertTime"    column="insert_time"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="insertBy"    column="insert_by"    />
        <result property="modifyBy"    column="modify_by"    />
    </resultMap>

    <sql id="selectSupplierDelStateVo">
        select id, delivery_number, serial_number, serial_srate, material_code, material_description, plant_id, receiving_crossings, quantity_delivery, data_create_time, supplier_receive_time, road_shipped_time, road_receive_time, last_requrie_time, create_by_user, create_time, update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by from supplier_del_state
    </sql>

    <select id="selectSupplierDelStateList" parameterType="SupplierDelState" resultMap="SupplierDelStateResult">
        <include refid="selectSupplierDelStateVo"/>
        <where>  
            <if test="deliveryNumber != null  and deliveryNumber != ''"> and delivery_number = #{deliveryNumber}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and serial_number = #{serialNumber}</if>
            <if test="serialSrate != null  and serialSrate != ''"> and serial_srate = #{serialSrate}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description = #{materialDescription}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if test="receivingCrossings != null  and receivingCrossings != ''"> and receiving_crossings = #{receivingCrossings}</if>
            <if test="quantityDelivery != null "> and quantity_delivery = #{quantityDelivery}</if>
            <if test="dataCreateTime != null  and dataCreateTime != ''"> and data_create_time = #{dataCreateTime}</if>
            <if test="supplierReceiveTime != null  and supplierReceiveTime != ''"> and supplier_receive_time = #{supplierReceiveTime}</if>
            <if test="roadShippedTime != null  and roadShippedTime != ''"> and road_shipped_time = #{roadShippedTime}</if>
            <if test="roadReceiveTime != null  and roadReceiveTime != ''"> and road_receive_time = #{roadReceiveTime}</if>
            <if test="lastRequrieTime != null  and lastRequrieTime != ''"> and last_requrie_time = #{lastRequrieTime}</if>
            <if test="createByUser != null  and createByUser != ''"> and create_by_user = #{createByUser}</if>
            <if test="updateByUser != null  and updateByUser != ''"> and update_by_user = #{updateByUser}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
            <if test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
        </where>
    </select>
    
    <select id="selectSupplierDelStateById" parameterType="Long" resultMap="SupplierDelStateResult">
        <include refid="selectSupplierDelStateVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierDelState" parameterType="SupplierDelState">
        insert into supplier_del_state
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deliveryNumber != null">delivery_number,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="serialSrate != null">serial_srate,</if>
            <if test="materialCode != null">material_code,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="plantId != null">plant_id,</if>
            <if test="receivingCrossings != null">receiving_crossings,</if>
            <if test="quantityDelivery != null">quantity_delivery,</if>
            <if test="dataCreateTime != null">data_create_time,</if>
            <if test="supplierReceiveTime != null">supplier_receive_time,</if>
            <if test="roadShippedTime != null">road_shipped_time,</if>
            <if test="roadReceiveTime != null">road_receive_time,</if>
            <if test="lastRequrieTime != null">last_requrie_time,</if>
            <if test="createByUser != null">create_by_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateByUser != null">update_by_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="version != null">version,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deliveryNumber != null">#{deliveryNumber},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="serialSrate != null">#{serialSrate},</if>
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="plantId != null">#{plantId},</if>
            <if test="receivingCrossings != null">#{receivingCrossings},</if>
            <if test="quantityDelivery != null">#{quantityDelivery},</if>
            <if test="dataCreateTime != null">#{dataCreateTime},</if>
            <if test="supplierReceiveTime != null">#{supplierReceiveTime},</if>
            <if test="roadShippedTime != null">#{roadShippedTime},</if>
            <if test="roadReceiveTime != null">#{roadReceiveTime},</if>
            <if test="lastRequrieTime != null">#{lastRequrieTime},</if>
            <if test="createByUser != null">#{createByUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateByUser != null">#{updateByUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="version != null">#{version},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
         </trim>
    </insert>

    <update id="updateSupplierDelState" parameterType="SupplierDelState">
        update supplier_del_state
        <trim prefix="SET" suffixOverrides=",">
            <if test="deliveryNumber != null">delivery_number = #{deliveryNumber},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="serialSrate != null">serial_srate = #{serialSrate},</if>
            <if test="materialCode != null">material_code = #{materialCode},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="plantId != null">plant_id = #{plantId},</if>
            <if test="receivingCrossings != null">receiving_crossings = #{receivingCrossings},</if>
            <if test="quantityDelivery != null">quantity_delivery = #{quantityDelivery},</if>
            <if test="dataCreateTime != null">data_create_time = #{dataCreateTime},</if>
            <if test="supplierReceiveTime != null">supplier_receive_time = #{supplierReceiveTime},</if>
            <if test="roadShippedTime != null">road_shipped_time = #{roadShippedTime},</if>
            <if test="roadReceiveTime != null">road_receive_time = #{roadReceiveTime},</if>
            <if test="lastRequrieTime != null">last_requrie_time = #{lastRequrieTime},</if>
            <if test="createByUser != null">create_by_user = #{createByUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateByUser != null">update_by_user = #{updateByUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="version != null">version = #{version},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierDelStateById" parameterType="Long">
        delete from supplier_del_state where id = #{id}
    </delete>

    <delete id="deleteSupplierDelStateByIds" parameterType="String">
        delete from supplier_del_state where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        insert into supplier_del_state (
            id, delivery_number, serial_number, serial_srate, material_code, material_description,
            plant_id, receiving_crossings, quantity_delivery, data_create_time, supplier_receive_time,
            road_shipped_time, road_receive_time, last_requrie_time, create_by_user, create_time,
            update_by_user, update_time, is_delete, version, insert_time, modify_time, insert_by, modify_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.deliveryNumber}, #{item.serialNumber}, #{item.serialSrate},
                #{item.materialCode}, #{item.materialDescription}, #{item.plantId}, #{item.receivingCrossings},
                #{item.quantityDelivery}, #{item.dataCreateTime}, #{item.supplierReceiveTime},
                #{item.roadShippedTime}, #{item.roadReceiveTime}, #{item.lastRequrieTime},
                #{item.createByUser}, #{item.createTime}, #{item.updateByUser}, #{item.updateTime},
                #{item.isDelete}, #{item.version}, #{item.insertTime}, #{item.modifyTime},
                #{item.insertBy}, #{item.modifyBy}
            )
        </foreach>
        on duplicate key update
            delivery_number = values(delivery_number),
            serial_number = values(serial_number),
            serial_srate = values(serial_srate),
            material_code = values(material_code),
            material_description = values(material_description),
            plant_id = values(plant_id),
            receiving_crossings = values(receiving_crossings),
            quantity_delivery = values(quantity_delivery),
            data_create_time = values(data_create_time),
            supplier_receive_time = values(supplier_receive_time),
            road_shipped_time = values(road_shipped_time),
            road_receive_time = values(road_receive_time),
            last_requrie_time = values(last_requrie_time),
            create_by_user = values(create_by_user),
            create_time = values(create_time),
            update_by_user = values(update_by_user),
            update_time = values(update_time),
            is_delete = values(is_delete),
            version = values(version),
            modify_time = values(modify_time),
            modify_by = values(modify_by)
    </insert>

</mapper>