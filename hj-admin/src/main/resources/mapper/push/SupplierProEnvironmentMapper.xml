<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierProEnvironmentMapper">

    <resultMap type="SupplierProEnvironment" id="SupplierProEnvironmentResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="plantId" column="plant_id" />
        <result property="plantName" column="plant_name" />
        <result property="workshopId" column="workshop_id" />
        <result property="workshopName" column="workshop_name" />
        <result property="productionLineId" column="production_line_id" />
        <result property="productionLineName" column="production_line_name" />
        <result property="envIndicatorName" column="env_indicator_name" />
        <result property="numValue" column="num_value" />
        <result property="upperLimit" column="upper_limit" />
        <result property="lowerLimit" column="lower_limit" />
        <result property="chineseUnit" column="chinese_unit" />
        <result property="equipmentCode" column="equipment_code" />
        <result property="equipmentName" column="equipment_name" />
        <result property="dataCollectionPoint" column="data_collection_point" />
        <result property="collectTime" column="collect_time" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierProEnvironmentVo"> select id, supplier_code, supplier_name, plant_id,
        plant_name, workshop_id, workshop_name, production_line_id, production_line_name,
        env_indicator_name, num_value, upper_limit, lower_limit, chinese_unit, equipment_code,
        equipment_name, data_collection_point, collect_time, has_push, insert_time, modify_time,
        insert_by, modify_by from supplier_pro_environment </sql>

    <select id="selectSupplierProEnvironmentList" parameterType="SupplierProEnvironment"
        resultMap="SupplierProEnvironmentResult">
        <include refid="selectSupplierProEnvironmentVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and
        supplier_name like concat('%', #{supplierName}, '%')</if>
            <if
                test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if
                test="plantName != null  and plantName != ''"> and plant_name like concat('%',
        #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and workshop_id
        = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and
        workshop_name like concat('%', #{workshopName}, '%')</if>
            <if
                test="productionLineId != null  and productionLineId != ''"> and production_line_id
        = #{productionLineId}</if>
            <if
                test="productionLineName != null  and productionLineName != ''"> and
        production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if
                test="envIndicatorName != null  and envIndicatorName != ''"> and env_indicator_name
        like concat('%', #{envIndicatorName}, '%')</if>
            <if test="numValue != null "> and num_value =
        #{numValue}</if>
            <if test="upperLimit != null "> and upper_limit = #{upperLimit}</if>
            <if
                test="lowerLimit != null "> and lower_limit = #{lowerLimit}</if>
            <if
                test="chineseUnit != null  and chineseUnit != ''"> and chinese_unit = #{chineseUnit}</if>
            <if
                test="equipmentCode != null  and equipmentCode != ''"> and equipment_code =
        #{equipmentCode}</if>
            <if test="equipmentName != null  and equipmentName != ''"> and
        equipment_name like concat('%', #{equipmentName}, '%')</if>
            <if
                test="dataCollectionPoint != null  and dataCollectionPoint != ''"> and
        data_collection_point = #{dataCollectionPoint}</if>
            <if
                test="collectTime != null  and collectTime != ''"> and collect_time = #{collectTime}</if>
            <if
                test="hasPush != null "> and has_push = #{hasPush}</if>
            <if
                test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
              <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
<if
                    test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>
        </where>
    </select>

    <select id="selectSupplierProEnvironmentById" parameterType="Long"
        resultMap="SupplierProEnvironmentResult">
        <include refid="selectSupplierProEnvironmentVo" /> where id = #{id} </select>

    <insert id="insertSupplierProEnvironment" parameterType="SupplierProEnvironment"
        useGeneratedKeys="true" keyProperty="id"> insert into supplier_pro_environment <trim
            prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if
                test="plantId != null and plantId != ''">plant_id,</if>
            <if
                test="plantName != null and plantName != ''">plant_name,</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if
                test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if
                test="envIndicatorName != null and envIndicatorName != ''">env_indicator_name,</if>
            <if
                test="numValue != null">num_value,</if>
            <if test="upperLimit != null">upper_limit,</if>
            <if
                test="lowerLimit != null">lower_limit,</if>
            <if
                test="chineseUnit != null and chineseUnit != ''">chinese_unit,</if>
            <if
                test="equipmentCode != null and equipmentCode != ''">equipment_code,</if>
            <if
                test="equipmentName != null and equipmentName != ''">equipment_name,</if>
            <if
                test="dataCollectionPoint != null and dataCollectionPoint != ''">
        data_collection_point,</if>
            <if test="collectTime != null and collectTime != ''">
        collect_time,</if>
            <if test="hasPush != null">has_push,</if>
            <if test="insertTime != null">
        insert_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if
                test="plantId != null and plantId != ''">#{plantId},</if>
            <if
                test="plantName != null and plantName != ''">#{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if
                test="productionLineName != null and productionLineName != ''">
        #{productionLineName},</if>
            <if test="envIndicatorName != null and envIndicatorName != ''">
        #{envIndicatorName},</if>
            <if test="numValue != null">#{numValue},</if>
            <if
                test="upperLimit != null">#{upperLimit},</if>
            <if test="lowerLimit != null">
        #{lowerLimit},</if>
            <if test="chineseUnit != null and chineseUnit != ''">#{chineseUnit},</if>
            <if
                test="equipmentCode != null and equipmentCode != ''">#{equipmentCode},</if>
            <if
                test="equipmentName != null and equipmentName != ''">#{equipmentName},</if>
            <if
                test="dataCollectionPoint != null and dataCollectionPoint != ''">
        #{dataCollectionPoint},</if>
            <if test="collectTime != null and collectTime != ''">
        #{collectTime},</if>
            <if test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">
        #{insertTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierProEnvironment" parameterType="SupplierProEnvironment"> update
        supplier_pro_environment <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if
                test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if
                test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id =
        #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name
        = #{productionLineName},</if>
            <if test="envIndicatorName != null and envIndicatorName != ''">env_indicator_name
        = #{envIndicatorName},</if>
            <if test="numValue != null">num_value = #{numValue},</if>
            <if
                test="upperLimit != null">upper_limit = #{upperLimit},</if>
            <if
                test="lowerLimit != null">lower_limit = #{lowerLimit},</if>
            <if
                test="chineseUnit != null and chineseUnit != ''">chinese_unit = #{chineseUnit},</if>
            <if
                test="equipmentCode != null and equipmentCode != ''">equipment_code =
        #{equipmentCode},</if>
            <if test="equipmentName != null and equipmentName != ''">equipment_name
        = #{equipmentName},</if>
            <if test="dataCollectionPoint != null and dataCollectionPoint != ''">data_collection_point
        = #{dataCollectionPoint},</if>
            <if test="collectTime != null and collectTime != ''">collect_time
        = #{collectTime},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if
                test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_pro_environment <set>
                <if test="item.supplierCode != null and item.supplierCode != ''">supplier_code =
        #{item.supplierCode},</if>
                <if test="item.supplierName != null and item.supplierName != ''">supplier_name
        = #{item.supplierName},</if>
                <if test="item.plantId != null and item.plantId != ''">plant_id
        = #{item.plantId},</if>
                <if test="item.plantName != null and item.plantName != ''">plant_name
        = #{item.plantName},</if>
                <if test="item.workshopId != null and item.workshopId != ''">workshop_id
        = #{item.workshopId},</if>
                <if test="item.workshopName != null and item.workshopName != ''">workshop_name
        = #{item.workshopName},</if>
                <if
                    test="item.productionLineId != null and item.productionLineId != ''">production_line_id
        = #{item.productionLineId},</if>
                <if
                    test="item.productionLineName != null and item.productionLineName != ''">production_line_name
        = #{item.productionLineName},</if>
                <if
                    test="item.envIndicatorName != null and item.envIndicatorName != ''">env_indicator_name
        = #{item.envIndicatorName},</if>
                <if test="item.numValue != null">num_value =
        #{item.numValue},</if>
                <if test="item.upperLimit != null">upper_limit = #{item.upperLimit},</if>
                <if
                    test="item.lowerLimit != null">lower_limit = #{item.lowerLimit},</if>
                <if
                    test="item.chineseUnit != null and item.chineseUnit != ''">chinese_unit =
        #{item.chineseUnit},</if>
                <if test="item.equipmentCode != null and item.equipmentCode != ''">equipment_code
        = #{item.equipmentCode},</if>
                <if
                    test="item.equipmentName != null and item.equipmentName != ''">equipment_name =
        #{item.equipmentName},</if>
                <if
                    test="item.dataCollectionPoint != null and item.dataCollectionPoint != ''">data_collection_point
        = #{item.dataCollectionPoint},</if>
                <if
                    test="item.collectTime != null and item.collectTime != ''">collect_time =
        #{item.collectTime},</if>
                <if test="item.hasPush != null">has_push = #{item.hasPush},</if>
                <if
                    test="item.insertTime != null">insert_time = #{item.insertTime},</if>
                <if
                    test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if
                    test="item.insertBy != null and item.insertBy != ''">insert_by =
        #{item.insertBy},</if>
            <if
                    test="item.outBatchNo != null and item.outBatchNo != ''">out_batch_no =
                #{item.outBatchNo}</if>

            </set> where id = #{item.id} </foreach>
    </update>

    <delete id="deleteSupplierProEnvironmentById" parameterType="Long"> delete from
        supplier_pro_environment where id = #{id} </delete>

    <delete id="deleteSupplierProEnvironmentByIds" parameterType="String"> delete from
        supplier_pro_environment where id in <foreach item="id" collection="array" open="("
            separator="," close=")"> #{id} </foreach>
    </delete>

    <select id="selectSupplierProEnvironmentByIds" resultMap="SupplierProEnvironmentResult">
        <include refid="selectSupplierProEnvironmentVo" /> where out_batch_no = #{batchNo}
    </select>
</mapper>