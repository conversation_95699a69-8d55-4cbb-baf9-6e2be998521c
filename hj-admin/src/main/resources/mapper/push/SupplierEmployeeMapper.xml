<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierEmployeeMapper">

    <resultMap type="SupplierEmployee" id="SupplierEmployeeResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="plantId" column="plant_id" />
        <result property="plantName" column="plant_name" />
        <result property="workshopId" column="workshop_id" />
        <result property="workshopName" column="workshop_name" />
        <result property="productionLineId" column="production_line_id" />
        <result property="productionLineName" column="production_line_name" />
        <result property="stationId" column="station_id" />
        <result property="stationName" column="station_name" />
        <result property="operatorId" column="operator_id" />
        <result property="operatorName" column="operator_name" />
        <result property="haveQuantity" column="have_quantity" />
        <result property="dataUpdateTime" column="data_update_time" />
        <result property="positionId" column="position_id" />
        <result property="positionName" column="position_name" />
        <result property="qualificationLevel" column="qualification_level" />
        <result property="checkInTime" column="check_in_time" />
        <result property="checkOutTime" column="check_out_time" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierEmployeeVo"> select id, supplier_code, supplier_name, plant_id,
        plant_name, workshop_id, workshop_name, production_line_id, production_line_name,
        station_id, station_name, operator_id, operator_name, have_quantity, data_update_time,
        position_id, position_name, qualification_level, check_in_time, check_out_time, has_push,
        insert_time, modify_time, insert_by, modify_by from supplier_employee </sql>

    <select id="selectSupplierEmployeeList" parameterType="SupplierEmployee"
        resultMap="SupplierEmployeeResult">
        <include refid="selectSupplierEmployeeVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and
        supplier_name like concat('%', #{supplierName}, '%')</if>
            <if
                test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if
                test="plantName != null  and plantName != ''"> and plant_name like concat('%',
        #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and workshop_id
        = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and
        workshop_name like concat('%', #{workshopName}, '%')</if>
            <if
                test="productionLineId != null  and productionLineId != ''"> and production_line_id
        = #{productionLineId}</if>
            <if
                test="productionLineName != null  and productionLineName != ''"> and
        production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if
                test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if
                test="stationName != null  and stationName != ''"> and station_name like concat('%',
        #{stationName}, '%')</if>
            <if test="operatorId != null  and operatorId != ''"> and
        operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and
        operator_name like concat('%', #{operatorName}, '%')</if>
            <if
                test="haveQuantity != null  and haveQuantity != ''"> and have_quantity =
        #{haveQuantity}</if>
            <if test="dataUpdateTime != null  and dataUpdateTime != ''"> and
        data_update_time = #{dataUpdateTime}</if>
            <if test="positionId != null  and positionId != ''">
        and position_id = #{positionId}</if>
            <if test="positionName != null  and positionName != ''">
        and position_name like concat('%', #{positionName}, '%')</if>
            <if
                test="qualificationLevel != null  and qualificationLevel != ''"> and
        qualification_level = #{qualificationLevel}</if>
            <if
                test="checkInTime != null  and checkInTime != ''"> and check_in_time =
        #{checkInTime}</if>
            <if test="checkOutTime != null  and checkOutTime != ''"> and
        check_out_time = #{checkOutTime}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if
                test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
              <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
<if
                    test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>
        </where>
    </select>

    <select id="selectSupplierEmployeeById" parameterType="Long" resultMap="SupplierEmployeeResult">
        <include refid="selectSupplierEmployeeVo" /> where id = #{id} </select>

    <insert id="insertSupplierEmployee" parameterType="SupplierEmployee" useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_employee <trim prefix="(" suffix=")"
            suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if
                test="plantId != null and plantId != ''">plant_id,</if>
            <if
                test="plantName != null and plantName != ''">plant_name,</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if
                test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if
                test="stationId != null and stationId != ''">station_id,</if>
            <if
                test="stationName != null and stationName != ''">station_name,</if>
            <if
                test="operatorId != null and operatorId != ''">operator_id,</if>
            <if
                test="operatorName != null and operatorName != ''">operator_name,</if>
            <if
                test="haveQuantity != null and haveQuantity != ''">have_quantity,</if>
            <if
                test="dataUpdateTime != null and dataUpdateTime != ''">data_update_time,</if>
            <if
                test="positionId != null and positionId != ''">position_id,</if>
            <if
                test="positionName != null and positionName != ''">position_name,</if>
            <if
                test="qualificationLevel != null and qualificationLevel != ''">qualification_level,</if>
            <if
                test="checkInTime != null and checkInTime != ''">check_in_time,</if>
            <if
                test="checkOutTime != null and checkOutTime != ''">check_out_time,</if>
            <if
                test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if
                test="modifyTime != null">modify_time,</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if
                test="plantId != null and plantId != ''">#{plantId},</if>
            <if
                test="plantName != null and plantName != ''">#{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if
                test="productionLineName != null and productionLineName != ''">
        #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if
                test="stationName != null and stationName != ''">#{stationName},</if>
            <if
                test="operatorId != null and operatorId != ''">#{operatorId},</if>
            <if
                test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if
                test="haveQuantity != null and haveQuantity != ''">#{haveQuantity},</if>
            <if
                test="dataUpdateTime != null and dataUpdateTime != ''">#{dataUpdateTime},</if>
            <if
                test="positionId != null and positionId != ''">#{positionId},</if>
            <if
                test="positionName != null and positionName != ''">#{positionName},</if>
            <if
                test="qualificationLevel != null and qualificationLevel != ''">
        #{qualificationLevel},</if>
            <if test="checkInTime != null and checkInTime != ''">
        #{checkInTime},</if>
            <if test="checkOutTime != null and checkOutTime != ''">#{checkOutTime},</if>
            <if
                test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if
                test="modifyTime != null">#{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierEmployee" parameterType="SupplierEmployee"> update supplier_employee <trim
            prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if
                test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if
                test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id =
        #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name
        = #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">station_id =
        #{stationId},</if>
            <if test="stationName != null and stationName != ''">station_name =
        #{stationName},</if>
            <if test="operatorId != null and operatorId != ''">operator_id =
        #{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">operator_name =
        #{operatorName},</if>
            <if test="haveQuantity != null and haveQuantity != ''">have_quantity =
        #{haveQuantity},</if>
            <if test="dataUpdateTime != null and dataUpdateTime != ''">data_update_time
        = #{dataUpdateTime},</if>
            <if test="positionId != null and positionId != ''">position_id =
        #{positionId},</if>
            <if test="positionName != null and positionName != ''">position_name =
        #{positionName},</if>
            <if test="qualificationLevel != null and qualificationLevel != ''">qualification_level
        = #{qualificationLevel},</if>
            <if test="checkInTime != null and checkInTime != ''">check_in_time
        = #{checkInTime},</if>
            <if test="checkOutTime != null and checkOutTime != ''">check_out_time
        = #{checkOutTime},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if
                test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_employee <set>
                <if test="item.supplierCode != null and item.supplierCode != ''">supplier_code =
        #{item.supplierCode},</if>
                <if test="item.supplierName != null and item.supplierName != ''">supplier_name
        = #{item.supplierName},</if>
                <if test="item.plantId != null and item.plantId != ''">plant_id
        = #{item.plantId},</if>
                <if test="item.plantName != null and item.plantName != ''">plant_name
        = #{item.plantName},</if>
                <if test="item.workshopId != null and item.workshopId != ''">workshop_id
        = #{item.workshopId},</if>
                <if test="item.workshopName != null and item.workshopName != ''">workshop_name
        = #{item.workshopName},</if>
                <if
                    test="item.productionLineId != null and item.productionLineId != ''">production_line_id
        = #{item.productionLineId},</if>
                <if
                    test="item.productionLineName != null and item.productionLineName != ''">production_line_name
        = #{item.productionLineName},</if>
                <if test="item.stationId != null and item.stationId != ''">station_id
        = #{item.stationId},</if>
                <if test="item.stationName != null and item.stationName != ''">station_name
        = #{item.stationName},</if>
                <if test="item.operatorId != null and item.operatorId != ''">operator_id
        = #{item.operatorId},</if>
                <if test="item.operatorName != null and item.operatorName != ''">operator_name
        = #{item.operatorName},</if>
                <if test="item.haveQuantity != null and item.haveQuantity != ''">have_quantity
        = #{item.haveQuantity},</if>
                <if
                    test="item.dataUpdateTime != null and item.dataUpdateTime != ''">data_update_time
        = #{item.dataUpdateTime},</if>
                <if test="item.positionId != null and item.positionId != ''">position_id
        = #{item.positionId},</if>
                <if test="item.positionName != null and item.positionName != ''">position_name
        = #{item.positionName},</if>
                <if
                    test="item.qualificationLevel != null and item.qualificationLevel != ''">qualification_level
        = #{item.qualificationLevel},</if>
                <if
                    test="item.checkInTime != null and item.checkInTime != ''">check_in_time =
        #{item.checkInTime},</if>
                <if test="item.checkOutTime != null and item.checkOutTime != ''">check_out_time
        = #{item.checkOutTime},</if>
                <if test="item.hasPush != null">has_push = #{item.hasPush},</if>
                <if
                    test="item.insertTime != null">insert_time = #{item.insertTime},</if>
                <if
                    test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if
                    test="item.insertBy != null and item.insertBy != ''">insert_by =
        #{item.insertBy},</if>
            <if
                    test="item.outBatchNo != null and item.outBatchNo != ''">out_batch_no =
                #{item.outBatchNo}</if>

            </set> where id = #{item.id} </foreach>
    </update>

    <delete id="deleteSupplierEmployeeById" parameterType="Long"> delete from supplier_employee
        where id = #{id} </delete>

    <delete id="deleteSupplierEmployeeByIds" parameterType="String"> delete from supplier_employee
        where id in <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>

    <select id="selectSupplierEmployeeByIds" resultMap="SupplierEmployeeResult">
        <include refid="selectSupplierEmployeeVo" /> where out_batch_no = #{batchNo}
    </select>

</mapper>