<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierConPoMapper">

    <resultMap type="SupplierConPo" id="SupplierConPoResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="purchaseOrder" column="purchase_order" />
        <result property="serialNumber" column="serial_number" />
        <result property="quantityMeet" column="quantity_meet" />
        <result property="feedbackResults" column="feedback_results" />
        <result property="ventureType" column="venture_type" />
        <result property="ventureSpecific" column="venture_specific" />
        <result property="measures" column="measures" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierConPoVo"> select id, supplier_code, purchase_order, serial_number,
        quantity_meet, feedback_results, venture_type, venture_specific, measures, has_push,
        insert_time, modify_time, insert_by, modify_by from supplier_con_po </sql>

    <select id="selectSupplierConPoList" parameterType="SupplierConPo"
        resultMap="SupplierConPoResult">
        <include refid="selectSupplierConPoVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode}</if>
            <if test="purchaseOrder != null  and purchaseOrder != ''"> and
        purchase_order = #{purchaseOrder}</if>
            <if
                test="serialNumber != null  and serialNumber != ''"> and serial_number =
        #{serialNumber}</if>
            <if test="quantityMeet != null  and quantityMeet != ''"> and
        quantity_meet = #{quantityMeet}</if>
            <if
                test="feedbackResults != null  and feedbackResults != ''"> and feedback_results =
        #{feedbackResults}</if>
            <if test="ventureType != null  and ventureType != ''"> and
        venture_type = #{ventureType}</if>
            <if
                test="ventureSpecific != null  and ventureSpecific != ''"> and venture_specific =
        #{ventureSpecific}</if>
            <if test="measures != null  and measures != ''"> and measures =
        #{measures}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if
                test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
              <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
<if
                    test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>

        </where>
    </select>

    <select id="selectSupplierConPoById" parameterType="Long" resultMap="SupplierConPoResult">
        <include refid="selectSupplierConPoVo" /> where id = #{id} </select>

    <insert id="insertSupplierConPo" parameterType="SupplierConPo" useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_con_po <trim prefix="(" suffix=")"
            suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if
                test="purchaseOrder != null and purchaseOrder != ''">purchase_order,</if>
            <if
                test="serialNumber != null and serialNumber != ''">serial_number,</if>
            <if
                test="quantityMeet != null and quantityMeet != ''">quantity_meet,</if>
            <if
                test="feedbackResults != null and feedbackResults != ''">feedback_results,</if>
            <if
                test="ventureType != null and ventureType != ''">venture_type,</if>
            <if
                test="ventureSpecific != null and ventureSpecific != ''">venture_specific,</if>
            <if
                test="measures != null and measures != ''">measures,</if>
            <if test="hasPush != null">
        has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">
        modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if
                test="purchaseOrder != null and purchaseOrder != ''">#{purchaseOrder},</if>
            <if
                test="serialNumber != null and serialNumber != ''">#{serialNumber},</if>
            <if
                test="quantityMeet != null and quantityMeet != ''">#{quantityMeet},</if>
            <if
                test="feedbackResults != null and feedbackResults != ''">#{feedbackResults},</if>
            <if
                test="ventureType != null and ventureType != ''">#{ventureType},</if>
            <if
                test="ventureSpecific != null and ventureSpecific != ''">#{ventureSpecific},</if>
            <if
                test="measures != null and measures != ''">#{measures},</if>
            <if
                test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if
                test="modifyTime != null">#{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierConPo" parameterType="SupplierConPo"> update supplier_con_po <trim
            prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if
                test="purchaseOrder != null and purchaseOrder != ''">purchase_order =
        #{purchaseOrder},</if>
            <if test="serialNumber != null and serialNumber != ''">serial_number =
        #{serialNumber},</if>
            <if test="quantityMeet != null and quantityMeet != ''">quantity_meet =
        #{quantityMeet},</if>
            <if test="feedbackResults != null and feedbackResults != ''">feedback_results
        = #{feedbackResults},</if>
            <if test="ventureType != null and ventureType != ''">venture_type
        = #{ventureType},</if>
            <if test="ventureSpecific != null and ventureSpecific != ''">venture_specific
        = #{ventureSpecific},</if>
            <if test="measures != null and measures != ''">measures =
        #{measures},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if
                test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierConPoById" parameterType="Long"> delete from supplier_con_po where id
        = #{id} </delete>

    <delete id="deleteSupplierConPoByIds" parameterType="String"> delete from supplier_con_po where
        id in <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_con_po <set>
                <if test="item.supplierCode != null and item.supplierCode != ''">supplier_code =
        #{item.supplierCode},</if>
                <if test="item.purchaseOrder != null and item.purchaseOrder != ''">purchase_order
        = #{item.purchaseOrder},</if>
                <if
                    test="item.serialNumber != null and item.serialNumber != ''">serial_number =
        #{item.serialNumber},</if>
                <if test="item.quantityMeet != null and item.quantityMeet != ''">quantity_meet
        = #{item.quantityMeet},</if>
                <if
                    test="item.feedbackResults != null and item.feedbackResults != ''">feedback_results
        = #{item.feedbackResults},</if>
                <if
                    test="item.ventureType != null and item.ventureType != ''">venture_type =
        #{item.ventureType},</if>
                <if
                    test="item.ventureSpecific != null and item.ventureSpecific != ''">venture_specific
        = #{item.ventureSpecific},</if>
                <if test="item.measures != null and item.measures != ''">measures
        = #{item.measures},</if>
                <if test="item.hasPush != null">has_push = #{item.hasPush},</if>
                <if
                    test="item.insertTime != null">insert_time = #{item.insertTime},</if>
                <if
                    test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if
                    test="item.insertBy != null and item.insertBy != ''">insert_by =
        #{item.insertBy},</if>
            <if
                    test="item.outBatchNo != null and item.outBatchNo != ''">out_batch_no =
                #{item.outBatchNo}</if>

            </set> where id = #{item.id} </foreach>
    </update>

    <select id="selectSupplierConPoByIds" resultMap="SupplierConPoResult">
        <include refid="selectSupplierConPoVo" />  where out_batch_no = #{batchNo}
    </select>

</mapper>