<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierProSchedulingMapper">
    <resultMap type="SupplierProScheduling" id="SupplierProSchedulingResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="plantId" column="plant_id" />
        <result property="plantName" column="plant_name" />
        <result property="vendorProductNo" column="vendor_product_no" />
        <result property="vendorProductName" column="vendor_product_name" />
        <result property="cheryProductNo" column="chery_product_no" />
        <result property="cheryProductName" column="chery_product_name" />
        <result property="planNo" column="plan_no" />
        <result property="manufactureNo" column="manufacture_no" />
        <result property="productBatchNo" column="product_batch_no" />
        <result property="manufactureNum" column="manufacture_num" />
        <result property="manufactureInputNum" column="manufacture_input_num" />
        <result property="manufactureOutputNum" column="manufacture_output_num" />
        <result property="planStatus" column="plan_status" />
        <result property="planBeginTime" column="plan_begin_time" />
        <result property="planEndTime" column="plan_end_time" />
        <result property="actualBeginTime" column="actual_begin_time" />
        <result property="actualEndTime" column="actual_end_time" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierProSchedulingVo"> select id, supplier_code, supplier_name, plant_id,
        plant_name, vendor_product_no, vendor_product_name, chery_product_no, chery_product_name,
        plan_no, manufacture_no, product_batch_no, manufacture_num, manufacture_input_num,
        manufacture_output_num, plan_status, plan_begin_time, plan_end_time, actual_begin_time,
        actual_end_time, has_push, insert_time, modify_time, insert_by, modify_by from
        supplier_pro_scheduling </sql>

    <select id="selectSupplierProSchedulingList" parameterType="SupplierProScheduling"
        resultMap="SupplierProSchedulingResult">
        <include refid="selectSupplierProSchedulingVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode} </if>
            <if test="supplierName != null  and supplierName != ''"> and
        supplier_name like concat('%', #{supplierName}, '%') </if>
            <if
                test="plantId != null  and plantId != ''"> and plant_id = #{plantId} </if>
            <if
                test="plantName != null  and plantName != ''"> and plant_name like concat('%',
        #{plantName}, '%') </if>
            <if test="vendorProductNo != null  and vendorProductNo != ''"> and
        vendor_product_no = #{vendorProductNo} </if>
            <if
                test="vendorProductName != null  and vendorProductName != ''"> and
        vendor_product_name like concat('%', #{vendorProductName}, '%') </if>
            <if
                test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no =
        #{cheryProductNo} </if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and
        chery_product_name like concat('%', #{cheryProductName}, '%') </if>
            <if
                test="planNo != null  and planNo != ''"> and plan_no = #{planNo} </if>
            <if
                test="manufactureNo != null  and manufactureNo != ''"> and manufacture_no =
        #{manufactureNo} </if>
            <if test="productBatchNo != null  and productBatchNo != ''"> and
        product_batch_no = #{productBatchNo} </if>
            <if test="manufactureNum != null"> and
        manufacture_num = #{manufactureNum} </if>
            <if test="manufactureInputNum != null"> and
        manufacture_input_num = #{manufactureInputNum} </if>
            <if test="manufactureOutputNum != null">
        and manufacture_output_num = #{manufactureOutputNum} </if>
            <if
                test="planStatus != null  and planStatus != ''"> and plan_status = #{planStatus} </if>
            <if
                test="planBeginTime != null  and planBeginTime != ''"> and plan_begin_time =
        #{planBeginTime} </if>
            <if test="planEndTime != null  and planEndTime != ''"> and
        plan_end_time = #{planEndTime} </if>
            <if
                test="actualBeginTime != null  and actualBeginTime != ''"> and actual_begin_time =
        #{actualBeginTime} </if>
            <if test="actualEndTime != null  and actualEndTime != ''"> and
        actual_end_time = #{actualEndTime} </if>
            <if test="hasPush != null"> and has_push =
        #{hasPush} </if>
            <if test="insertTime != null"> and insert_time = #{insertTime} </if>
            <if
                test="modifyTime != null"> and modify_time = #{modifyTime} </if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy} </if>
            <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy} </if>
        </where>
    </select>

    <select id="selectSupplierProSchedulingById" parameterType="Long"
        resultMap="SupplierProSchedulingResult">
        <include refid="selectSupplierProSchedulingVo" /> where id = #{id} </select>

    <insert id="insertSupplierProScheduling" parameterType="SupplierProScheduling"
        useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_pro_scheduling <trim prefix="(" suffix=")"
            suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''"> supplier_code, </if>
            <if
                test="supplierName != null and supplierName != ''"> supplier_name, </if>
            <if
                test="plantId != null and plantId != ''"> plant_id, </if>
            <if
                test="plantName != null and plantName != ''"> plant_name, </if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''"> vendor_product_no, </if>
            <if
                test="vendorProductName != null and vendorProductName != ''"> vendor_product_name, </if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''"> chery_product_no, </if>
            <if
                test="cheryProductName != null and cheryProductName != ''"> chery_product_name, </if>
            <if
                test="planNo != null and planNo != ''"> plan_no, </if>
            <if
                test="manufactureNo != null and manufactureNo != ''"> manufacture_no, </if>
            <if
                test="productBatchNo != null and productBatchNo != ''"> product_batch_no, </if>
            <if
                test="manufactureNum != null"> manufacture_num, </if>
            <if
                test="manufactureInputNum != null"> manufacture_input_num, </if>
            <if
                test="manufactureOutputNum != null"> manufacture_output_num, </if>
            <if
                test="planStatus != null and planStatus != ''"> plan_status, </if>
            <if
                test="planBeginTime != null and planBeginTime != ''"> plan_begin_time, </if>
            <if
                test="planEndTime != null and planEndTime != ''"> plan_end_time, </if>
            <if
                test="actualBeginTime != null and actualBeginTime != ''"> actual_begin_time, </if>
            <if
                test="actualEndTime != null and actualEndTime != ''"> actual_end_time, </if>
            <if
                test="hasPush != null"> has_push, </if>
            <if test="insertTime != null"> insert_time, </if>
            <if
                test="modifyTime != null"> modify_time, </if>
            <if
                test="insertBy != null and insertBy != ''"> insert_by, </if>
            <if
                test="modifyBy != null and modifyBy != ''"> modify_by, </if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''"> #{supplierCode}, </if>
            <if
                test="supplierName != null and supplierName != ''"> #{supplierName}, </if>
            <if
                test="plantId != null and plantId != ''"> #{plantId}, </if>
            <if
                test="plantName != null and plantName != ''"> #{plantName}, </if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''"> #{vendorProductNo}, </if>
            <if
                test="vendorProductName != null and vendorProductName != ''"> #{vendorProductName}, </if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''"> #{cheryProductNo}, </if>
            <if
                test="cheryProductName != null and cheryProductName != ''"> #{cheryProductName}, </if>
            <if
                test="planNo != null and planNo != ''"> #{planNo}, </if>
            <if
                test="manufactureNo != null and manufactureNo != ''"> #{manufactureNo}, </if>
            <if
                test="productBatchNo != null and productBatchNo != ''"> #{productBatchNo}, </if>
            <if
                test="manufactureNum != null"> #{manufactureNum}, </if>
            <if
                test="manufactureInputNum != null"> #{manufactureInputNum}, </if>
            <if
                test="manufactureOutputNum != null"> #{manufactureOutputNum}, </if>
            <if
                test="planStatus != null and planStatus != ''"> #{planStatus}, </if>
            <if
                test="planBeginTime != null and planBeginTime != ''"> #{planBeginTime}, </if>
            <if
                test="planEndTime != null and planEndTime != ''"> #{planEndTime}, </if>
            <if
                test="actualBeginTime != null and actualBeginTime != ''"> #{actualBeginTime}, </if>
            <if
                test="actualEndTime != null and actualEndTime != ''"> #{actualEndTime}, </if>
            <if
                test="hasPush != null"> #{hasPush}, </if>
            <if test="insertTime != null">
        #{insertTime}, </if>
            <if test="modifyTime != null"> #{modifyTime}, </if>
            <if
                test="insertBy != null and insertBy != ''"> #{insertBy}, </if>
            <if
                test="modifyBy != null and modifyBy != ''"> #{modifyBy}, </if>
        </trim>
    </insert>

    <update id="updateSupplierProScheduling" parameterType="SupplierProScheduling"> update
        supplier_pro_scheduling <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''"> supplier_code = #{supplierCode}, </if>
            <if
                test="supplierName != null and supplierName != ''"> supplier_name = #{supplierName}, </if>
            <if
                test="plantId != null and plantId != ''"> plant_id = #{plantId}, </if>
            <if
                test="plantName != null and plantName != ''"> plant_name = #{plantName}, </if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''"> vendor_product_no =
        #{vendorProductNo}, </if>
            <if test="vendorProductName != null and vendorProductName != ''">
        vendor_product_name = #{vendorProductName}, </if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''"> chery_product_no =
        #{cheryProductNo}, </if>
            <if test="cheryProductName != null and cheryProductName != ''">
        chery_product_name = #{cheryProductName}, </if>
            <if test="planNo != null and planNo != ''">
        plan_no = #{planNo}, </if>
            <if test="manufactureNo != null and manufactureNo != ''">
        manufacture_no = #{manufactureNo}, </if>
            <if
                test="productBatchNo != null and productBatchNo != ''"> product_batch_no =
        #{productBatchNo}, </if>
            <if test="manufactureNum != null"> manufacture_num =
        #{manufactureNum}, </if>
            <if test="manufactureInputNum != null"> manufacture_input_num =
        #{manufactureInputNum}, </if>
            <if test="manufactureOutputNum != null"> manufacture_output_num
        = #{manufactureOutputNum}, </if>
            <if test="planStatus != null and planStatus != ''">
        plan_status = #{planStatus}, </if>
            <if test="planBeginTime != null and planBeginTime != ''">
        plan_begin_time = #{planBeginTime}, </if>
            <if
                test="planEndTime != null and planEndTime != ''"> plan_end_time = #{planEndTime}, </if>
            <if
                test="actualBeginTime != null and actualBeginTime != ''"> actual_begin_time =
        #{actualBeginTime}, </if>
            <if test="actualEndTime != null and actualEndTime != ''">
        actual_end_time = #{actualEndTime}, </if>
            <if test="hasPush != null"> has_push = #{hasPush}, </if>
            <if
                test="insertTime != null"> insert_time = #{insertTime}, </if>
            <if
                test="modifyTime != null"> modify_time = #{modifyTime}, </if>
            <if
                test="insertBy != null and insertBy != ''"> insert_by = #{insertBy}, </if>
            <if
                test="modifyBy != null and modifyBy != ''"> modify_by = #{modifyBy}, </if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierProSchedulingById" parameterType="Long"> delete from
        supplier_pro_scheduling where id = #{id} </delete>

    <delete id="deleteSupplierProSchedulingByIds" parameterType="String"> delete from
        supplier_pro_scheduling where id in <foreach item="id" collection="array" open="("
            separator="," close=")"> #{id} </foreach>
    </delete>

    <select id="selectSupplierProSchedulingByIds" resultMap="SupplierProSchedulingResult">
        <include refid="selectSupplierProSchedulingVo" /> where out_batch_no = #{batchNo}
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_pro_scheduling <set>
                <if test="item.supplierCode != null and item.supplierCode != ''"> supplier_code =
        #{item.supplierCode}, </if>
                <if test="item.supplierName != null and item.supplierName != ''">
        supplier_name = #{item.supplierName}, </if>
                <if
                    test="item.plantId != null and item.plantId != ''"> plant_id = #{item.plantId}, </if>
                <if
                    test="item.plantName != null and item.plantName != ''"> plant_name =
        #{item.plantName}, </if>
                <if
                    test="item.vendorProductNo != null and item.vendorProductNo != ''">
        vendor_product_no = #{item.vendorProductNo}, </if>
                <if
                    test="item.vendorProductName != null and item.vendorProductName != ''">
        vendor_product_name = #{item.vendorProductName}, </if>
                <if
                    test="item.cheryProductNo != null and item.cheryProductNo != ''">
        chery_product_no = #{item.cheryProductNo}, </if>
                <if
                    test="item.cheryProductName != null and item.cheryProductName != ''">
        chery_product_name = #{item.cheryProductName}, </if>
                <if
                    test="item.planNo != null and item.planNo != ''"> plan_no = #{item.planNo}, </if>
                <if
                    test="item.manufactureNo != null and item.manufactureNo != ''"> manufacture_no =
        #{item.manufactureNo}, </if>
                <if
                    test="item.productBatchNo != null and item.productBatchNo != ''">
        product_batch_no = #{item.productBatchNo}, </if>
                <if test="item.manufactureNum != null">
        manufacture_num = #{item.manufactureNum}, </if>
                <if test="item.manufactureInputNum != null">
        manufacture_input_num = #{item.manufactureInputNum}, </if>
                <if
                    test="item.manufactureOutputNum != null"> manufacture_output_num =
        #{item.manufactureOutputNum}, </if>
                <if
                    test="item.planStatus != null and item.planStatus != ''"> plan_status =
        #{item.planStatus}, </if>
                <if test="item.planBeginTime != null and item.planBeginTime != ''">
        plan_begin_time = #{item.planBeginTime}, </if>
                <if
                    test="item.planEndTime != null and item.planEndTime != ''"> plan_end_time =
        #{item.planEndTime}, </if>
                <if
                    test="item.actualBeginTime != null and item.actualBeginTime != ''">
        actual_begin_time = #{item.actualBeginTime}, </if>
                <if
                    test="item.actualEndTime != null and item.actualEndTime != ''"> actual_end_time
        = #{item.actualEndTime}, </if>
                <if test="item.hasPush != null"> has_push = #{item.hasPush}, </if>
                <if
                    test="item.insertTime != null"> insert_time = #{item.insertTime}, </if>
                <if
                    test="item.modifyTime != null"> modify_time = #{item.modifyTime}, </if>
                <if
                    test="item.insertBy != null and item.insertBy != ''"> insert_by =
        #{item.insertBy}, </if>
                <if test="item.modifyBy != null and item.modifyBy != ''"> modify_by =
        #{item.modifyBy}, </if>
            </set> where id = #{item.id} </foreach>
    </update>
</mapper>