<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierBomMapper">

    <resultMap type="SupplierBom" id="SupplierBomResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="bomCode" column="bom_code" />
        <result property="bomName" column="bom_name" />
        <result property="bomVersion" column="bom_version" />
        <result property="cheryProductNo" column="chery_product_no" />
        <result property="cheryProductName" column="chery_product_name" />
        <result property="vendorProductNo" column="vendor_product_no" />
        <result property="vendorProductName" column="vendor_product_name" />
        <result property="vendorProductType" column="vendor_product_type" />
        <result property="materialUnit" column="material_unit" />
        <result property="subMaterialCode" column="sub_material_code" />
        <result property="subMaterialName" column="sub_material_name" />
        <result property="subMaterialType" column="sub_material_type" />
        <result property="subMaterialUnit" column="sub_material_unit" />
        <result property="subMaterialQuota" column="sub_material_quota" />
        <result property="dataUpdateTime" column="data_update_time" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierBomVo"> select id, supplier_code, supplier_name, bom_code, bom_name,
        bom_version, chery_product_no, chery_product_name, vendor_product_no, vendor_product_name,
        vendor_product_type, material_unit, sub_material_code, sub_material_name, sub_material_type,
        sub_material_unit, sub_material_quota, data_update_time, has_push, insert_time, modify_time,
        insert_by, modify_by from supplier_bom </sql>

    <select id="selectSupplierBomList" parameterType="SupplierBom" resultMap="SupplierBomResult">
        <include refid="selectSupplierBomVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and
        supplier_name like concat('%', #{supplierName}, '%')</if>
            <if
                test="bomCode != null  and bomCode != ''"> and bom_code = #{bomCode}</if>
            <if
                test="bomName != null  and bomName != ''"> and bom_name like concat('%', #{bomName},
        '%')</if>
            <if test="bomVersion != null  and bomVersion != ''"> and bom_version =
        #{bomVersion}</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and
        chery_product_no = #{cheryProductNo}</if>
            <if
                test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name
        like concat('%', #{cheryProductName}, '%')</if>
            <if
                test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no =
        #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''">
        and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if
                test="vendorProductType != null  and vendorProductType != ''"> and
        vendor_product_type = #{vendorProductType}</if>
            <if
                test="materialUnit != null  and materialUnit != ''"> and material_unit =
        #{materialUnit}</if>
            <if test="subMaterialCode != null  and subMaterialCode != ''"> and
        sub_material_code = #{subMaterialCode}</if>
            <if
                test="subMaterialName != null  and subMaterialName != ''"> and sub_material_name
        like concat('%', #{subMaterialName}, '%')</if>
            <if
                test="subMaterialType != null  and subMaterialType != ''"> and sub_material_type =
        #{subMaterialType}</if>
            <if test="subMaterialUnit != null  and subMaterialUnit != ''"> and
        sub_material_unit = #{subMaterialUnit}</if>
            <if test="subMaterialQuota != null "> and
        sub_material_quota = #{subMaterialQuota}</if>
            <if
                test="dataUpdateTime != null  and dataUpdateTime != ''"> and data_update_time =
        #{dataUpdateTime}</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if
                test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
              <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
<if
                    test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>

        </where>
    </select>

    <select id="selectSupplierBomById" parameterType="Long" resultMap="SupplierBomResult">
        <include refid="selectSupplierBomVo" /> where id = #{id} </select>

    <insert id="insertSupplierBom" parameterType="SupplierBom" useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_bom <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if
                test="bomCode != null and bomCode != ''">bom_code,</if>
            <if
                test="bomName != null and bomName != ''">bom_name,</if>
            <if
                test="bomVersion != null and bomVersion != ''">bom_version,</if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if
                test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if
                test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if
                test="vendorProductType != null and vendorProductType != ''">vendor_product_type,</if>
            <if
                test="materialUnit != null and materialUnit != ''">material_unit,</if>
            <if
                test="subMaterialCode != null and subMaterialCode != ''">sub_material_code,</if>
            <if
                test="subMaterialName != null and subMaterialName != ''">sub_material_name,</if>
            <if
                test="subMaterialType != null and subMaterialType != ''">sub_material_type,</if>
            <if
                test="subMaterialUnit != null and subMaterialUnit != ''">sub_material_unit,</if>
            <if
                test="subMaterialQuota != null">sub_material_quota,</if>
            <if
                test="dataUpdateTime != null and dataUpdateTime != ''">data_update_time,</if>
            <if
                test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if
                test="modifyTime != null">modify_time,</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if
                test="bomCode != null and bomCode != ''">#{bomCode},</if>
            <if
                test="bomName != null and bomName != ''">#{bomName},</if>
            <if
                test="bomVersion != null and bomVersion != ''">#{bomVersion},</if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''">#{cheryProductNo},</if>
            <if
                test="cheryProductName != null and cheryProductName != ''">#{cheryProductName},</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if
                test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if
                test="vendorProductType != null and vendorProductType != ''">#{vendorProductType},</if>
            <if
                test="materialUnit != null and materialUnit != ''">#{materialUnit},</if>
            <if
                test="subMaterialCode != null and subMaterialCode != ''">#{subMaterialCode},</if>
            <if
                test="subMaterialName != null and subMaterialName != ''">#{subMaterialName},</if>
            <if
                test="subMaterialType != null and subMaterialType != ''">#{subMaterialType},</if>
            <if
                test="subMaterialUnit != null and subMaterialUnit != ''">#{subMaterialUnit},</if>
            <if
                test="subMaterialQuota != null">#{subMaterialQuota},</if>
            <if
                test="dataUpdateTime != null and dataUpdateTime != ''">#{dataUpdateTime},</if>
            <if
                test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if
                test="modifyTime != null">#{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierBom" parameterType="SupplierBom"> update supplier_bom <trim
            prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if
                test="bomCode != null and bomCode != ''">bom_code = #{bomCode},</if>
            <if
                test="bomName != null and bomName != ''">bom_name = #{bomName},</if>
            <if
                test="bomVersion != null and bomVersion != ''">bom_version = #{bomVersion},</if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''">chery_product_no =
        #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name
        = #{cheryProductName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no
        = #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name
        = #{vendorProductName},</if>
            <if test="vendorProductType != null and vendorProductType != ''">vendor_product_type
        = #{vendorProductType},</if>
            <if test="materialUnit != null and materialUnit != ''">material_unit
        = #{materialUnit},</if>
            <if test="subMaterialCode != null and subMaterialCode != ''">sub_material_code
        = #{subMaterialCode},</if>
            <if test="subMaterialName != null and subMaterialName != ''">sub_material_name
        = #{subMaterialName},</if>
            <if test="subMaterialType != null and subMaterialType != ''">sub_material_type
        = #{subMaterialType},</if>
            <if test="subMaterialUnit != null and subMaterialUnit != ''">sub_material_unit
        = #{subMaterialUnit},</if>
            <if test="subMaterialQuota != null">sub_material_quota =
        #{subMaterialQuota},</if>
            <if test="dataUpdateTime != null and dataUpdateTime != ''">data_update_time
        = #{dataUpdateTime},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if
                test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierBomById" parameterType="Long"> delete from supplier_bom where id =
        #{id} </delete>

    <delete id="deleteSupplierBomByIds" parameterType="String"> delete from supplier_bom where id in <foreach
            item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_bom <set>
                <if test="item.supplierCode != null and item.supplierCode != ''">supplier_code =
        #{item.supplierCode},</if>
                <if test="item.supplierName != null and item.supplierName != ''">supplier_name
        = #{item.supplierName},</if>
                <if test="item.bomCode != null and item.bomCode != ''">bom_code
        = #{item.bomCode},</if>
                <if test="item.bomName != null and item.bomName != ''">bom_name =
        #{item.bomName},</if>
                <if test="item.bomVersion != null and item.bomVersion != ''">bom_version
        = #{item.bomVersion},</if>
                <if
                    test="item.cheryProductNo != null and item.cheryProductNo != ''">chery_product_no
        = #{item.cheryProductNo},</if>
                <if
                    test="item.cheryProductName != null and item.cheryProductName != ''">chery_product_name
        = #{item.cheryProductName},</if>
                <if
                    test="item.vendorProductNo != null and item.vendorProductNo != ''">vendor_product_no
        = #{item.vendorProductNo},</if>
                <if
                    test="item.vendorProductName != null and item.vendorProductName != ''">vendor_product_name
        = #{item.vendorProductName},</if>
                <if
                    test="item.vendorProductType != null and item.vendorProductType != ''">vendor_product_type
        = #{item.vendorProductType},</if>
                <if
                    test="item.materialUnit != null and item.materialUnit != ''">material_unit =
        #{item.materialUnit},</if>
                <if
                    test="item.subMaterialCode != null and item.subMaterialCode != ''">sub_material_code
        = #{item.subMaterialCode},</if>
                <if
                    test="item.subMaterialName != null and item.subMaterialName != ''">sub_material_name
        = #{item.subMaterialName},</if>
                <if
                    test="item.subMaterialType != null and item.subMaterialType != ''">sub_material_type
        = #{item.subMaterialType},</if>
                <if
                    test="item.subMaterialUnit != null and item.subMaterialUnit != ''">sub_material_unit
        = #{item.subMaterialUnit},</if>
                <if test="item.subMaterialQuota != null">sub_material_quota =
        #{item.subMaterialQuota},</if>
                <if
                    test="item.dataUpdateTime != null and item.dataUpdateTime != ''">data_update_time
        = #{item.dataUpdateTime},</if>
                <if test="item.hasPush != null">has_push = #{item.hasPush},</if>
                <if
                    test="item.insertTime != null">insert_time = #{item.insertTime},</if>
                <if
                    test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if
                    test="item.insertBy != null and item.insertBy != ''">insert_by =
        #{item.insertBy},</if>
            <if
                    test="item.outBatchNo != null and item.outBatchNo != ''">out_batch_no =
                #{item.outBatchNo}</if>

            </set> where id = #{item.id} </foreach>
    </update>

    <select id="selectSupplierBomByIds" resultMap="SupplierBomResult">
        <include refid="selectSupplierBomVo" />  where out_batch_no = #{batchNo}
    </select>

</mapper>