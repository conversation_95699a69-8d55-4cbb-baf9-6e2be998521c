<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierProProcessChildMapper">

    <resultMap type="SupplierProProcessChild" id="SupplierProProcessChildResult">
        <result property="id" column="id" />
        <result property="parentId" column="parent_id" />
        <result property="processCode" column="process_code" />
        <result property="processName" column="process_name" />
        <result property="processOrder" column="process_order" />
        <result property="rhythm" column="rhythm" />
        <result property="processSupplierCode" column="process_supplier_code" />
        <result property="processSupplierName" column="process_supplier_name" />
        <result property="processLocation" column="process_location" />
        <result property="processCycleTime" column="process_cycle_time" />
        <result property="processYieldTarget" column="process_yield_target" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierProProcessChildVo"> select id, parent_id, process_code, process_name,
        process_order, rhythm, process_supplier_code, process_supplier_name, process_location,
        process_cycle_time, process_yield_target, insert_time, modify_time, insert_by, modify_by
        from supplier_pro_process_child </sql>

    <select id="selectSupplierProProcessChildList" parameterType="SupplierProProcessChild"
        resultMap="SupplierProProcessChildResult">
        <include refid="selectSupplierProProcessChildVo" />
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if
                test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if
                test="processName != null  and processName != ''"> and process_name like concat('%',
        #{processName}, '%')</if>
            <if test="processOrder != null "> and process_order =
        #{processOrder}</if>
            <if test="rhythm != null "> and rhythm = #{rhythm}</if>
            <if
                test="processSupplierCode != null  and processSupplierCode != ''"> and
        process_supplier_code = #{processSupplierCode}</if>
            <if
                test="processSupplierName != null  and processSupplierName != ''"> and
        process_supplier_name like concat('%', #{processSupplierName}, '%')</if>
            <if
                test="processLocation != null  and processLocation != ''"> and process_location =
        #{processLocation}</if>
            <if test="processCycleTime != null "> and process_cycle_time =
        #{processCycleTime}</if>
            <if test="processYieldTarget != null "> and process_yield_target =
        #{processYieldTarget}</if>
        </where>
    </select>

    <select id="selectSupplierProProcessChildById" parameterType="Long"
        resultMap="SupplierProProcessChildResult">
        <include refid="selectSupplierProProcessChildVo" /> where id = #{id} </select>

    <resultMap type="SupplierProProcessChild" id="SupplierProProcessChildResultWithGrandChild"
        extends="SupplierProProcessChildResult">
        <collection property="subList" javaType="java.util.List"
            ofType="SupplierProProcessGrandChild"
            select="com.hj.admin.mapper.push.SupplierProProcessGrandChildMapper.selectSupplierProProcessGrandChildList"
            column="{parentId=id}" />
    </resultMap>

    <select id="selectSupplierProProcessChildListWithGrandChild"
        parameterType="SupplierProProcessChild"
        resultMap="SupplierProProcessChildResultWithGrandChild">
        <include refid="selectSupplierProProcessChildVo" />
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
    </select>

    <insert id="insertSupplierProProcessChild" parameterType="SupplierProProcessChild"
        useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_pro_process_child <trim
            prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if
                test="processCode != null and processCode != ''">process_code,</if>
            <if
                test="processName != null and processName != ''">process_name,</if>
            <if
                test="processOrder != null">process_order,</if>
            <if test="rhythm != null">rhythm,</if>
            <if
                test="processSupplierCode != null and processSupplierCode != ''">
        process_supplier_code,</if>
            <if
                test="processSupplierName != null and processSupplierName != ''">
        process_supplier_name,</if>
            <if test="processLocation != null and processLocation != ''">
        process_location,</if>
            <if test="processCycleTime != null">process_cycle_time,</if>
            <if
                test="processYieldTarget != null">process_yield_target,</if>
            <if
                test="insertTime != null">insert_time,</if>
            <if test="modifyTime != null">
        modify_time,</if>
            <if test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if
                test="processCode != null and processCode != ''">#{processCode},</if>
            <if
                test="processName != null and processName != ''">#{processName},</if>
            <if
                test="processOrder != null">#{processOrder},</if>
            <if test="rhythm != null">
        #{rhythm},</if>
            <if test="processSupplierCode != null and processSupplierCode != ''">
        #{processSupplierCode},</if>
            <if
                test="processSupplierName != null and processSupplierName != ''">
        #{processSupplierName},</if>
            <if test="processLocation != null and processLocation != ''">
        #{processLocation},</if>
            <if test="processCycleTime != null">#{processCycleTime},</if>
            <if
                test="processYieldTarget != null">#{processYieldTarget},</if>
            <if
                test="insertTime != null">#{insertTime},</if>
            <if test="modifyTime != null">
        #{modifyTime},</if>
            <if test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierProProcessChild" parameterType="SupplierProProcessChild"> update
        supplier_pro_process_child <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if
                test="processCode != null and processCode != ''">process_code = #{processCode},</if>
            <if
                test="processName != null and processName != ''">process_name = #{processName},</if>
            <if
                test="processOrder != null">process_order = #{processOrder},</if>
            <if
                test="rhythm != null">rhythm = #{rhythm},</if>
            <if
                test="processSupplierCode != null and processSupplierCode != ''">process_supplier_code
        = #{processSupplierCode},</if>
            <if
                test="processSupplierName != null and processSupplierName != ''">process_supplier_name
        = #{processSupplierName},</if>
            <if test="processLocation != null and processLocation != ''">process_location
        = #{processLocation},</if>
            <if test="processCycleTime != null">process_cycle_time =
        #{processCycleTime},</if>
            <if test="processYieldTarget != null">process_yield_target =
        #{processYieldTarget},</if>
            <if test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierProProcessChildById" parameterType="Long"> delete from
        supplier_pro_process_child where id = #{id} </delete>

    <delete id="deleteSupplierProProcessChildByIds" parameterType="String"> delete from
        supplier_pro_process_child where id in <foreach item="id" collection="array" open="("
            separator="," close=")"> #{id} </foreach>
    </delete>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_pro_process_child <set>
                <if test="item.parentId != null"> parent_id = #{item.parentId}, </if>
                <if
                    test="item.processCode != null and item.processCode != ''"> process_code =
        #{item.processCode}, </if>
                <if test="item.processName != null and item.processName != ''">
        process_name = #{item.processName}, </if>
                <if test="item.processOrder != null"> process_order
        = #{item.processOrder}, </if>
                <if test="item.rhythm != null"> rhythm = #{item.rhythm}, </if>
                <if
                    test="item.processSupplierCode != null and item.processSupplierCode != ''">
        process_supplier_code = #{item.processSupplierCode}, </if>
                <if
                    test="item.processSupplierName != null and item.processSupplierName != ''">
        process_supplier_name = #{item.processSupplierName}, </if>
                <if
                    test="item.processLocation != null and item.processLocation != ''">
        process_location = #{item.processLocation}, </if>
                <if test="item.processCycleTime != null">
        process_cycle_time = #{item.processCycleTime}, </if>
                <if
                    test="item.processYieldTarget != null"> process_yield_target =
        #{item.processYieldTarget}, </if>
                <if test="item.insertTime != null"> insert_time =
        #{item.insertTime}, </if>
                <if test="item.modifyTime != null"> modify_time =
        #{item.modifyTime}, </if>
                <if test="item.insertBy != null and item.insertBy != ''"> insert_by
        = #{item.insertBy}, </if>
                <if test="item.modifyBy != null and item.modifyBy != ''"> modify_by
        = #{item.modifyBy}, </if>
            </set> where id = #{item.id} </foreach>
    </update>

    <select id="selectSupplierProProcessChildByIds" resultMap="SupplierProProcessChildResult">
        <include refid="selectSupplierProProcessChildVo" /> where out_batch_no = #{batchNo}
    </select>

</mapper>