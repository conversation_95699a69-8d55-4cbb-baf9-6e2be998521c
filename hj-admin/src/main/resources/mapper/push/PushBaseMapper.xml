<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.PushBaseMapper">

    <select id="changeFieldConfirmStatus" parameterType="String">
        UPDATE ${tableName} SET field_has_confirm = true WHERE out_batch_no = #{batchNo};
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        update ${tableName} set out_batch_no = #{outBatchNo} where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>;
    </update>

    <update id="changeInterfaceConfirmStatus">
        UPDATE ${tableName} SET field_has_confirm = true where out_batch_no = #{batchNo}
        and has_push = false and interface_has_confirm = false;
    </update>

    <select id="listUnPushData" resultType="java.util.Map">
        select * from ${tableName} where has_push = false
        and out_batch_no is not null and out_batch_no !='';
    </select>
</mapper>