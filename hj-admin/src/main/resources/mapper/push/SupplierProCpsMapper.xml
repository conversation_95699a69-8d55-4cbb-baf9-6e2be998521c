<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierProCpsMapper">

    <resultMap type="SupplierProCps" id="SupplierProCpsResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="vendorProductNo" column="vendor_product_no" />
        <result property="vendorProductName" column="vendor_product_name" />
        <result property="vendorProductSn" column="vendor_product_sn" />
        <result property="vendorProductBatch" column="vendor_product_batch" />
        <result property="cheryProductNo" column="chery_product_no" />
        <result property="cheryProductName" column="chery_product_name" />
        <result property="cheryProductSn" column="chery_product_sn" />
        <result property="productBatchNo" column="product_batch_no" />
        <result property="manufactureNo" column="manufacture_no" />
        <result property="plantId" column="plant_id" />
        <result property="plantName" column="plant_name" />
        <result property="workshopId" column="workshop_id" />
        <result property="workshopName" column="workshop_name" />
        <result property="productionLineId" column="production_line_id" />
        <result property="productionLineName" column="production_line_name" />
        <result property="stationId" column="station_id" />
        <result property="stationName" column="station_name" />
        <result property="empCode" column="emp_code" />
        <result property="empName" column="emp_name" />
        <result property="vendorFieldName" column="vendor_field_name" />
        <result property="vendorFieldCode" column="vendor_field_code" />
        <result property="gatherSpot" column="gather_spot" />
        <result property="samplingRate" column="sampling_rate" />
        <result property="limitUpdateTime" column="limit_update_time" />
        <result property="vendorFieldDesc" column="vendor_field_desc" />
        <result property="carrierCode" column="carrier_code" />
        <result property="intputQty" column="intput_qty" />
        <result property="fttQty" column="ftt_qty" />
        <result property="parameter" column="parameter" />
        <result property="characteristic" column="characteristic" />
        <result property="cc" column="cc" />
        <result property="sc" column="sc" />
        <result property="spc" column="spc" />
        <result property="standardValue" column="standard_value" />
        <result property="upperLimit" column="upper_limit" />
        <result property="lowerLimit" column="lower_limit" />
        <result property="decimalValue" column="decimal_value" />
        <result property="unitCn" column="unit_cn" />
        <result property="unitEn" column="unit_en" />
        <result property="checkResult" column="check_result" />
        <result property="detectionMode" column="detection_mode" />
        <result property="workShift" column="work_shift" />
        <result property="collectTime" column="collect_time" />
        <result property="checkMode" column="check_mode" />
        <result property="deviceCode" column="device_code" />
        <result property="deviceName" column="device_name" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierProCpsVo"> select id, supplier_code, supplier_name, vendor_product_no,
        vendor_product_name, vendor_product_sn, vendor_product_batch, chery_product_no,
        chery_product_name, chery_product_sn, product_batch_no, manufacture_no, plant_id,
        plant_name, workshop_id, workshop_name, production_line_id, production_line_name,
        station_id, station_name, emp_code, emp_name, vendor_field_name, vendor_field_code,
        gather_spot, sampling_rate, limit_update_time, vendor_field_desc, carrier_code, intput_qty,
        ftt_qty, parameter, characteristic, cc, sc, spc, standard_value, upper_limit, lower_limit,
        decimal_value, unit_cn, unit_en, check_result, detection_mode, work_shift, collect_time,
        check_mode, device_code, device_name, has_push, insert_time, modify_time, insert_by,
        modify_by from supplier_pro_cps </sql>

    <select id="selectSupplierProCpsList" parameterType="SupplierProCps"
        resultMap="SupplierProCpsResult">
        <include refid="selectSupplierProCpsVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and
        supplier_name like concat('%', #{supplierName}, '%')</if>
            <if
                test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no =
        #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''">
        and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if
                test="vendorProductSn != null  and vendorProductSn != ''"> and vendor_product_sn =
        #{vendorProductSn}</if>
            <if test="vendorProductBatch != null  and vendorProductBatch != ''">
        and vendor_product_batch = #{vendorProductBatch}</if>
            <if
                test="cheryProductNo != null  and cheryProductNo != ''"> and chery_product_no =
        #{cheryProductNo}</if>
            <if test="cheryProductName != null  and cheryProductName != ''"> and
        chery_product_name like concat('%', #{cheryProductName}, '%')</if>
            <if
                test="cheryProductSn != null  and cheryProductSn != ''"> and chery_product_sn =
        #{cheryProductSn}</if>
            <if test="productBatchNo != null  and productBatchNo != ''"> and
        product_batch_no = #{productBatchNo}</if>
            <if
                test="manufactureNo != null  and manufactureNo != ''"> and manufacture_no =
        #{manufactureNo}</if>
            <if test="plantId != null  and plantId != ''"> and plant_id =
        #{plantId}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like
        concat('%', #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and
        workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and
        workshop_name like concat('%', #{workshopName}, '%')</if>
            <if
                test="productionLineId != null  and productionLineId != ''"> and production_line_id
        = #{productionLineId}</if>
            <if
                test="productionLineName != null  and productionLineName != ''"> and
        production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if
                test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if
                test="stationName != null  and stationName != ''"> and station_name like concat('%',
        #{stationName}, '%')</if>
            <if test="empCode != null  and empCode != ''"> and emp_code =
        #{empCode}</if>
            <if test="empName != null  and empName != ''"> and emp_name like concat('%',
        #{empName}, '%')</if>
            <if test="vendorFieldName != null  and vendorFieldName != ''"> and
        vendor_field_name like concat('%', #{vendorFieldName}, '%')</if>
            <if
                test="vendorFieldCode != null  and vendorFieldCode != ''"> and vendor_field_code =
        #{vendorFieldCode}</if>
            <if test="gatherSpot != null  and gatherSpot != ''"> and gather_spot
        = #{gatherSpot}</if>
            <if test="samplingRate != null "> and sampling_rate = #{samplingRate}</if>
            <if
                test="limitUpdateTime != null  and limitUpdateTime != ''"> and limit_update_time =
        #{limitUpdateTime}</if>
            <if test="vendorFieldDesc != null  and vendorFieldDesc != ''"> and
        vendor_field_desc = #{vendorFieldDesc}</if>
            <if
                test="carrierCode != null  and carrierCode != ''"> and carrier_code = #{carrierCode}</if>
            <if
                test="intputQty != null "> and intput_qty = #{intputQty}</if>
            <if
                test="fttQty != null "> and ftt_qty = #{fttQty}</if>
            <if
                test="parameter != null  and parameter != ''"> and parameter = #{parameter}</if>
            <if
                test="characteristic != null  and characteristic != ''"> and characteristic =
        #{characteristic}</if>
            <if test="cc != null  and cc != ''"> and cc = #{cc}</if>
            <if
                test="sc != null  and sc != ''"> and sc = #{sc}</if>
            <if
                test="spc != null  and spc != ''"> and spc = #{spc}</if>
            <if
                test="standardValue != null  and standardValue != ''"> and standard_value =
        #{standardValue}</if>
            <if test="upperLimit != null "> and upper_limit = #{upperLimit}</if>
            <if
                test="lowerLimit != null "> and lower_limit = #{lowerLimit}</if>
            <if
                test="decimalValue != null "> and decimal_value = #{decimalValue}</if>
            <if
                test="unitCn != null  and unitCn != ''"> and unit_cn = #{unitCn}</if>
            <if
                test="unitEn != null  and unitEn != ''"> and unit_en = #{unitEn}</if>
            <if
                test="checkResult != null  and checkResult != ''"> and check_result = #{checkResult}</if>
            <if
                test="detectionMode != null  and detectionMode != ''"> and detection_mode =
        #{detectionMode}</if>
            <if test="workShift != null  and workShift != ''"> and work_shift =
        #{workShift}</if>
            <if test="collectTime != null  and collectTime != ''"> and collect_time =
        #{collectTime}</if>
            <if test="checkMode != null  and checkMode != ''"> and check_mode =
        #{checkMode}</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code =
        #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like
        concat('%', #{deviceName}, '%')</if>
            <if test="hasPush != null "> and has_push = #{hasPush}</if>
            <if
                test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
              <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
<if
                    test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>
        </where>
    </select>

    <select id="selectSupplierProCpsById" parameterType="Long" resultMap="SupplierProCpsResult">
        <include refid="selectSupplierProCpsVo" /> where id = #{id} </select>

    <insert id="insertSupplierProCps" parameterType="SupplierProCps" useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_pro_cps <trim prefix="(" suffix=")"
            suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if
                test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if
                test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn,</if>
            <if
                test="vendorProductBatch != null and vendorProductBatch != ''">vendor_product_batch,</if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if
                test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if
                test="cheryProductSn != null and cheryProductSn != ''">chery_product_sn,</if>
            <if
                test="productBatchNo != null and productBatchNo != ''">product_batch_no,</if>
            <if
                test="manufactureNo != null and manufactureNo != ''">manufacture_no,</if>
            <if
                test="plantId != null and plantId != ''">plant_id,</if>
            <if
                test="plantName != null and plantName != ''">plant_name,</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if
                test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if
                test="stationId != null and stationId != ''">station_id,</if>
            <if
                test="stationName != null and stationName != ''">station_name,</if>
            <if
                test="empCode != null and empCode != ''">emp_code,</if>
            <if
                test="empName != null and empName != ''">emp_name,</if>
            <if
                test="vendorFieldName != null and vendorFieldName != ''">vendor_field_name,</if>
            <if
                test="vendorFieldCode != null and vendorFieldCode != ''">vendor_field_code,</if>
            <if
                test="gatherSpot != null and gatherSpot != ''">gather_spot,</if>
            <if
                test="samplingRate != null">sampling_rate,</if>
            <if
                test="limitUpdateTime != null and limitUpdateTime != ''">limit_update_time,</if>
            <if
                test="vendorFieldDesc != null and vendorFieldDesc != ''">vendor_field_desc,</if>
            <if
                test="carrierCode != null and carrierCode != ''">carrier_code,</if>
            <if
                test="intputQty != null">intput_qty,</if>
            <if test="fttQty != null">ftt_qty,</if>
            <if
                test="parameter != null and parameter != ''">parameter,</if>
            <if
                test="characteristic != null and characteristic != ''">characteristic,</if>
            <if
                test="cc != null and cc != ''">cc,</if>
            <if test="sc != null and sc != ''">sc,</if>
            <if
                test="spc != null and spc != ''">spc,</if>
            <if
                test="standardValue != null and standardValue != ''">standard_value,</if>
            <if
                test="upperLimit != null">upper_limit,</if>
            <if test="lowerLimit != null">
        lower_limit,</if>
            <if test="decimalValue != null">decimal_value,</if>
            <if
                test="unitCn != null and unitCn != ''">unit_cn,</if>
            <if
                test="unitEn != null and unitEn != ''">unit_en,</if>
            <if
                test="checkResult != null and checkResult != ''">check_result,</if>
            <if
                test="detectionMode != null and detectionMode != ''">detection_mode,</if>
            <if
                test="workShift != null and workShift != ''">work_shift,</if>
            <if
                test="collectTime != null and collectTime != ''">collect_time,</if>
            <if
                test="checkMode != null and checkMode != ''">check_mode,</if>
            <if
                test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if
                test="deviceName != null and deviceName != ''">device_name,</if>
            <if
                test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if
                test="modifyTime != null">modify_time,</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if
                test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if
                test="vendorProductSn != null and vendorProductSn != ''">#{vendorProductSn},</if>
            <if
                test="vendorProductBatch != null and vendorProductBatch != ''">
        #{vendorProductBatch},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">
        #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">
        #{cheryProductName},</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">
        #{cheryProductSn},</if>
            <if test="productBatchNo != null and productBatchNo != ''">
        #{productBatchNo},</if>
            <if test="manufactureNo != null and manufactureNo != ''">
        #{manufactureNo},</if>
            <if test="plantId != null and plantId != ''">#{plantId},</if>
            <if
                test="plantName != null and plantName != ''">#{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if
                test="productionLineName != null and productionLineName != ''">
        #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if
                test="stationName != null and stationName != ''">#{stationName},</if>
            <if
                test="empCode != null and empCode != ''">#{empCode},</if>
            <if
                test="empName != null and empName != ''">#{empName},</if>
            <if
                test="vendorFieldName != null and vendorFieldName != ''">#{vendorFieldName},</if>
            <if
                test="vendorFieldCode != null and vendorFieldCode != ''">#{vendorFieldCode},</if>
            <if
                test="gatherSpot != null and gatherSpot != ''">#{gatherSpot},</if>
            <if
                test="samplingRate != null">#{samplingRate},</if>
            <if
                test="limitUpdateTime != null and limitUpdateTime != ''">#{limitUpdateTime},</if>
            <if
                test="vendorFieldDesc != null and vendorFieldDesc != ''">#{vendorFieldDesc},</if>
            <if
                test="carrierCode != null and carrierCode != ''">#{carrierCode},</if>
            <if
                test="intputQty != null">#{intputQty},</if>
            <if test="fttQty != null">#{fttQty},</if>
            <if
                test="parameter != null and parameter != ''">#{parameter},</if>
            <if
                test="characteristic != null and characteristic != ''">#{characteristic},</if>
            <if
                test="cc != null and cc != ''">#{cc},</if>
            <if test="sc != null and sc != ''">#{sc},</if>
            <if
                test="spc != null and spc != ''">#{spc},</if>
            <if
                test="standardValue != null and standardValue != ''">#{standardValue},</if>
            <if
                test="upperLimit != null">#{upperLimit},</if>
            <if test="lowerLimit != null">
        #{lowerLimit},</if>
            <if test="decimalValue != null">#{decimalValue},</if>
            <if
                test="unitCn != null and unitCn != ''">#{unitCn},</if>
            <if
                test="unitEn != null and unitEn != ''">#{unitEn},</if>
            <if
                test="checkResult != null and checkResult != ''">#{checkResult},</if>
            <if
                test="detectionMode != null and detectionMode != ''">#{detectionMode},</if>
            <if
                test="workShift != null and workShift != ''">#{workShift},</if>
            <if
                test="collectTime != null and collectTime != ''">#{collectTime},</if>
            <if
                test="checkMode != null and checkMode != ''">#{checkMode},</if>
            <if
                test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if
                test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if
                test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if
                test="modifyTime != null">#{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierProCps" parameterType="SupplierProCps"> update supplier_pro_cps <trim
            prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no =
        #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name
        = #{vendorProductName},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn
        = #{vendorProductSn},</if>
            <if test="vendorProductBatch != null and vendorProductBatch != ''">vendor_product_batch
        = #{vendorProductBatch},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no
        = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name
        = #{cheryProductName},</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">chery_product_sn
        = #{cheryProductSn},</if>
            <if test="productBatchNo != null and productBatchNo != ''">product_batch_no
        = #{productBatchNo},</if>
            <if test="manufactureNo != null and manufactureNo != ''">manufacture_no
        = #{manufactureNo},</if>
            <if test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if
                test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id =
        #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name
        = #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">station_id =
        #{stationId},</if>
            <if test="stationName != null and stationName != ''">station_name =
        #{stationName},</if>
            <if test="empCode != null and empCode != ''">emp_code = #{empCode},</if>
            <if
                test="empName != null and empName != ''">emp_name = #{empName},</if>
            <if
                test="vendorFieldName != null and vendorFieldName != ''">vendor_field_name =
        #{vendorFieldName},</if>
            <if test="vendorFieldCode != null and vendorFieldCode != ''">vendor_field_code
        = #{vendorFieldCode},</if>
            <if test="gatherSpot != null and gatherSpot != ''">gather_spot =
        #{gatherSpot},</if>
            <if test="samplingRate != null">sampling_rate = #{samplingRate},</if>
            <if
                test="limitUpdateTime != null and limitUpdateTime != ''">limit_update_time =
        #{limitUpdateTime},</if>
            <if test="vendorFieldDesc != null and vendorFieldDesc != ''">vendor_field_desc
        = #{vendorFieldDesc},</if>
            <if test="carrierCode != null and carrierCode != ''">carrier_code
        = #{carrierCode},</if>
            <if test="intputQty != null">intput_qty = #{intputQty},</if>
            <if
                test="fttQty != null">ftt_qty = #{fttQty},</if>
            <if
                test="parameter != null and parameter != ''">parameter = #{parameter},</if>
            <if
                test="characteristic != null and characteristic != ''">characteristic =
        #{characteristic},</if>
            <if test="cc != null and cc != ''">cc = #{cc},</if>
            <if
                test="sc != null and sc != ''">sc = #{sc},</if>
            <if test="spc != null and spc != ''">spc
        = #{spc},</if>
            <if test="standardValue != null and standardValue != ''">standard_value =
        #{standardValue},</if>
            <if test="upperLimit != null">upper_limit = #{upperLimit},</if>
            <if
                test="lowerLimit != null">lower_limit = #{lowerLimit},</if>
            <if
                test="decimalValue != null">decimal_value = #{decimalValue},</if>
            <if
                test="unitCn != null and unitCn != ''">unit_cn = #{unitCn},</if>
            <if
                test="unitEn != null and unitEn != ''">unit_en = #{unitEn},</if>
            <if
                test="checkResult != null and checkResult != ''">check_result = #{checkResult},</if>
            <if
                test="detectionMode != null and detectionMode != ''">detection_mode =
        #{detectionMode},</if>
            <if test="workShift != null and workShift != ''">work_shift =
        #{workShift},</if>
            <if test="collectTime != null and collectTime != ''">collect_time =
        #{collectTime},</if>
            <if test="checkMode != null and checkMode != ''">check_mode =
        #{checkMode},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code =
        #{deviceCode},</if>
            <if test="deviceName != null and deviceName != ''">device_name =
        #{deviceName},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if
                test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierProCpsById" parameterType="Long"> delete from supplier_pro_cps where
        id = #{id} </delete>

    <delete id="deleteSupplierProCpsByIds" parameterType="String"> delete from supplier_pro_cps
        where id in <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>

    <select id="selectSupplierProCpsByIds" resultMap="SupplierProCpsResult">
        <include refid="selectSupplierProCpsVo" />  where out_batch_no = #{batchNo}
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_pro_cps <set>
                <if test="item.supplierCode != null and item.supplierCode != ''">supplier_code =
        #{item.supplierCode},</if>
                <if test="item.supplierName != null and item.supplierName != ''">supplier_name
        = #{item.supplierName},</if>
                <if
                    test="item.vendorProductNo != null and item.vendorProductNo != ''">vendor_product_no
        = #{item.vendorProductNo},</if>
                <if
                    test="item.vendorProductName != null and item.vendorProductName != ''">vendor_product_name
        = #{item.vendorProductName},</if>
                <if
                    test="item.vendorProductSn != null and item.vendorProductSn != ''">vendor_product_sn
        = #{item.vendorProductSn},</if>
                <if
                    test="item.vendorProductBatch != null and item.vendorProductBatch != ''">vendor_product_batch
        = #{item.vendorProductBatch},</if>
                <if
                    test="item.cheryProductNo != null and item.cheryProductNo != ''">chery_product_no
        = #{item.cheryProductNo},</if>
                <if
                    test="item.cheryProductName != null and item.cheryProductName != ''">chery_product_name
        = #{item.cheryProductName},</if>
                <if
                    test="item.cheryProductSn != null and item.cheryProductSn != ''">chery_product_sn
        = #{item.cheryProductSn},</if>
                <if
                    test="item.productBatchNo != null and item.productBatchNo != ''">product_batch_no
        = #{item.productBatchNo},</if>
                <if
                    test="item.manufactureNo != null and item.manufactureNo != ''">manufacture_no =
        #{item.manufactureNo},</if>
                <if test="item.plantId != null and item.plantId != ''">plant_id =
        #{item.plantId},</if>
                <if test="item.plantName != null and item.plantName != ''">plant_name =
        #{item.plantName},</if>
                <if test="item.workshopId != null and item.workshopId != ''">workshop_id
        = #{item.workshopId},</if>
                <if test="item.workshopName != null and item.workshopName != ''">workshop_name
        = #{item.workshopName},</if>
                <if
                    test="item.productionLineId != null and item.productionLineId != ''">production_line_id
        = #{item.productionLineId},</if>
                <if
                    test="item.productionLineName != null and item.productionLineName != ''">production_line_name
        = #{item.productionLineName},</if>
                <if test="item.stationId != null and item.stationId != ''">station_id
        = #{item.stationId},</if>
                <if test="item.stationName != null and item.stationName != ''">station_name
        = #{item.stationName},</if>
                <if test="item.empCode != null and item.empCode != ''">emp_code =
        #{item.empCode},</if>
                <if test="item.empName != null and item.empName != ''">emp_name =
        #{item.empName},</if>
                <if test="item.vendorFieldName != null and item.vendorFieldName != ''">vendor_field_name
        = #{item.vendorFieldName},</if>
                <if
                    test="item.vendorFieldCode != null and item.vendorFieldCode != ''">vendor_field_code
        = #{item.vendorFieldCode},</if>
                <if test="item.gatherSpot != null and item.gatherSpot != ''">gather_spot
        = #{item.gatherSpot},</if>
                <if test="item.samplingRate != null">sampling_rate =
        #{item.samplingRate},</if>
                <if
                    test="item.limitUpdateTime != null and item.limitUpdateTime != ''">limit_update_time
        = #{item.limitUpdateTime},</if>
                <if
                    test="item.vendorFieldDesc != null and item.vendorFieldDesc != ''">vendor_field_desc
        = #{item.vendorFieldDesc},</if>
                <if
                    test="item.carrierCode != null and item.carrierCode != ''">carrier_code =
        #{item.carrierCode},</if>
                <if test="item.intputQty != null">intput_qty = #{item.intputQty},</if>
                <if
                    test="item.fttQty != null">ftt_qty = #{item.fttQty},</if>
                <if
                    test="item.parameter != null and item.parameter != ''">parameter =
        #{item.parameter},</if>
                <if test="item.characteristic != null and item.characteristic != ''">characteristic
        = #{item.characteristic},</if>
                <if test="item.cc != null and item.cc != ''">cc = #{item.cc},</if>
                <if
                    test="item.sc != null and item.sc != ''">sc = #{item.sc},</if>
                <if
                    test="item.spc != null and item.spc != ''">spc = #{item.spc},</if>
                <if
                    test="item.standardValue != null and item.standardValue != ''">standard_value =
        #{item.standardValue},</if>
                <if test="item.upperLimit != null">upper_limit =
        #{item.upperLimit},</if>
                <if test="item.lowerLimit != null">lower_limit = #{item.lowerLimit},</if>
                <if
                    test="item.decimalValue != null">decimal_value = #{item.decimalValue},</if>
                <if
                    test="item.unitCn != null and item.unitCn != ''">unit_cn = #{item.unitCn},</if>
                <if
                    test="item.unitEn != null and item.unitEn != ''">unit_en = #{item.unitEn},</if>
                <if
                    test="item.checkResult != null and item.checkResult != ''">check_result =
        #{item.checkResult},</if>
                <if test="item.detectionMode != null and item.detectionMode != ''">detection_mode
        = #{item.detectionMode},</if>
                <if test="item.workShift != null and item.workShift != ''">work_shift
        = #{item.workShift},</if>
                <if test="item.collectTime != null and item.collectTime != ''">collect_time
        = #{item.collectTime},</if>
                <if test="item.checkMode != null and item.checkMode != ''">check_mode
        = #{item.checkMode},</if>
                <if test="item.deviceCode != null and item.deviceCode != ''">device_code
        = #{item.deviceCode},</if>
                <if test="item.deviceName != null and item.deviceName != ''">device_name
        = #{item.deviceName},</if>
                <if test="item.hasPush != null">has_push = #{item.hasPush},</if>
                <if
                    test="item.insertTime != null">insert_time = #{item.insertTime},</if>
                <if
                    test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if
                    test="item.insertBy != null and item.insertBy != ''">insert_by =
        #{item.insertBy},</if>
            <if
                    test="item.outBatchNo != null and item.outBatchNo != ''">out_batch_no =
                #{item.outBatchNo}</if>

            </set> where id = #{item.id} </foreach>
    </update>
</mapper>