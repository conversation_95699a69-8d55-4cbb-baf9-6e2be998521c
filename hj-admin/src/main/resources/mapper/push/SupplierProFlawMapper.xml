<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.push.SupplierProFlawMapper">

    <resultMap type="SupplierProFlaw" id="SupplierProFlawResult">
        <result property="id" column="id" />
        <result property="supplierCode" column="supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="plantId" column="plant_id" />
        <result property="plantName" column="plant_name" />
        <result property="workshopId" column="workshop_id" />
        <result property="workshopName" column="workshop_name" />
        <result property="productionLineId" column="production_line_id" />
        <result property="productionLineName" column="production_line_name" />
        <result property="stationId" column="station_id" />
        <result property="stationName" column="station_name" />
        <result property="defectsCode" column="defects_code" />
        <result property="defectsName" column="defects_name" />
        <result property="classOfName" column="class_of_name" />
        <result property="vendorProductNo" column="vendor_product_no" />
        <result property="vendorProductName" column="vendor_product_name" />
        <result property="vendorProductBatch" column="vendor_product_batch" />
        <result property="vendorProductSn" column="vendor_product_sn" />
        <result property="cheryProductNo" column="chery_product_no" />
        <result property="cheryProductName" column="chery_product_name" />
        <result property="cheryProductSn" column="chery_product_sn" />
        <result property="productBatchNo" column="product_batch_no" />
        <result property="manufactureNo" column="manufacture_no" />
        <result property="workShift" column="work_shift" />
        <result property="numberofdefect" column="numberofdefect" />
        <result property="defectsDesc" column="defects_desc" />
        <result property="defectsLevel" column="defects_level" />
        <result property="statisticalTime" column="statistical_time" />
        <result property="hasPush" column="has_push" />
        <result property="insertTime" column="insert_time" />
        <result property="modifyTime" column="modify_time" />
        <result property="insertBy" column="insert_by" />
        <result property="modifyBy" column="modify_by" />
    </resultMap>

    <sql id="selectSupplierProFlawVo"> select id, supplier_code, supplier_name, plant_id,
        plant_name, workshop_id, workshop_name, production_line_id, production_line_name,
        station_id, station_name, defects_code, defects_name, class_of_name, vendor_product_no,
        vendor_product_name, vendor_product_batch, vendor_product_sn, chery_product_no,
        chery_product_name, chery_product_sn, product_batch_no, manufacture_no, work_shift,
        numberofdefect, defects_desc, defects_level, statistical_time, has_push, insert_time,
        modify_time, insert_by, modify_by from supplier_pro_flaw </sql>

    <select id="selectSupplierProFlawList" parameterType="SupplierProFlaw"
        resultMap="SupplierProFlawResult">
        <include refid="selectSupplierProFlawVo" />
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code =
        #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and
        supplier_name like concat('%', #{supplierName}, '%')</if>
            <if
                test="plantId != null  and plantId != ''"> and plant_id = #{plantId}</if>
            <if
                test="plantName != null  and plantName != ''"> and plant_name like concat('%',
        #{plantName}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and workshop_id
        = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and
        workshop_name like concat('%', #{workshopName}, '%')</if>
            <if
                test="productionLineId != null  and productionLineId != ''"> and production_line_id
        = #{productionLineId}</if>
            <if
                test="productionLineName != null  and productionLineName != ''"> and
        production_line_name like concat('%', #{productionLineName}, '%')</if>
            <if
                test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if
                test="stationName != null  and stationName != ''"> and station_name like concat('%',
        #{stationName}, '%')</if>
            <if test="defectsCode != null  and defectsCode != ''"> and
        defects_code = #{defectsCode}</if>
            <if test="defectsName != null  and defectsName != ''"> and
        defects_name like concat('%', #{defectsName}, '%')</if>
            <if
                test="classOfName != null  and classOfName != ''"> and class_of_name like
        concat('%', #{classOfName}, '%')</if>
            <if
                test="vendorProductNo != null  and vendorProductNo != ''"> and vendor_product_no =
        #{vendorProductNo}</if>
            <if test="vendorProductName != null  and vendorProductName != ''">
        and vendor_product_name like concat('%', #{vendorProductName}, '%')</if>
            <if
                test="vendorProductBatch != null  and vendorProductBatch != ''"> and
        vendor_product_batch = #{vendorProductBatch}</if>
            <if
                test="vendorProductSn != null  and vendorProductSn != ''"> and vendor_product_sn =
        #{vendorProductSn}</if>
            <if test="cheryProductNo != null  and cheryProductNo != ''"> and
        chery_product_no = #{cheryProductNo}</if>
            <if
                test="cheryProductName != null  and cheryProductName != ''"> and chery_product_name
        like concat('%', #{cheryProductName}, '%')</if>
            <if
                test="cheryProductSn != null  and cheryProductSn != ''"> and chery_product_sn =
        #{cheryProductSn}</if>
            <if test="productBatchNo != null  and productBatchNo != ''"> and
        product_batch_no = #{productBatchNo}</if>
            <if
                test="manufactureNo != null  and manufactureNo != ''"> and manufacture_no =
        #{manufactureNo}</if>
            <if test="workShift != null  and workShift != ''"> and work_shift =
        #{workShift}</if>
            <if test="numberofdefect != null "> and numberofdefect = #{numberofdefect}</if>
            <if
                test="defectsDesc != null  and defectsDesc != ''"> and defects_desc = #{defectsDesc}</if>
            <if
                test="defectsLevel != null  and defectsLevel != ''"> and defects_level =
        #{defectsLevel}</if>
            <if test="statisticalTime != null  and statisticalTime != ''"> and
        statistical_time = #{statisticalTime}</if>
            <if test="hasPush != null "> and has_push =
        #{hasPush}</if>
            <if test="insertTime != null "> and insert_time = #{insertTime}</if>
            <if
                test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if
                test="insertBy != null  and insertBy != ''"> and insert_by = #{insertBy}</if>
              <if
                test="modifyBy != null  and modifyBy != ''"> and modify_by = #{modifyBy}</if>
<if
                    test="outBatchNo != null "> and out_batch_no = #{outBatchNo}</if>
        </where>
    </select>

    <select id="selectSupplierProFlawById" parameterType="Long" resultMap="SupplierProFlawResult">
        <include refid="selectSupplierProFlawVo" /> where id = #{id} </select>

    <insert id="insertSupplierProFlaw" parameterType="SupplierProFlaw" useGeneratedKeys="true"
        keyProperty="id"> insert into supplier_pro_flaw <trim prefix="(" suffix=")"
            suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if
                test="plantId != null and plantId != ''">plant_id,</if>
            <if
                test="plantName != null and plantName != ''">plant_name,</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id,</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id,</if>
            <if
                test="productionLineName != null and productionLineName != ''">production_line_name,</if>
            <if
                test="stationId != null and stationId != ''">station_id,</if>
            <if
                test="stationName != null and stationName != ''">station_name,</if>
            <if
                test="defectsCode != null and defectsCode != ''">defects_code,</if>
            <if
                test="defectsName != null and defectsName != ''">defects_name,</if>
            <if
                test="classOfName != null and classOfName != ''">class_of_name,</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no,</if>
            <if
                test="vendorProductName != null and vendorProductName != ''">vendor_product_name,</if>
            <if
                test="vendorProductBatch != null and vendorProductBatch != ''">vendor_product_batch,</if>
            <if
                test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn,</if>
            <if
                test="cheryProductNo != null and cheryProductNo != ''">chery_product_no,</if>
            <if
                test="cheryProductName != null and cheryProductName != ''">chery_product_name,</if>
            <if
                test="cheryProductSn != null and cheryProductSn != ''">chery_product_sn,</if>
            <if
                test="productBatchNo != null and productBatchNo != ''">product_batch_no,</if>
            <if
                test="manufactureNo != null and manufactureNo != ''">manufacture_no,</if>
            <if
                test="workShift != null and workShift != ''">work_shift,</if>
            <if
                test="numberofdefect != null">numberofdefect,</if>
            <if
                test="defectsDesc != null and defectsDesc != ''">defects_desc,</if>
            <if
                test="defectsLevel != null and defectsLevel != ''">defects_level,</if>
            <if
                test="statisticalTime != null and statisticalTime != ''">statistical_time,</if>
            <if
                test="hasPush != null">has_push,</if>
            <if test="insertTime != null">insert_time,</if>
            <if
                test="modifyTime != null">modify_time,</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by,</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by,</if>
        </trim>
        <trim
            prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if
                test="plantId != null and plantId != ''">#{plantId},</if>
            <if
                test="plantName != null and plantName != ''">#{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">#{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">#{productionLineId},</if>
            <if
                test="productionLineName != null and productionLineName != ''">
        #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if
                test="stationName != null and stationName != ''">#{stationName},</if>
            <if
                test="defectsCode != null and defectsCode != ''">#{defectsCode},</if>
            <if
                test="defectsName != null and defectsName != ''">#{defectsName},</if>
            <if
                test="classOfName != null and classOfName != ''">#{classOfName},</if>
            <if
                test="vendorProductNo != null and vendorProductNo != ''">#{vendorProductNo},</if>
            <if
                test="vendorProductName != null and vendorProductName != ''">#{vendorProductName},</if>
            <if
                test="vendorProductBatch != null and vendorProductBatch != ''">
        #{vendorProductBatch},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">
        #{vendorProductSn},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">
        #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">
        #{cheryProductName},</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">
        #{cheryProductSn},</if>
            <if test="productBatchNo != null and productBatchNo != ''">
        #{productBatchNo},</if>
            <if test="manufactureNo != null and manufactureNo != ''">
        #{manufactureNo},</if>
            <if test="workShift != null and workShift != ''">#{workShift},</if>
            <if
                test="numberofdefect != null">#{numberofdefect},</if>
            <if
                test="defectsDesc != null and defectsDesc != ''">#{defectsDesc},</if>
            <if
                test="defectsLevel != null and defectsLevel != ''">#{defectsLevel},</if>
            <if
                test="statisticalTime != null and statisticalTime != ''">#{statisticalTime},</if>
            <if
                test="hasPush != null">#{hasPush},</if>
            <if test="insertTime != null">#{insertTime},</if>
            <if
                test="modifyTime != null">#{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">#{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">#{modifyBy},</if>
        </trim>
    </insert>

    <update id="updateSupplierProFlaw" parameterType="SupplierProFlaw"> update supplier_pro_flaw <trim
            prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if
                test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if
                test="plantId != null and plantId != ''">plant_id = #{plantId},</if>
            <if
                test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if
                test="workshopId != null and workshopId != ''">workshop_id = #{workshopId},</if>
            <if
                test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if
                test="productionLineId != null and productionLineId != ''">production_line_id =
        #{productionLineId},</if>
            <if test="productionLineName != null and productionLineName != ''">production_line_name
        = #{productionLineName},</if>
            <if test="stationId != null and stationId != ''">station_id =
        #{stationId},</if>
            <if test="stationName != null and stationName != ''">station_name =
        #{stationName},</if>
            <if test="defectsCode != null and defectsCode != ''">defects_code =
        #{defectsCode},</if>
            <if test="defectsName != null and defectsName != ''">defects_name =
        #{defectsName},</if>
            <if test="classOfName != null and classOfName != ''">class_of_name =
        #{classOfName},</if>
            <if test="vendorProductNo != null and vendorProductNo != ''">vendor_product_no
        = #{vendorProductNo},</if>
            <if test="vendorProductName != null and vendorProductName != ''">vendor_product_name
        = #{vendorProductName},</if>
            <if
                test="vendorProductBatch != null and vendorProductBatch != ''">vendor_product_batch
        = #{vendorProductBatch},</if>
            <if test="vendorProductSn != null and vendorProductSn != ''">vendor_product_sn
        = #{vendorProductSn},</if>
            <if test="cheryProductNo != null and cheryProductNo != ''">chery_product_no
        = #{cheryProductNo},</if>
            <if test="cheryProductName != null and cheryProductName != ''">chery_product_name
        = #{cheryProductName},</if>
            <if test="cheryProductSn != null and cheryProductSn != ''">chery_product_sn
        = #{cheryProductSn},</if>
            <if test="productBatchNo != null and productBatchNo != ''">product_batch_no
        = #{productBatchNo},</if>
            <if test="manufactureNo != null and manufactureNo != ''">manufacture_no
        = #{manufactureNo},</if>
            <if test="workShift != null and workShift != ''">work_shift =
        #{workShift},</if>
            <if test="numberofdefect != null">numberofdefect = #{numberofdefect},</if>
            <if
                test="defectsDesc != null and defectsDesc != ''">defects_desc = #{defectsDesc},</if>
            <if
                test="defectsLevel != null and defectsLevel != ''">defects_level = #{defectsLevel},</if>
            <if
                test="statisticalTime != null and statisticalTime != ''">statistical_time =
        #{statisticalTime},</if>
            <if test="hasPush != null">has_push = #{hasPush},</if>
            <if
                test="insertTime != null">insert_time = #{insertTime},</if>
            <if
                test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if
                test="insertBy != null and insertBy != ''">insert_by = #{insertBy},</if>
            <if
                test="modifyBy != null and modifyBy != ''">modify_by = #{modifyBy},</if>
        </trim>
        where id = #{id} </update>

    <delete id="deleteSupplierProFlawById" parameterType="Long"> delete from supplier_pro_flaw where
        id = #{id} </delete>

    <delete id="deleteSupplierProFlawByIds" parameterType="String"> delete from supplier_pro_flaw
        where id in <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>

    <select id="selectSupplierProFlawByIds" resultMap="SupplierProFlawResult">
        <include refid="selectSupplierProFlawVo" />  where out_batch_no = #{batchNo}
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";"> update supplier_pro_flaw <set>
                <if test="item.supplierCode != null and item.supplierCode != ''">supplier_code =
        #{item.supplierCode},</if>
                <if test="item.supplierName != null and item.supplierName != ''">supplier_name
        = #{item.supplierName},</if>
                <if test="item.plantId != null and item.plantId != ''">plant_id
        = #{item.plantId},</if>
                <if test="item.plantName != null and item.plantName != ''">plant_name
        = #{item.plantName},</if>
                <if test="item.workshopId != null and item.workshopId != ''">workshop_id
        = #{item.workshopId},</if>
                <if test="item.workshopName != null and item.workshopName != ''">workshop_name
        = #{item.workshopName},</if>
                <if
                    test="item.productionLineId != null and item.productionLineId != ''">production_line_id
        = #{item.productionLineId},</if>
                <if
                    test="item.productionLineName != null and item.productionLineName != ''">production_line_name
        = #{item.productionLineName},</if>
                <if test="item.stationId != null and item.stationId != ''">station_id
        = #{item.stationId},</if>
                <if test="item.stationName != null and item.stationName != ''">station_name
        = #{item.stationName},</if>
                <if test="item.defectsCode != null and item.defectsCode != ''">defects_code
        = #{item.defectsCode},</if>
                <if test="item.defectsName != null and item.defectsName != ''">defects_name
        = #{item.defectsName},</if>
                <if test="item.classOfName != null and item.classOfName != ''">class_of_name
        = #{item.classOfName},</if>
                <if
                    test="item.vendorProductNo != null and item.vendorProductNo != ''">vendor_product_no
        = #{item.vendorProductNo},</if>
                <if
                    test="item.vendorProductName != null and item.vendorProductName != ''">vendor_product_name
        = #{item.vendorProductName},</if>
                <if
                    test="item.vendorProductBatch != null and item.vendorProductBatch != ''">vendor_product_batch
        = #{item.vendorProductBatch},</if>
                <if
                    test="item.vendorProductSn != null and item.vendorProductSn != ''">vendor_product_sn
        = #{item.vendorProductSn},</if>
                <if
                    test="item.cheryProductNo != null and item.cheryProductNo != ''">chery_product_no
        = #{item.cheryProductNo},</if>
                <if
                    test="item.cheryProductName != null and item.cheryProductName != ''">chery_product_name
        = #{item.cheryProductName},</if>
                <if
                    test="item.cheryProductSn != null and item.cheryProductSn != ''">chery_product_sn
        = #{item.cheryProductSn},</if>
                <if
                    test="item.productBatchNo != null and item.productBatchNo != ''">product_batch_no
        = #{item.productBatchNo},</if>
                <if
                    test="item.manufactureNo != null and item.manufactureNo != ''">manufacture_no =
        #{item.manufactureNo},</if>
                <if test="item.workShift != null and item.workShift != ''">work_shift
        = #{item.workShift},</if>
                <if test="item.numberofdefect != null">numberofdefect =
        #{item.numberofdefect},</if>
                <if test="item.defectsDesc != null and item.defectsDesc != ''">defects_desc
        = #{item.defectsDesc},</if>
                <if test="item.defectsLevel != null and item.defectsLevel != ''">defects_level
        = #{item.defectsLevel},</if>
                <if
                    test="item.statisticalTime != null and item.statisticalTime != ''">statistical_time
        = #{item.statisticalTime},</if>
                <if test="item.hasPush != null">has_push = #{item.hasPush},</if>
                <if
                    test="item.insertTime != null">insert_time = #{item.insertTime},</if>
                <if
                    test="item.modifyTime != null">modify_time = #{item.modifyTime},</if>
                <if
                    test="item.insertBy != null and item.insertBy != ''">insert_by =
        #{item.insertBy},</if>
            <if
                    test="item.outBatchNo != null and item.outBatchNo != ''">out_batch_no =
                #{item.outBatchNo}</if>

            </set> where id = #{item.id} </foreach>
    </update>
</mapper>