<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.confirm.EdiPushFieldConfirmMapper">
    
    <resultMap type="EdiPushFieldConfirm" id="EdiPushFieldConfirmResult">
        <result property="id"    column="id"    />
        <result property="batchNo" column="batch_no"    />
        <result property="fieldMaintainerId"    column="field_maintainer_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEdiPushFieldConfirmVo">
        select id, batch_no, field_maintainer_id, create_by, create_time, update_by, update_time from edi_push_field_confirm
    </sql>

    <select id="selectEdiPushFieldConfirmList" parameterType="EdiPushFieldConfirm" resultMap="EdiPushFieldConfirmResult">
        <include refid="selectEdiPushFieldConfirmVo"/>
        <where>  
            <if test="batchNo != null">
                and batch_no = #{batchNo}</if>
            <if test="fieldMaintainerId != null "> and field_maintainer_id = #{fieldMaintainerId}</if>
        </where>
    </select>
    
    <select id="selectEdiPushFieldConfirmById" parameterType="Long" resultMap="EdiPushFieldConfirmResult">
        <include refid="selectEdiPushFieldConfirmVo"/>
        where id = #{id}
    </select>

    <insert id="insertEdiPushFieldConfirm" parameterType="EdiPushFieldConfirm" useGeneratedKeys="true" keyProperty="id">
        insert into edi_push_field_confirm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null">batch_no,</if>
            <if test="fieldMaintainerId != null">field_maintainer_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null">#{batchNo},</if>
            <if test="fieldMaintainerId != null">#{fieldMaintainerId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEdiPushFieldConfirm" parameterType="EdiPushFieldConfirm">
        update edi_push_field_confirm
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="fieldMaintainerId != null">field_maintainer_id = #{fieldMaintainerId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEdiPushFieldConfirmById" parameterType="Long">
        delete from edi_push_field_confirm where id = #{id}
    </delete>

    <delete id="deleteEdiPushFieldConfirmByIds" parameterType="String">
        delete from edi_push_field_confirm where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="batchInsert" resultType="int">
        insert into edi_push_field_confirm (batch_no, field_maintainer_id, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.batchNo}, #{item.fieldMaintainerId}, #{item.createBy}, #{item.createTime}, #{item.updateBy},
            #{item.updateTime})
        </foreach>
    </select>
</mapper>