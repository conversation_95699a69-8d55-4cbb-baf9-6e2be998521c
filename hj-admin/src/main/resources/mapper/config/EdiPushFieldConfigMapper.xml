<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.config.EdiPushFieldConfigMapper">
    
    <resultMap type="EdiPushFieldConfig" id="EdiPushFieldConfigResult">
        <result property="id"    column="id"    />
        <result property="interfaceConfigId"    column="interface_config_id"    />
        <result property="interfaceConfigName"    column="interface_config_name"    />
        <result property="columnName"    column="column_name"    />
        <result property="fieldMaintainerId"    column="field_maintainer_id"    />
        <result property="fieldMaintainerName"    column="field_maintainer_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEdiPushFieldConfigVo">
        select id, interface_config_id, interface_config_name, column_name, field_maintainer_id, field_maintainer_name, create_by, create_time, update_by, update_time from edi_push_field_config
    </sql>

    <select id="selectEdiPushFieldConfigList" parameterType="EdiPushFieldConfig" resultMap="EdiPushFieldConfigResult">
        <include refid="selectEdiPushFieldConfigVo"/>
        <where>  
            <if test="interfaceConfigId != null "> and interface_config_id = #{interfaceConfigId}</if>
            <if test="columnName != null  and columnName != ''"> and column_name like concat('%', #{columnName}, '%')</if>
            <if test="fieldMaintainerId != null "> and field_maintainer_id = #{fieldMaintainerId}</if>
            <if test="fieldMaintainerName != null  and fieldMaintainerName != ''"> and field_maintainer_name like concat('%', #{fieldMaintainerName}, '%')</if>
        </where>
    </select>
    
    <select id="selectEdiPushFieldConfigById" parameterType="Long" resultMap="EdiPushFieldConfigResult">
        <include refid="selectEdiPushFieldConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertEdiPushFieldConfig" parameterType="EdiPushFieldConfig" useGeneratedKeys="true" keyProperty="id">
        insert into edi_push_field_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceConfigId != null">interface_config_id,</if>
            <if test="interfaceConfigName != null">interface_config_name,</if>
            <if test="columnName != null">column_name,</if>
            <if test="fieldMaintainerId != null">field_maintainer_id,</if>
            <if test="fieldMaintainerName != null">field_maintainer_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="interfaceConfigId != null">#{interfaceConfigId},</if>
            <if test="interfaceConfigName != null">#{interfaceConfigName},</if>
            <if test="columnName != null">#{columnName},</if>
            <if test="fieldMaintainerId != null">#{fieldMaintainerId},</if>
            <if test="fieldMaintainerName != null">#{fieldMaintainerName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEdiPushFieldConfig" parameterType="EdiPushFieldConfig">
        update edi_push_field_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="interfaceConfigId != null">interface_config_id = #{interfaceConfigId},</if>
            <if test="interfaceConfigName != null">interface_config_name = #{interfaceConfigName},</if>
            <if test="columnName != null">column_name = #{columnName},</if>
            <if test="fieldMaintainerId != null">field_maintainer_id = #{fieldMaintainerId},</if>
            <if test="fieldMaintainerName != null">field_maintainer_name = #{fieldMaintainerName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEdiPushFieldConfigById" parameterType="Long">
        delete from edi_push_field_config where id = #{id}
    </delete>

    <delete id="deleteEdiPushFieldConfigByIds" parameterType="String">
        delete from edi_push_field_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByInterfacePathAndUser" resultMap="EdiPushFieldConfigResult">
        <include refid="selectEdiPushFieldConfigVo"/> where field_maintainer_id = #{userId} and
        interface_config_id = (
            select id from edi_push_interface_config where interface_path = #{interfacePath}
        );
    </select>
</mapper>