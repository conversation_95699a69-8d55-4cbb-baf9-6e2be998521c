<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.admin.mapper.config.EdiPushInterfaceConfigMapper">
    
    <resultMap type="EdiPushInterfaceConfig" id="EdiPushInterfaceConfigResult">
        <result property="id"    column="id"    />
        <result property="interfacePath"    column="interface_path"    />
        <result property="interfaceName"    column="interface_name"    />
        <result property="functionMaintainerId"    column="function_maintainer_id"    />
        <result property="functionMaintainerName"    column="function_maintainer_name"    />
        <result property="interfaceMaintainerId"    column="interface_maintainer_id"    />
        <result property="interfaceMaintainerName"    column="interface_maintainer_name"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEdiPushInterfaceConfigVo">
        select id, interface_path, interface_name, function_maintainer_id, function_maintainer_name, interface_maintainer_id, interface_maintainer_name, description, create_by, create_time, update_by, update_time from edi_push_interface_config
    </sql>

    <select id="selectEdiPushInterfaceConfigList" parameterType="EdiPushInterfaceConfig" resultMap="EdiPushInterfaceConfigResult">
        <include refid="selectEdiPushInterfaceConfigVo"/>
        <where>  
            <if test="interfacePath != null  and interfacePath != ''"> and interface_path = #{interfacePath}</if>
            <if test="interfaceName != null  and interfaceName != ''"> and interface_name like concat('%', #{interfaceName}, '%')</if>
            <if test="functionMaintainerId != null "> and function_maintainer_id = #{functionMaintainerId}</if>
            <if test="functionMaintainerName != null  and functionMaintainerName != ''"> and function_maintainer_name like concat('%', #{functionMaintainerName}, '%')</if>
            <if test="interfaceMaintainerId != null "> and interface_maintainer_id = #{interfaceMaintainerId}</if>
            <if test="interfaceMaintainerName != null  and interfaceMaintainerName != ''"> and interface_maintainer_name like concat('%', #{interfaceMaintainerName}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
    </select>
    
    <select id="selectEdiPushInterfaceConfigById" parameterType="Long" resultMap="EdiPushInterfaceConfigResult">
        <include refid="selectEdiPushInterfaceConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertEdiPushInterfaceConfig" parameterType="EdiPushInterfaceConfig" useGeneratedKeys="true" keyProperty="id">
        insert into edi_push_interface_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfacePath != null and interfacePath != ''">interface_path,</if>
            <if test="interfaceName != null and interfaceName != ''">interface_name,</if>
            <if test="functionMaintainerId != null">function_maintainer_id,</if>
            <if test="functionMaintainerName != null">function_maintainer_name,</if>
            <if test="interfaceMaintainerId != null">interface_maintainer_id,</if>
            <if test="interfaceMaintainerName != null">interface_maintainer_name,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="interfacePath != null and interfacePath != ''">#{interfacePath},</if>
            <if test="interfaceName != null and interfaceName != ''">#{interfaceName},</if>
            <if test="functionMaintainerId != null">#{functionMaintainerId},</if>
            <if test="functionMaintainerName != null">#{functionMaintainerName},</if>
            <if test="interfaceMaintainerId != null">#{interfaceMaintainerId},</if>
            <if test="interfaceMaintainerName != null">#{interfaceMaintainerName},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEdiPushInterfaceConfig" parameterType="EdiPushInterfaceConfig">
        update edi_push_interface_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="interfacePath != null and interfacePath != ''">interface_path = #{interfacePath},</if>
            <if test="interfaceName != null and interfaceName != ''">interface_name = #{interfaceName},</if>
            <if test="functionMaintainerId != null">function_maintainer_id = #{functionMaintainerId},</if>
            <if test="functionMaintainerName != null">function_maintainer_name = #{functionMaintainerName},</if>
            <if test="interfaceMaintainerId != null">interface_maintainer_id = #{interfaceMaintainerId},</if>
            <if test="interfaceMaintainerName != null">interface_maintainer_name = #{interfaceMaintainerName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEdiPushInterfaceConfigById" parameterType="Long">
        delete from edi_push_interface_config where id = #{id}
    </delete>

    <delete id="deleteEdiPushInterfaceConfigByIds" parameterType="String">
        delete from edi_push_interface_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOneByInterfacePath" parameterType="String" resultMap="EdiPushInterfaceConfigResult">
        <include refid="selectEdiPushInterfaceConfigVo"/>
        where interface_path = #{path}
    </select>

    <select id="selectByTableName" parameterType="String" resultMap="EdiPushInterfaceConfigResult">
        <include refid="selectEdiPushInterfaceConfigVo"/>
        where interface_path LIKE CONCAT('%', #{tableName}, '%')
        LIMIT 1
    </select>

    <select id="selectInterfaceNameByIds" resultType="string">
        select interface_name from edi_push_interface_config
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>