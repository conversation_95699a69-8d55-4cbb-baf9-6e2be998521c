<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增Push接口配置')"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-config-add">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">接口：</label>
                <div class="col-sm-8">
                    <select id="interface-selector" class="form-control" required>
                        <option value="">请选择</option>
                    </select>
                    <input name="interfacePath" id="interfacePath" type="hidden">
                    <input name="interfaceName" id="interfaceName" type="hidden">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">功能维护者：</label>
                <div class="col-sm-8">
                    <select id="functionMaintainer-selector" class="form-control" required>
                        <option value="">请选择</option>
                    </select>
                    <input name="functionMaintainerId" id="functionMaintainerId" type="hidden">
                    <input name="functionMaintainerName" id="functionMaintainerName" type="hidden">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">接口维护者：</label>
                <div class="col-sm-8">
                    <select id="interfaceMaintainer-selector" class="form-control" required>
                        <option value="">请选择</option>
                    </select>
                    <input name="interfaceMaintainerId" id="interfaceMaintainerId" type="hidden">
                    <input name="interfaceMaintainerName" id="interfaceMaintainerName" type="hidden">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">接口描述：</label>
                <div class="col-sm-8">
                    <textarea name="description" class="form-control"></textarea>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    const prefix = ctx + "edi/pushInterfaceConfig"
    $("#form-config-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-config-add').serialize());
        }
    }

    $(function () {
        initUserSelects();

        $.get(ctx + "push/allInterface", function (data) {
            const interfaceSelector = $("#interface-selector");
            $.each(data, function (path, name) {
                const option = $('<option></option>').attr('value', path).text(name + ' (' + path + ')').data('name', name);
                interfaceSelector.append(option);
            });
        });

        $("#interface-selector").on("change", function () {
            const selectedOption = $(this).find("option:selected");
            const path = selectedOption.val();
            const name = selectedOption.data("name");
            $("#interfacePath").val(path);
            $("#interfaceName").val(name);
        });
    });

    function initUserSelects() {
        $.post(ctx + "system/user/list", {}, function (data) {
            if (data && data.rows) {
                const functionMaintainerSelect = $("#functionMaintainer-selector");
                const functionMaintainerId = $("#functionMaintainerId").val();
                $.each(data.rows, function (index, user) {
                    const option = $('<option></option>').attr('value', user.userId).text(user.userName);
                    functionMaintainerSelect.append(option);
                });

                const interfaceMaintainerSelect = $("#interfaceMaintainer-selector");
                const interfaceMaintainerId = $("#interfaceMaintainerId").val();
                $.each(data.rows, function (index, user) {
                    const option = $('<option></option>').attr('value', user.userId).text(user.userName);
                    interfaceMaintainerSelect.append(option);
                });
            }
        }, "json");

        $("#functionMaintainer-selector").on("change", function () {
            const selectedOption = $(this).find("option:selected");
            const userId = selectedOption.val();
            const userName = selectedOption.text();
            $("#functionMaintainerId").val(userId);
            $("#functionMaintainerName").val(userName);
        });

        $("#interfaceMaintainer-selector").on("change", function () {
            const selectedOption = $(this).find("option:selected");
            const userId = selectedOption.val();
            const userName = selectedOption.text();
            $("#interfaceMaintainerId").val(userId);
            $("#interfaceMaintainerName").val(userName);
        });
    }
</script>
</body>
</html>