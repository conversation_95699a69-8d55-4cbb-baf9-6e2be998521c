<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('Push接口配置列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
<!--                            <li>-->
<!--                                <label>接口路径：</label>-->
<!--                                <select name="interfacePath" id="interfacePath" class="form-control">-->
<!--                                    <option value="">所有</option>-->
<!--                                </select>-->
<!--                            </li>-->
                            <li>
                                <label>接口名称：</label>
                                <select name="interfaceName" id="interfaceName" class="form-control">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>功能维护：</label>
                                <select name="functionMaintainerId" id="functionMaintainerId" class="form-control">
                                    <option value="">所有用户</option>
                                </select>
                            </li>
                            <li>
                                <label>接口维护：</label>
                                <select name="interfaceMaintainerId" id="interfaceMaintainerId" class="form-control">
                                    <option value="">所有用户</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="com:config:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="com:config:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="com:config:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
<!--                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="com:config:export">-->
<!--                    <i class="fa fa-download"></i> 导出-->
<!--                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        const editFlag = [[${@permission.hasPermi('com:config:edit')}]];
        const removeFlag = [[${@permission.hasPermi('com:config:remove')}]];
        const prefix = ctx + "edi/pushInterfaceConfig";

        $(function() {
            const options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "Push接口配置",
                columns: [{
                    checkbox: false
                },
                {
                    field: 'id',
                    title: '主键ID',
                    visible: false
                },
                {
                    field: 'interfacePath',
                    title: '接口路径'
                },
                {
                    field: 'interfaceName',
                    title: '接口名称'
                },
                {
                    field: 'functionMaintainerId',
                    title: '功能维护者ID'
                },
                {
                    field: 'functionMaintainerName',
                    title: '功能维护者姓名'
                },
                {
                    field: 'interfaceMaintainerId',
                    title: '接口维护者ID'
                },
                {
                    field: 'interfaceMaintainerName',
                    title: '接口维护者姓名'
                },
                {
                    field: 'description',
                    title: '接口描述'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        const actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
            initUserSelects();
            $.get(ctx + "push/allInterface", function(data) {
                const interfacePathSelect = $("#interfacePath");
                const interfaceNameSelect = $("#interfaceName");
                $.each(data, function(path, name) {
                    interfacePathSelect.append($('<option></option>').attr('value', path).text(path));
                    interfaceNameSelect.append($('<option></option>').attr('value', name).text(name));
                });
            });
        });

        function initUserSelects() {
            $.post(ctx + "system/user/list", {}, function (data) {
                if (data && data.rows) {
                    const functionMaintainerSelect = $("#functionMaintainerId");
                    const interfaceMaintainerSelect = $("#interfaceMaintainerId");

                    $.each(data.rows, function (index, user) {
                        const optionText = user.loginName + (user.userName ? " (" + user.userName + ")" : "");
                        const option = new Option(optionText, user.userId);
                        
                        functionMaintainerSelect.append(option.cloneNode(true));
                        interfaceMaintainerSelect.append(option.cloneNode(true));
                    });
                }
            }, "json");
        }
    </script>
</body>
</html>