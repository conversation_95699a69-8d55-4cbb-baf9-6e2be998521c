<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('Push接口字段配置列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>接口配置：</label>
                            <select name="interfaceConfigId" id="interfaceConfigId" class="form-control">
                                <option value="">所有接口</option>
                            </select>
                        </li>
                        <!--                            <li>-->
                        <!--                                <label>数据库字段名：</label>-->
                        <!--                                <input type="text" name="columnName"/>-->
                        <!--                            </li>-->
                        <li>
                            <label>字段维护：</label>
                            <select name="fieldMaintainerId" id="fieldMaintainerId" class="form-control">
                                <option value="">所有用户</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="com:config:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="com:config:edit">-->
            <!--                    <i class="fa fa-edit"></i> 修改-->
            <!--                </a>-->
            <!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="com:config:remove">-->
            <!--                    <i class="fa fa-remove"></i> 删除-->
            <!--                </a>-->
            <!--                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="com:config:export">-->
            <!--                    <i class="fa fa-download"></i> 导出-->
            <!--                </a>-->
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('com:config:edit')}]];
    var removeFlag = [[${@permission.hasPermi('com:config:remove')}]];
    var prefix = ctx + "edi/pushFieldConfig";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "Push接口字段配置",
            columns: [{
                checkbox: false
            },
                {
                    field: 'id',
                    title: '主键ID',
                    visible: false
                },
                {
                    field: 'interfaceConfigId',
                    title: '接口配置ID',
                    visible: false
                },
                {
                    field: 'interfaceConfigName',
                    title: '接口名称'
                },
                {
                    field: 'columnName',
                    title: '数据库字段名'
                },
                {
                    field: 'fieldMaintainerId',
                    title: '字段维护者ID'
                },
                {
                    field: 'fieldMaintainerName',
                    title: '字段维护者姓名'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
        initUserSelect();
        initInterfaceConfigSelect();
    });

    function initInterfaceConfigSelect() {
        $.post(ctx + "edi/pushInterfaceConfig/list", {}, function (data) {
            if (data && data.rows) {
                var interfaceSelect = $("#interfaceConfigId");
                $.each(data.rows, function (index, config) {
                    var optionText = config.interfaceName + " (" + config.interfacePath + ")";
                    interfaceSelect.append(new Option(optionText, config.id));
                });
            }
        }, "json");
    }

    function initUserSelect() {
        $.post(ctx + "system/user/list", {}, function (data) {
            if (data && data.rows) {
                var userSelect = $("#fieldMaintainerId");
                $.each(data.rows, function (index, user) {
                    var optionText = user.loginName + (user.userName ? " (" + user.userName + ")" : "");
                    userSelect.append(new Option(optionText, user.userId));
                });
            }
        }, "json");
    }
</script>
</body>
</html>