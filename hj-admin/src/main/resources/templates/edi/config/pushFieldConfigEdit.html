<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改Push接口字段配置')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-config-edit" th:object="${ediPushFieldConfig}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">接口配置：</label>
                    <div class="col-sm-8">
                        <select name="interfaceConfigId" id="interfaceConfigId" th:field="*{interfaceConfigId}" class="form-control" required>
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数据库字段名：</label>
                    <div class="col-sm-8">
                        <select name="columnName" id="columnName" th:field="*{columnName}" class="form-control">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">字段维护者：</label>
                    <div class="col-sm-8">
                        <select name="fieldMaintainerId" id="fieldMaintainerId" th:field="*{fieldMaintainerId}" class="form-control">
                            <option value="">请选择</option>
                        </select>
                        <input name="fieldMaintainerName" id="fieldMaintainerName" th:value="*{fieldMaintainerName}" type="hidden">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/pushFieldConfig";
        // 初始回显值（由后端渲染注入）
        const initial = {
            interfaceConfigId: /*[[${ediPushFieldConfig.interfaceConfigId}]]*/ null,
            columnName: /*[['' + ${ediPushFieldConfig.columnName}]]*/ '',
            fieldMaintainerId: /*[[${ediPushFieldConfig.fieldMaintainerId}]]*/ null
        };

        $("#form-config-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var selectedUserName = $("#fieldMaintainerId option:selected").data('username');
                $("#fieldMaintainerName").val(selectedUserName || '');
                $.operate.save(prefix + "/edit", $('#form-config-edit').serialize());
            }
        }

        var allInterfaceFields = {};

        $(function() {
            initUserSelect();

            $.get(ctx + "push/allInterfaceField", function(data) {
                allInterfaceFields = data;
                initInterfaceConfigSelect(); // 初始化接口配置并填充字段
            });

            $("#interfaceConfigId").on("change", function() {
                populateColumnNameSelect();
            });
        });

        function initInterfaceConfigSelect() {
            $.post(ctx + "edi/pushInterfaceConfig/list", {}, function (data) {
                if (data && data.rows) {
                    var interfaceSelect = $("#interfaceConfigId");
                    interfaceSelect.find("option:gt(0)").remove();
                    $.each(data.rows, function (index, config) {
                        var optionText = config.interfaceName + " (" + config.interfacePath + ")";
                        var option = new Option(optionText, config.id);
                        $(option).data('config', config);
                        if (initial.interfaceConfigId && config.id == initial.interfaceConfigId) {
                            option.selected = true;
                        }
                        interfaceSelect.append(option);
                    });
                    // 如未匹配到，保持第一项为空；匹配到则触发字段下拉填充
                    populateColumnNameSelect();
                }
            }, "json");
        }

        function populateColumnNameSelect() {
            var selectedOption = $("#interfaceConfigId option:selected");
            var selectedConfig = selectedOption.data('config');
            var columnNameSelect = $("#columnName");
            var currentColumnName = initial.columnName || columnNameSelect.val();
            columnNameSelect.empty().append('<option value="">请选择</option>');

            if (selectedConfig && allInterfaceFields[selectedConfig.interfacePath]) {
                var fields = allInterfaceFields[selectedConfig.interfacePath];
                fields.forEach(function(field) {
                    var option = $('<option></option>').attr('value', field).text(field);
                    if (field === currentColumnName) {
                        option.prop('selected', true);
                    }
                    columnNameSelect.append(option);
                });
            }
        }

        function initUserSelect() {
            $.post(ctx + "system/user/list", {}, function (data) {
                if (data && data.rows) {
                    var userSelect = $("#fieldMaintainerId");
                    userSelect.find("option:gt(0)").remove();
                    $.each(data.rows, function (index, user) {
                        var optionText = (user.userName ? user.userName : user.loginName);
                        var option = new Option(optionText, user.userId);
                        $(option).data('username', user.userName || optionText || '');
                        if (initial.fieldMaintainerId && user.userId == initial.fieldMaintainerId) {
                            option.selected = true;
                        }
                        userSelect.append(option);
                    });
                }
            }, "json");
        }
    </script>
</body>
</html>