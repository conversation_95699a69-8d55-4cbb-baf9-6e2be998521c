<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('批量配置Push接口字段')"/>
    <!--    <th:block th:include="include :: bootstrap-table-css" />-->
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-config-add">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">接口选择：</label>
                <div class="col-sm-8">
                    <select name="interfaceConfigId" id="interfaceConfigId" class="form-control" required>
                        <option value="">请选择接口</option>
                    </select>
                    <input name="interfaceConfigName" id="interfaceConfigName" type="hidden">
                </div>
            </div>
        </div>

        <!-- 字段配置表格 -->
        <div class="col-xs-12" id="fieldsTableContainer" style="display: none;">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">字段配置：</label>
                <div class="col-sm-12">
                    <table id="fieldsTable" class="table table-striped table-bordered">
                        <thead>
                        <tr>
                            <th width="200px">字段名</th>
                            <th width="300px">字段维护者</th>
                        </tr>
                        </thead>
                        <tbody id="fieldsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>

</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    /*<![CDATA[*/
    // const ctx = /*[[${#httpServletRequest.getContextPath()}]]*/ '';
    const prefix = ctx + "edi/pushFieldConfig";
    let allInterfaceFields = {};
    let allUsers = [];

    $("#form-config-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            const interfaceConfigId = $("#interfaceConfigId").val();
            const interfaceConfigName = $("#interfaceConfigName").val();
            if (!interfaceConfigId) {
                $.modal.alertWarning("请选择接口配置");
                return;
            }

            const fieldConfigs = [];
            $("#fieldsTableBody tr").each(function () {
                const fieldName = $(this).find(".field-name").text();
                const maintainerSelect = $(this).find(".maintainer-select");
                const maintainerId = maintainerSelect.val();
                const maintainerName = maintainerSelect.find("option:selected").text();

                if (maintainerId) {
                    fieldConfigs.push({
                        interfaceConfigId: interfaceConfigId,
                        interfaceConfigName: interfaceConfigName,
                        columnName: fieldName,
                        fieldMaintainerId: maintainerId,
                        fieldMaintainerName: maintainerName
                    });
                }
            });

            if (fieldConfigs.length === 0) {
                $.modal.alertWarning("请至少选择一个字段并设置维护者");
                return;
            }

            $.ajax({
                url: prefix + "/batchAdd",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(fieldConfigs),
                success: function (result) {
                    if (result.code === 0) {
                        $.modal.msgSuccess(result.msg);
                        parent.$.modal.closeTab();
                    } else {
                        $.modal.msgError(result.msg || "批量配置失败");
                    }
                },
                error: function () {
                    $.modal.msgError("批量配置失败");
                }
            });
        }
    }

    $(function () {
        initUserSelect();
        initInterfaceConfigSelect();

        $.get(ctx + "push/allInterfaceField", function (data) {
            allInterfaceFields = data;
        });

        $("#interfaceConfigId").on("change", function () {
            const interfaceConfigId = $(this).val();
            const interfaceConfigName = $(this).find("option:selected").text();
            $("#interfaceConfigName").val(interfaceConfigName);
            const selectedConfig = $(this).find("option:selected").data('config');

            if (selectedConfig && allInterfaceFields[selectedConfig.interfacePath]) {
                loadFieldsTable(selectedConfig.interfacePath, interfaceConfigId);
                $("#fieldsTableContainer").show();
            } else {
                $("#fieldsTableContainer").hide();
            }
        });
    });

    function loadFieldsTable(interfacePath) {
        const fields = allInterfaceFields[interfacePath];
        const tbody = $("#fieldsTableBody");
        tbody.empty();

        fields.forEach(function (field) {
            const row = $('<tr></tr>');
            const fieldCell = $('<td class="field-name"></td>').text(field);
            const maintainerCell = $('<td></td>').append(createMaintainerSelect());
            row.append(fieldCell, maintainerCell);
            tbody.append(row);
        });
    }

    function createMaintainerSelect() {
        const select = $('<select class="form-control maintainer-select"></select>');
        select.append('<option value="">请选择</option>');

        allUsers.forEach(function (user) {
            const optionText = user.userName;
            select.append($('<option></option>').attr('value', user.userId).text(optionText));
        });

        return select;
    }

    function initInterfaceConfigSelect() {
        $.post(ctx + "edi/pushInterfaceConfig/list", {}, function (data) {
            if (data && data.rows) {
                const interfaceSelect = $("#interfaceConfigId");
                $.each(data.rows, function (index, config) {
                    const optionText = config.interfaceName;
                    const option = new Option(optionText, config.id);
                    $(option).data('config', config);
                    interfaceSelect.append(option);
                });
            }
        }, "json");
    }

    function initUserSelect() {
        $.post(ctx + "system/user/list", {}, function (data) {
            if (data && data.rows) {
                allUsers = data.rows;
                const batchSelect = $("#batchMaintainerSelect");
                $.each(data.rows, function (index, user) {
                    const optionText = user.userName;
                    batchSelect.append($('<option></option>').attr('value', user.userId).text(optionText));
                });
            }
        }, "json");
    }

    /*]]>*/
</script>
</body>
</html>