<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('过程控制项质量数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>供应商总成零件号：</label>
                                <input type="text" name="vendorProductNo"/>
                            </li>
                            <li>
                                <label>供应商总成零件名称：</label>
                                <input type="text" name="vendorProductName"/>
                            </li>
                            <li>
                                <label>供应商总成SN码：</label>
                                <input type="text" name="vendorProductSn"/>
                            </li>
                            <li>
                                <label>供应商总成批次号：</label>
                                <input type="text" name="vendorProductBatch"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="cheryProductNo"/>
                            </li>
                            <li>
                                <label>奇瑞零件名称：</label>
                                <input type="text" name="cheryProductName"/>
                            </li>
                            <li>
                                <label>奇瑞SN码：</label>
                                <input type="text" name="cheryProductSn"/>
                            </li>
                            <li>
                                <label>生产批次号：</label>
                                <input type="text" name="productBatchNo"/>
                            </li>
                            <li>
                                <label>生产工单号：</label>
                                <input type="text" name="manufactureNo"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>车间代码：</label>
                                <input type="text" name="workshopId"/>
                            </li>
                            <li>
                                <label>车间名称：</label>
                                <input type="text" name="workshopName"/>
                            </li>
                            <li>
                                <label>产线代码：</label>
                                <input type="text" name="productionLineId"/>
                            </li>
                            <li>
                                <label>产线名称：</label>
                                <input type="text" name="productionLineName"/>
                            </li>
                            <li>
                                <label>工位代码：</label>
                                <input type="text" name="stationId"/>
                            </li>
                            <li>
                                <label>工位名称：</label>
                                <input type="text" name="stationName"/>
                            </li>
                            <li>
                                <label>工位人员编号：</label>
                                <input type="text" name="empCode"/>
                            </li>
                            <li>
                                <label>工位人员姓名：</label>
                                <input type="text" name="empName"/>
                            </li>
                            <li>
                                <label>控制项名称：</label>
                                <input type="text" name="vendorFieldName"/>
                            </li>
                            <li>
                                <label>控制项代码：</label>
                                <input type="text" name="vendorFieldCode"/>
                            </li>
                            <li>
                                <label>控制项点位：</label>
                                <input type="text" name="gatherSpot"/>
                            </li>
                            <li>
                                <label>控制项要求频率：</label>
                                <input type="text" name="samplingRate"/>
                            </li>
                            <li>
                                <label>上下限更新时间：</label>
                                <input type="text" name="limitUpdateTime"/>
                            </li>
                            <li>
                                <label>控制项描述：</label>
                                <input type="text" name="vendorFieldDesc"/>
                            </li>
                            <li>
                                <label>载体编码：</label>
                                <input type="text" name="carrierCode"/>
                            </li>
                            <li>
                                <label>投入数量：</label>
                                <input type="text" name="intputQty"/>
                            </li>
                            <li>
                                <label>一次合格数量：</label>
                                <input type="text" name="fttQty"/>
                            </li>
                            <li>
                                <label>参数 , 是传Y，否传N：</label>
                                <input type="text" name="parameter"/>
                            </li>
                            <li>
                                <label>特性 , 是传Y，否传N：</label>
                                <input type="text" name="characteristic"/>
                            </li>
                            <li>
                                <label>CC项 , 是传Y，否传N：</label>
                                <input type="text" name="cc"/>
                            </li>
                            <li>
                                <label>SC项 , 是传Y，否传N：</label>
                                <input type="text" name="sc"/>
                            </li>
                            <li>
                                <label>SPC , 是传Y，否传N：</label>
                                <input type="text" name="spc"/>
                            </li>
                            <li>
                                <label>控制项标准值：</label>
                                <input type="text" name="standardValue"/>
                            </li>
                            <li>
                                <label>控制项上限：</label>
                                <input type="text" name="upperLimit"/>
                            </li>
                            <li>
                                <label>控制项下限：</label>
                                <input type="text" name="lowerLimit"/>
                            </li>
                            <li>
                                <label>控制项实测值：</label>
                                <input type="text" name="decimalValue"/>
                            </li>
                            <li>
                                <label>控制项值的单位名称-中文：</label>
                                <input type="text" name="unitCn"/>
                            </li>
                            <li>
                                <label>控控制项单位英文：</label>
                                <input type="text" name="unitEn"/>
                            </li>
                            <li>
                                <label>检测结果：</label>
                                <input type="text" name="checkResult"/>
                            </li>
                            <li>
                                <label>在线检测：</label>
                                <input type="text" name="detectionMode"/>
                            </li>
                            <li>
                                <label>班次：</label>
                                <input type="text" name="workShift"/>
                            </li>
                            <li>
                                <label>采集时间：</label>
                                <input type="text" name="collectTime"/>
                            </li>
                            <li>
                                <label>检测方式：</label>
                                <input type="text" name="checkMode"/>
                            </li>
                            <li>
                                <label>检测设备编号：</label>
                                <input type="text" name="deviceCode"/>
                            </li>
                            <li>
                                <label>检测设备名称：</label>
                                <input type="text" name="deviceName"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:cps:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:cps:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:cps:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:cps:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:cps:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:cps:remove')}]];
        var prefix = ctx + "edi/push/cps";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "过程控制项质量数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'vendorProductNo',
                    title: '供应商总成零件号'
                },
                {
                    field: 'vendorProductName',
                    title: '供应商总成零件名称'
                },
                {
                    field: 'vendorProductSn',
                    title: '供应商总成SN码'
                },
                {
                    field: 'vendorProductBatch',
                    title: '供应商总成批次号'
                },
                {
                    field: 'cheryProductNo',
                    title: '奇瑞零件号'
                },
                {
                    field: 'cheryProductName',
                    title: '奇瑞零件名称'
                },
                {
                    field: 'cheryProductSn',
                    title: '奇瑞SN码'
                },
                {
                    field: 'productBatchNo',
                    title: '生产批次号'
                },
                {
                    field: 'manufactureNo',
                    title: '生产工单号'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'workshopId',
                    title: '车间代码'
                },
                {
                    field: 'workshopName',
                    title: '车间名称'
                },
                {
                    field: 'productionLineId',
                    title: '产线代码'
                },
                {
                    field: 'productionLineName',
                    title: '产线名称'
                },
                {
                    field: 'stationId',
                    title: '工位代码'
                },
                {
                    field: 'stationName',
                    title: '工位名称'
                },
                {
                    field: 'empCode',
                    title: '工位人员编号'
                },
                {
                    field: 'empName',
                    title: '工位人员姓名'
                },
                {
                    field: 'vendorFieldName',
                    title: '控制项名称'
                },
                {
                    field: 'vendorFieldCode',
                    title: '控制项代码'
                },
                {
                    field: 'gatherSpot',
                    title: '控制项点位'
                },
                {
                    field: 'samplingRate',
                    title: '控制项要求频率'
                },
                {
                    field: 'limitUpdateTime',
                    title: '上下限更新时间'
                },
                {
                    field: 'vendorFieldDesc',
                    title: '控制项描述'
                },
                {
                    field: 'carrierCode',
                    title: '载体编码'
                },
                {
                    field: 'intputQty',
                    title: '投入数量'
                },
                {
                    field: 'fttQty',
                    title: '一次合格数量'
                },
                {
                    field: 'parameter',
                    title: '参数 , 是传Y，否传N'
                },
                {
                    field: 'characteristic',
                    title: '特性 , 是传Y，否传N'
                },
                {
                    field: 'cc',
                    title: 'CC项 , 是传Y，否传N'
                },
                {
                    field: 'sc',
                    title: 'SC项 , 是传Y，否传N'
                },
                {
                    field: 'spc',
                    title: 'SPC , 是传Y，否传N'
                },
                {
                    field: 'standardValue',
                    title: '控制项标准值'
                },
                {
                    field: 'upperLimit',
                    title: '控制项上限'
                },
                {
                    field: 'lowerLimit',
                    title: '控制项下限'
                },
                {
                    field: 'decimalValue',
                    title: '控制项实测值'
                },
                {
                    field: 'unitCn',
                    title: '控制项值的单位名称-中文'
                },
                {
                    field: 'unitEn',
                    title: '控控制项单位英文'
                },
                {
                    field: 'checkResult',
                    title: '检测结果'
                },
                {
                    field: 'detectionMode',
                    title: '在线检测'
                },
                {
                    field: 'workShift',
                    title: '班次'
                },
                {
                    field: 'collectTime',
                    title: '采集时间'
                },
                {
                    field: 'checkMode',
                    title: '检测方式'
                },
                {
                    field: 'deviceCode',
                    title: '检测设备编号'
                },
                {
                    field: 'deviceName',
                    title: '检测设备名称'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>