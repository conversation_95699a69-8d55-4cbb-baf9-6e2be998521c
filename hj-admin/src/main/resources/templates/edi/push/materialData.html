<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('物料主数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>供应商总成零件号：</label>
                                <input type="text" name="vendorProductNo"/>
                            </li>
                            <li>
                                <label>供应商总成零件名称：</label>
                                <input type="text" name="vendorProductName"/>
                            </li>
                            <li>
                                <label>供应商零件版本号：</label>
                                <input type="text" name="vendorHardwareRevision"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="cheryProductNo"/>
                            </li>
                            <li>
                                <label>奇瑞零件名称：</label>
                                <input type="text" name="cheryProductName"/>
                            </li>
                            <li>
                                <label>奇瑞硬件版本号：</label>
                                <input type="text" name="oemHardwareRevision"/>
                            </li>
                            <li>
                                <label>奇瑞软件版本号：</label>
                                <input type="text" name="oemSoftwareRevision"/>
                            </li>
                            <li>
                                <label>车型：</label>
                                <input type="text" name="oemModel"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="oemProjectName"/>
                            </li>
                            <li>
                                <label>是否SOP：</label>
                                <input type="text" name="launched"/>
                            </li>
                            <li>
                                <label>数据同步执行时间：</label>
                                <input type="text" name="dateTime"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>芯片MPN标识码：</label>
                                <input type="text" name="mpnCode"/>
                            </li>
                            <li>
                                <label>芯片MPN标识名称：</label>
                                <input type="text" name="mpnName"/>
                            </li>
                            <li>
                                <label>物料有效期：</label>
                                <input type="text" name="validDays"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:materialData:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:materialData:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:materialData:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:materialData:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:materialData:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:materialData:remove')}]];
        var prefix = ctx + "edi/push/materialData";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "物料主数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'vendorProductNo',
                    title: '供应商总成零件号'
                },
                {
                    field: 'vendorProductName',
                    title: '供应商总成零件名称'
                },
                {
                    field: 'type',
                    title: '类型(成品,半成品,原材料)'
                },
                {
                    field: 'vendorHardwareRevision',
                    title: '供应商零件版本号'
                },
                {
                    field: 'cheryProductNo',
                    title: '奇瑞零件号'
                },
                {
                    field: 'cheryProductName',
                    title: '奇瑞零件名称'
                },
                {
                    field: 'oemHardwareRevision',
                    title: '奇瑞硬件版本号'
                },
                {
                    field: 'oemSoftwareRevision',
                    title: '奇瑞软件版本号'
                },
                {
                    field: 'oemModel',
                    title: '车型'
                },
                {
                    field: 'oemProjectName',
                    title: '项目名称'
                },
                {
                    field: 'launched',
                    title: '是否SOP'
                },
                {
                    field: 'dateTime',
                    title: '数据同步执行时间'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'procurementType',
                    title: '芯片采购类型(AVAP,CS,CMcontro)'
                },
                {
                    field: 'mpnCode',
                    title: '芯片MPN标识码'
                },
                {
                    field: 'mpnName',
                    title: '芯片MPN标识名称'
                },
                {
                    field: 'validDays',
                    title: '物料有效期'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>