<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('M+6月物料需求计划风险确认列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>需求发布版次：</label>
                                <input type="text" name="releaseEdition"/>
                            </li>
                            <li>
                                <label>零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>反馈结果：</label>
                                <input type="text" name="feedbackResults"/>
                            </li>
                            <li>
                                <label>具体风险：</label>
                                <input type="text" name="ventureSpecific"/>
                            </li>
                            <li>
                                <label>应对措施：</label>
                                <input type="text" name="measures"/>
                            </li>
                            <li>
                                <label>起始月份：</label>
                                <input type="text" name="startMonth"/>
                            </li>
                            <li>
                                <label>满足数量1：</label>
                                <input type="text" name="quantityMeet1"/>
                            </li>
                            <li>
                                <label>满足数量2：</label>
                                <input type="text" name="quantityMeet2"/>
                            </li>
                            <li>
                                <label>满足数量3：</label>
                                <input type="text" name="quantityMeet3"/>
                            </li>
                            <li>
                                <label>满足数量4：</label>
                                <input type="text" name="quantityMeet4"/>
                            </li>
                            <li>
                                <label>满足数量5：</label>
                                <input type="text" name="quantityMeet5"/>
                            </li>
                            <li>
                                <label>满足数量6：</label>
                                <input type="text" name="quantityMeet6"/>
                            </li>
                            <li>
                                <label>满足数量7：</label>
                                <input type="text" name="quantityMeet7"/>
                            </li>
                            <li>
                                <label>满足数量8：</label>
                                <input type="text" name="quantityMeet8"/>
                            </li>
                            <li>
                                <label>满足数量9：</label>
                                <input type="text" name="quantityMeet9"/>
                            </li>
                            <li>
                                <label>满足数量10：</label>
                                <input type="text" name="quantityMeet10"/>
                            </li>
                            <li>
                                <label>满足数量11：</label>
                                <input type="text" name="quantityMeet11"/>
                            </li>
                            <li>
                                <label>满足数量12：</label>
                                <input type="text" name="quantityMeet12"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:conMmrp:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:conMmrp:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:conMmrp:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:conMmrp:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:conMmrp:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:conMmrp:remove')}]];
        var prefix = ctx + "edi/push/conMmrp";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "M+6月物料需求计划风险确认",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'releaseEdition',
                    title: '需求发布版次'
                },
                {
                    field: 'materialCode',
                    title: '零件号'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'feedbackResults',
                    title: '反馈结果'
                },
                {
                    field: 'ventureType',
                    title: '风险类型 , 当反馈结果=1时，此字段必输 1. 生产节拍不足 2. 人员不足 3. 原材料不足 4. 设备异常 5. 其他'
                },
                {
                    field: 'ventureSpecific',
                    title: '具体风险'
                },
                {
                    field: 'measures',
                    title: '应对措施'
                },
                {
                    field: 'startMonth',
                    title: '起始月份'
                },
                {
                    field: 'quantityMeet1',
                    title: '满足数量1'
                },
                {
                    field: 'quantityMeet2',
                    title: '满足数量2'
                },
                {
                    field: 'quantityMeet3',
                    title: '满足数量3'
                },
                {
                    field: 'quantityMeet4',
                    title: '满足数量4'
                },
                {
                    field: 'quantityMeet5',
                    title: '满足数量5'
                },
                {
                    field: 'quantityMeet6',
                    title: '满足数量6'
                },
                {
                    field: 'quantityMeet7',
                    title: '满足数量7'
                },
                {
                    field: 'quantityMeet8',
                    title: '满足数量8'
                },
                {
                    field: 'quantityMeet9',
                    title: '满足数量9'
                },
                {
                    field: 'quantityMeet10',
                    title: '满足数量10'
                },
                {
                    field: 'quantityMeet11',
                    title: '满足数量11'
                },
                {
                    field: 'quantityMeet12',
                    title: '满足数量12'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>