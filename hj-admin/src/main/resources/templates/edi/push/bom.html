<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('BOM主数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>BOM编码：</label>
                                <input type="text" name="bomCode"/>
                            </li>
                            <li>
                                <label>BOM名称：</label>
                                <input type="text" name="bomName"/>
                            </li>
                            <li>
                                <label>BOM版本：</label>
                                <input type="text" name="bomVersion"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="cheryProductNo"/>
                            </li>
                            <li>
                                <label>奇瑞零件名称：</label>
                                <input type="text" name="cheryProductName"/>
                            </li>
                            <li>
                                <label>供应商父件编码：</label>
                                <input type="text" name="vendorProductNo"/>
                            </li>
                            <li>
                                <label>供应商父件名称：</label>
                                <input type="text" name="vendorProductName"/>
                            </li>
                            <li>
                                <label>父件单位：</label>
                                <input type="text" name="materialUnit"/>
                            </li>
                            <li>
                                <label>子件编码：</label>
                                <input type="text" name="subMaterialCode"/>
                            </li>
                            <li>
                                <label>子件名称：</label>
                                <input type="text" name="subMaterialName"/>
                            </li>
                            <li>
                                <label>子件单位：</label>
                                <input type="text" name="subMaterialUnit"/>
                            </li>
                            <li>
                                <label>子件用量：</label>
                                <input type="text" name="subMaterialQuota"/>
                            </li>
                            <li>
                                <label>BOM变更时间：</label>
                                <input type="text" name="dataUpdateTime"/>
                            </li>
                            <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:bom:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:bom:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:bom:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:bom:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:bom:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:bom:remove')}]];
        var prefix = ctx + "edi/push/bom";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "BOM主数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'bomCode',
                    title: 'BOM编码'
                },
                {
                    field: 'bomName',
                    title: 'BOM名称'
                },
                {
                    field: 'bomVersion',
                    title: 'BOM版本'
                },
                {
                    field: 'cheryProductNo',
                    title: '奇瑞零件号'
                },
                {
                    field: 'cheryProductName',
                    title: '奇瑞零件名称'
                },
                {
                    field: 'vendorProductNo',
                    title: '供应商父件编码'
                },
                {
                    field: 'vendorProductName',
                    title: '供应商父件名称'
                },
                {
                    field: 'vendorProductType',
                    title: '父件类型(成品, 半成品)'
                },
                {
                    field: 'materialUnit',
                    title: '父件单位'
                },
                {
                    field: 'subMaterialCode',
                    title: '子件编码'
                },
                {
                    field: 'subMaterialName',
                    title: '子件名称'
                },
                {
                    field: 'subMaterialType',
                    title: '子件类型(半成品, 原材料)'
                },
                {
                    field: 'subMaterialUnit',
                    title: '子件单位'
                },
                {
                    field: 'subMaterialQuota',
                    title: '子件用量'
                },
                {
                    field: 'dataUpdateTime',
                    title: 'BOM变更时间'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                    // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>