<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('生产过程数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>车间代码：</label>
                                <input type="text" name="workshopId"/>
                            </li>
                            <li>
                                <label>车间名称：</label>
                                <input type="text" name="workshopName"/>
                            </li>
                            <li>
                                <label>产线代码：</label>
                                <input type="text" name="productionLineId"/>
                            </li>
                            <li>
                                <label>产线名称：</label>
                                <input type="text" name="productionLineName"/>
                            </li>
                            <li>
                                <label>工位代码：</label>
                                <input type="text" name="stationId"/>
                            </li>
                            <li>
                                <label>工位名称：</label>
                                <input type="text" name="stationName"/>
                            </li>
                            <li>
                                <label>工位人员编号：</label>
                                <input type="text" name="empCode"/>
                            </li>
                            <li>
                                <label>工位人员姓名：</label>
                                <input type="text" name="empName"/>
                            </li>
                            <li>
                                <label>供应商总成零件名称：</label>
                                <input type="text" name="vendorProductName"/>
                            </li>
                            <li>
                                <label>供应商总成零件号：</label>
                                <input type="text" name="vendorProductNo"/>
                            </li>
                            <li>
                                <label>供应商总成批次号：</label>
                                <input type="text" name="vendorProductBatch"/>
                            </li>
                            <li>
                                <label>供应商总成SN码：</label>
                                <input type="text" name="vendorProductSn"/>
                            </li>
                            <li>
                                <label>子件编码：</label>
                                <input type="text" name="subProdNo"/>
                            </li>
                            <li>
                                <label>子件名称：</label>
                                <input type="text" name="subProdName"/>
                            </li>
                            <li>
                                <label>子件批次号：</label>
                                <input type="text" name="subBatchNo"/>
                            </li>
                            <li>
                                <label>子件分包号：</label>
                                <input type="text" name="childPackageInfo"/>
                            </li>
                            <li>
                                <label>子件扣料数量：</label>
                                <input type="text" name="subProdNum"/>
                            </li>
                            <li>
                                <label>子件SN码：</label>
                                <input type="text" name="subProdSn"/>
                            </li>
                            <li>
                                <label>子件物料来源：</label>
                                <input type="text" name="childSource"/>
                            </li>
                            <li>
                                <label>分供方代码：</label>
                                <input type="text" name="subSupplierCode"/>
                            </li>
                            <li>
                                <label>分供方名称：</label>
                                <input type="text" name="subSupplierName"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="cheryProductNo"/>
                            </li>
                            <li>
                                <label>奇瑞零件名称：</label>
                                <input type="text" name="cheryProductName"/>
                            </li>
                            <li>
                                <label>奇瑞SN码：</label>
                                <input type="text" name="cheryProductSn"/>
                            </li>
                            <li>
                                <label>生产工单号：</label>
                                <input type="text" name="manufactureNo"/>
                            </li>
                            <li>
                                <label>生产批次号：</label>
                                <input type="text" name="productBatchNo"/>
                            </li>
                            <li>
                                <label>班次：</label>
                                <input type="text" name="workShift"/>
                            </li>
                            <li>
                                <label>进工位的时间：</label>
                                <input type="text" name="materialInputTime"/>
                            </li>
                            <li>
                                <label>出工位的时间：</label>
                                <input type="text" name="materialOutputTime"/>
                            </li>
                            <li>
                                <label>装配设备编号：</label>
                                <input type="text" name="vendorFieldNum"/>
                            </li>
                            <li>
                                <label>装配设备名称：</label>
                                <input type="text" name="vendorFieldName"/>
                            </li>
                            <li>
                                <label>采集时间：</label>
                                <input type="text" name="collectTime"/>
                            </li>
                            <li>
                                <label>子件绑定扫码时间：</label>
                                <input type="text" name="dateTime"/>
                            </li>
                            <li>
                                <label>父件硬件版本号：</label>
                                <input type="text" name="parentHardwareRevision"/>
                            </li>
                            <li>
                                <label>父件软件版本号：</label>
                                <input type="text" name="parentSoftwareRevision"/>
                            </li>
                            <li>
                                <label>子件硬件版本号：</label>
                                <input type="text" name="childHardwareRevision"/>
                            </li>
                            <li>
                                <label>子件软件版本号：</label>
                                <input type="text" name="childSoftwareRevision"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:proData:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:proData:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:proData:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:proData:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:proData:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:proData:remove')}]];
        var prefix = ctx + "edi/push/proData";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "生产过程数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'workshopId',
                    title: '车间代码'
                },
                {
                    field: 'workshopName',
                    title: '车间名称'
                },
                {
                    field: 'productionLineId',
                    title: '产线代码'
                },
                {
                    field: 'productionLineName',
                    title: '产线名称'
                },
                {
                    field: 'stationId',
                    title: '工位代码'
                },
                {
                    field: 'stationName',
                    title: '工位名称'
                },
                {
                    field: 'empCode',
                    title: '工位人员编号'
                },
                {
                    field: 'empName',
                    title: '工位人员姓名'
                },
                {
                    field: 'vendorProductName',
                    title: '供应商总成零件名称'
                },
                {
                    field: 'vendorProductNo',
                    title: '供应商总成零件号'
                },
                {
                    field: 'vendorProductBatch',
                    title: '供应商总成批次号'
                },
                {
                    field: 'vendorProductSn',
                    title: '供应商总成SN码'
                },
                {
                    field: 'subProdNo',
                    title: '子件编码'
                },
                {
                    field: 'subProdName',
                    title: '子件名称'
                },
                {
                    field: 'subBatchNo',
                    title: '子件批次号'
                },
                {
                    field: 'childPackageInfo',
                    title: '子件分包号'
                },
                {
                    field: 'subProdNum',
                    title: '子件扣料数量'
                },
                {
                    field: 'subProdSn',
                    title: '子件SN码'
                },
                {
                    field: 'childSource',
                    title: '子件物料来源'
                },
                {
                    field: 'subSupplierCode',
                    title: '分供方代码'
                },
                {
                    field: 'subSupplierName',
                    title: '分供方名称'
                },
                {
                    field: 'cheryProductNo',
                    title: '奇瑞零件号'
                },
                {
                    field: 'cheryProductName',
                    title: '奇瑞零件名称'
                },
                {
                    field: 'cheryProductSn',
                    title: '奇瑞SN码'
                },
                {
                    field: 'manufactureNo',
                    title: '生产工单号'
                },
                {
                    field: 'productBatchNo',
                    title: '生产批次号'
                },
                {
                    field: 'workShift',
                    title: '班次'
                },
                {
                    field: 'materialInputTime',
                    title: '进工位的时间'
                },
                {
                    field: 'materialOutputTime',
                    title: '出工位的时间'
                },
                {
                    field: 'vendorFieldNum',
                    title: '装配设备编号'
                },
                {
                    field: 'vendorFieldName',
                    title: '装配设备名称'
                },
                {
                    field: 'instrumentQualityStatus',
                    title: '设备判定的质量状态 , 合格与否，NG不合适 OK合适'
                },
                {
                    field: 'manualQualityStatus',
                    title: '人工判定的质量状态, 合格与否，NG不合适 OK合适'
                },
                {
                    field: 'finalQualityStatus',
                    title: '最终质量状态, 合格与否，NG不合适 OK合适'
                },
                {
                    field: 'collectTime',
                    title: '采集时间'
                },
                {
                    field: 'dateTime',
                    title: '子件绑定扫码时间'
                },
                {
                    field: 'parentHardwareRevision',
                    title: '父件硬件版本号'
                },
                {
                    field: 'parentSoftwareRevision',
                    title: '父件软件版本号'
                },
                {
                    field: 'childHardwareRevision',
                    title: '子件硬件版本号'
                },
                {
                    field: 'childSoftwareRevision',
                    title: '子件软件版本号'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>