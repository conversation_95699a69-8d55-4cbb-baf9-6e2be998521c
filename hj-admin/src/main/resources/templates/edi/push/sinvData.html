<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('供应商共享库存列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>零件名称：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>当前库存数量：</label>
                                <input type="text" name="quantityCurrent"/>
                            </li>
                            <li>
                                <label>原材料在途数量：</label>
                                <input type="text" name="quantityPlan"/>
                            </li>
                            <li>
                                <label>安全库存：</label>
                                <input type="text" name="safetyStock"/>
                            </li>
                            <li>
                                <label>生产/采购周期:成品即半成品为生产周期：</label>
                                <input type="text" name="productionCycle"/>
                            </li>
                            <li>
                                <label>库存更新时间：</label>
                                <input type="text" name="dataUpdateTime"/>
                            </li>
                            <li>
                                <label>批次：</label>
                                <input type="text" name="supplierBatch"/>
                            </li>
                            <li>
                                <label>有效期截止日期：</label>
                                <input type="text" name="supplieryxqDate"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="sinv:data:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="sinv:data:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="sinv:data:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="sinv:data:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('sinv:data:edit')}]];
        var removeFlag = [[${@permission.hasPermi('sinv:data:remove')}]];
        var prefix = ctx + "edi/push/sinvData";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "供应商共享库存",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'materialCode',
                    title: '零件号'
                },
                {
                    field: 'materialDescription',
                    title: '零件名称'
                },
                {
                    field: 'materialType',
                    title: '物料类型(成品,半成品,原材料)'
                },
                {
                    field: 'quantityCurrent',
                    title: '当前库存数量'
                },
                {
                    field: 'quantityPlan',
                    title: '原材料在途数量'
                },
                {
                    field: 'inventoryStatus',
                    title: '库存状态(生产件,呆滞件,备件,KD件)'
                },
                {
                    field: 'safetyStock',
                    title: '安全库存'
                },
                {
                    field: 'productionCycle',
                    title: '生产/采购周期:成品即半成品为生产周期'
                },
                {
                    field: 'dataUpdateTime',
                    title: '库存更新时间'
                },
                {
                    field: 'supplierBatch',
                    title: '批次'
                },
                {
                    field: 'supplieryxqDate',
                    title: '有效期截止日期'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>