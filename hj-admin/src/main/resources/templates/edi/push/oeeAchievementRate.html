<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('设备OEE达成率列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>车间代码：</label>
                                <input type="text" name="workshopId"/>
                            </li>
                            <li>
                                <label>车间名称：</label>
                                <input type="text" name="workshopName"/>
                            </li>
                            <li>
                                <label>产线代码：</label>
                                <input type="text" name="productionLineId"/>
                            </li>
                            <li>
                                <label>产线名称：</label>
                                <input type="text" name="productionLineName"/>
                            </li>
                            <li>
                                <label>工位代码：</label>
                                <input type="text" name="stationId"/>
                            </li>
                            <li>
                                <label>工位名称：</label>
                                <input type="text" name="stationName"/>
                            </li>
                            <li>
                                <label>设备代码：</label>
                                <input type="text" name="deviceId"/>
                            </li>
                            <li>
                                <label>设备名称：</label>
                                <input type="text" name="deviceName"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="cheryProductNo"/>
                            </li>
                            <li>
                                <label>奇瑞零件名称：</label>
                                <input type="text" name="cheryProductName"/>
                            </li>
                            <li>
                                <label>供应商总成零件号：</label>
                                <input type="text" name="vendorProductNo"/>
                            </li>
                            <li>
                                <label>供应商总成零件名称：</label>
                                <input type="text" name="vendorProductName"/>
                            </li>
                            <li>
                                <label>生产批次号：</label>
                                <input type="text" name="productBatchNo"/>
                            </li>
                            <li>
                                <label>生产工单号：</label>
                                <input type="text" name="manufactureNo"/>
                            </li>
                            <li>
                                <label>OEE实际值：</label>
                                <input type="text" name="rate"/>
                            </li>
                            <li>
                                <label>OEE目标值：</label>
                                <input type="text" name="rateTagValue"/>
                            </li>
                            <li>
                                <label>班次：</label>
                                <input type="text" name="workShift"/>
                            </li>
                            <li>
                                <label>生产日期：</label>
                                <input type="text" name="statisticalTime"/>
                            </li>
                            <li>
                                <label>值统计时间：</label>
                                <input type="text" name="dateTime"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:oeeAchievementRate:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:oeeAchievementRate:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:oeeAchievementRate:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:oeeAchievementRate:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:oeeAchievementRate:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:oeeAchievementRate:remove')}]];
        var prefix = ctx + "edi/push/oeeAchievementRate";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "设备OEE达成率",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'workshopId',
                    title: '车间代码'
                },
                {
                    field: 'workshopName',
                    title: '车间名称'
                },
                {
                    field: 'productionLineId',
                    title: '产线代码'
                },
                {
                    field: 'productionLineName',
                    title: '产线名称'
                },
                {
                    field: 'stationId',
                    title: '工位代码'
                },
                {
                    field: 'stationName',
                    title: '工位名称'
                },
                {
                    field: 'deviceId',
                    title: '设备代码'
                },
                {
                    field: 'deviceName',
                    title: '设备名称'
                },
                {
                    field: 'cheryProductNo',
                    title: '奇瑞零件号'
                },
                {
                    field: 'cheryProductName',
                    title: '奇瑞零件名称'
                },
                {
                    field: 'vendorProductNo',
                    title: '供应商总成零件号'
                },
                {
                    field: 'vendorProductName',
                    title: '供应商总成零件名称'
                },
                {
                    field: 'productBatchNo',
                    title: '生产批次号'
                },
                {
                    field: 'manufactureNo',
                    title: '生产工单号'
                },
                {
                    field: 'rate',
                    title: 'OEE实际值'
                },
                {
                    field: 'rateTagValue',
                    title: 'OEE目标值'
                },
                {
                    field: 'workShift',
                    title: '班次'
                },
                {
                    field: 'statisticalTime',
                    title: '生产日期'
                },
                {
                    field: 'dateTime',
                    title: '值统计时间'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>