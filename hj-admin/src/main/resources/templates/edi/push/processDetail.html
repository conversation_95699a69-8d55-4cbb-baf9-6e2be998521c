<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('工艺详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-process-detail">
            <input name="id" th:field="*{supplierProProcess.id}" type="hidden">
            
            <!-- 主数据信息 -->
            <div class="form-group">
                <h4 class="form-title">基本信息</h4>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">供应商代码：</label>
                        <div class="col-sm-8">
                            <input name="supplierCode" th:field="*{supplierProProcess.supplierCode}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">供应商名称：</label>
                        <div class="col-sm-8">
                            <input name="supplierName" th:field="*{supplierProProcess.supplierName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">奇瑞零件号：</label>
                        <div class="col-sm-8">
                            <input name="cheryProductNo" th:field="*{supplierProProcess.cheryProductNo}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">奇瑞零件名称：</label>
                        <div class="col-sm-8">
                            <input name="cheryProductName" th:field="*{supplierProProcess.cheryProductName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">供应商总成零件号：</label>
                        <div class="col-sm-8">
                            <input name="vendorProductNo" th:field="*{supplierProProcess.vendorProductNo}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">供应商总成零件名称：</label>
                        <div class="col-sm-8">
                            <input name="vendorProductName" th:field="*{supplierProProcess.vendorProductName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">工艺编码：</label>
                        <div class="col-sm-8">
                            <input name="techCode" th:field="*{supplierProProcess.techCode}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">工艺名称：</label>
                        <div class="col-sm-8">
                            <input name="techName" th:field="*{supplierProProcess.techName}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">工艺版本：</label>
                        <div class="col-sm-8">
                            <input name="techVersion" th:field="*{supplierProProcess.techVersion}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">是否启用：</label>
                        <div class="col-sm-8">
                            <input name="isEnabled" th:field="*{supplierProProcess.isEnabled}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">最大加工能力：</label>
                        <div class="col-sm-8">
                            <input name="maxProcessingCapacity" th:field="*{supplierProProcess.maxProcessingCapacity}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">承诺比例：</label>
                        <div class="col-sm-8">
                            <input name="promiseRatio" th:field="*{supplierProProcess.promiseRatio}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <label class="col-sm-4 control-label">供货周期：</label>
                        <div class="col-sm-8">
                            <input name="makeSamePeriod" th:field="*{supplierProProcess.makeSamePeriod}" class="form-control" type="text" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 子级数据 -->
            <div class="form-group">
                <h4 class="form-title">工序信息</h4>
                <div class="col-sm-12 select-table table-striped">
                    <table id="childTable"></table>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "edi/process";
    var processData = /*[[${supplierProProcess}]]*/ [];
        
        $("#form-process-detail").validate({
            focusCleanup: true
        });

        $(function() {
            var options = {
                data: processData.childList || [],
                pagination: false,
                search: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    field: 'processCode',
                    title: '工序编码'
                }, {
                    field: 'processName', 
                    title: '工序名称'
                }, {
                    field: 'processOrder',
                    title: '工序顺序'
                }, {
                    field: 'processType',
                    title: '工序类型'
                }, {
                    field: 'processCycleTime',
                    title: '工序周期时间'
                }, {
                    field: 'processYieldTarget',
                    title: '工序良率目标'
                }, {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showGrandChildDetail(' + index + ')"><i class="fa fa-eye"></i>查看工步</a>');
                        return actions.join('');
                    }
                }]
            };
            $('#childTable').bootstrapTable(options);
        });

        function showGrandChildDetail(index) {
            var childData = processData.childList[index];
            if (!childData.subList || childData.subList.length === 0) {
                $.modal.alertWarning("该工序暂无工步信息");
                return;
            }
            
            var grandChildHtml = '<div class="table-responsive"><table class="table table-striped table-bordered">';
            grandChildHtml += '<thead><tr><th>工步编码</th><th>工步名称</th><th>工步顺序</th><th>工步类型</th><th>工步周期时间</th><th>工步良率目标</th></tr></thead>';
            grandChildHtml += '<tbody>';
            
            for (var i = 0; i < childData.subList.length; i++) {
                var grandChild = childData.subList[i];
                grandChildHtml += '<tr>';
                grandChildHtml += '<td>' + (grandChild.stepCode || '') + '</td>';
                grandChildHtml += '<td>' + (grandChild.stepName || '') + '</td>';
                grandChildHtml += '<td>' + (grandChild.stepOrder || '') + '</td>';
                grandChildHtml += '<td>' + (grandChild.stepType || '') + '</td>';
                grandChildHtml += '<td>' + (grandChild.stepCycleTime || '') + '</td>';
                grandChildHtml += '<td>' + (grandChild.stepYieldTarget || '') + '</td>';
                grandChildHtml += '</tr>';
            }
            
            grandChildHtml += '</tbody></table></div>';
            
            $.modal.open("工步详情 - " + childData.processName, grandChildHtml, "800", "600");
        }
    </script>
</body>
</html>
