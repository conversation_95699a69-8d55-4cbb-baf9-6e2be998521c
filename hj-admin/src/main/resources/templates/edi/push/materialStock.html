<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('来料检验数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>子件编码：</label>
                                <input type="text" name="supplierSubCode"/>
                            </li>
                            <li>
                                <label>子件名称：</label>
                                <input type="text" name="supplierSubName"/>
                            </li>
                            <li>
                                <label>分供方代码：</label>
                                <input type="text" name="subSupplierCode"/>
                            </li>
                            <li>
                                <label>分供方名称：</label>
                                <input type="text" name="subSupplierName"/>
                            </li>
                            <li>
                                <label>分供方地址：</label>
                                <input type="text" name="subSupplierAddress"/>
                            </li>
                            <li>
                                <label>分供方子件编码：</label>
                                <input type="text" name="componentCode"/>
                            </li>
                            <li>
                                <label>分供方子件名称：</label>
                                <input type="text" name="componentName"/>
                            </li>
                            <li>
                                <label>子件批次号：</label>
                                <input type="text" name="subBatchNo"/>
                            </li>
                            <li>
                                <label>子件批次数量：</label>
                                <input type="text" name="subBatchNum"/>
                            </li>
                            <li>
                                <label>子件SN码：</label>
                                <input type="text" name="subBatchSn"/>
                            </li>
                            <li>
                                <label>检验人员编号：</label>
                                <input type="text" name="empCode"/>
                            </li>
                            <li>
                                <label>检验人员姓名：</label>
                                <input type="text" name="empName"/>
                            </li>
                            <li>
                                <label>检测设备编号：</label>
                                <input type="text" name="deviceCode"/>
                            </li>
                            <li>
                                <label>检测设备名称：</label>
                                <input type="text" name="deviceName"/>
                            </li>
                            <li>
                                <label>参数名称/特性名称：</label>
                                <input type="text" name="featureName"/>
                            </li>
                            <li>
                                <label>参数单位/特性单位：</label>
                                <input type="text" name="featureUnit"/>
                            </li>
                            <li>
                                <label>参数/特性标准值：</label>
                                <input type="text" name="standardValue"/>
                            </li>
                            <li>
                                <label>参数/特性上限值：</label>
                                <input type="text" name="featureUpper"/>
                            </li>
                            <li>
                                <label>参数/特性下限值：</label>
                                <input type="text" name="featureLower"/>
                            </li>
                            <li>
                                <label>参数/特性实测值：</label>
                                <input type="text" name="featureValue"/>
                            </li>
                            <li>
                                <label>来料检验单号：</label>
                                <input type="text" name="checkNo"/>
                            </li>
                            <li>
                                <label>来料检验结果：</label>
                                <input type="text" name="checkResult"/>
                            </li>
                            <li>
                                <label>检验时间：</label>
                                <input type="text" name="checkTime"/>
                            </li>
                            <li>
                                <label>控制项要求频率：</label>
                                <input type="text" name="samplingRate"/>
                            </li>
                            <li>
                                <label>上下限更新时间：</label>
                                <input type="text" name="limitUpdateTime"/>
                            </li>
                            <li>
                                <label>控制项描述：</label>
                                <input type="text" name="vendorFieldDesc"/>
                            </li>
                            <li>
                                <label>控制项代码：</label>
                                <input type="text" name="vendorFieldCode"/>
                            </li>
                            <li>
                                <label>库存有效日期：</label>
                                <input type="text" name="deadLine"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:materialStock:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:materialStock:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:materialStock:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:materialStock:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:materialStock:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:materialStock:remove')}]];
        var prefix = ctx + "edi/push/materialStock";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "来料检验数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'supplierSubCode',
                    title: '子件编码'
                },
                {
                    field: 'supplierSubName',
                    title: '子件名称'
                },
                {
                    field: 'subSupplierCode',
                    title: '分供方代码'
                },
                {
                    field: 'subSupplierName',
                    title: '分供方名称'
                },
                {
                    field: 'subSupplierAddress',
                    title: '分供方地址'
                },
                {
                    field: 'componentCode',
                    title: '分供方子件编码'
                },
                {
                    field: 'componentName',
                    title: '分供方子件名称'
                },
                {
                    field: 'subBatchNo',
                    title: '子件批次号'
                },
                {
                    field: 'subBatchNum',
                    title: '子件批次数量'
                },
                {
                    field: 'subBatchSn',
                    title: '子件SN码'
                },
                {
                    field: 'empCode',
                    title: '检验人员编号'
                },
                {
                    field: 'empName',
                    title: '检验人员姓名'
                },
                {
                    field: 'deviceCode',
                    title: '检测设备编号'
                },
                {
                    field: 'deviceName',
                    title: '检测设备名称'
                },
                {
                    field: 'featureName',
                    title: '参数名称/特性名称'
                },
                {
                    field: 'featureUnit',
                    title: '参数单位/特性单位'
                },
                {
                    field: 'standardValue',
                    title: '参数/特性标准值'
                },
                {
                    field: 'featureUpper',
                    title: '参数/特性上限值'
                },
                {
                    field: 'featureLower',
                    title: '参数/特性下限值'
                },
                {
                    field: 'featureValue',
                    title: '参数/特性实测值'
                },
                {
                    field: 'checkNo',
                    title: '来料检验单号'
                },
                {
                    field: 'checkResult',
                    title: '来料检验结果'
                },
                {
                    field: 'checkTime',
                    title: '检验时间'
                },
                {
                    field: 'samplingRate',
                    title: '控制项要求频率'
                },
                {
                    field: 'limitUpdateTime',
                    title: '上下限更新时间'
                },
                {
                    field: 'vendorFieldDesc',
                    title: '控制项描述'
                },
                {
                    field: 'vendorFieldCode',
                    title: '控制项代码'
                },
                {
                    field: 'deadLine',
                    title: '库存有效日期'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>