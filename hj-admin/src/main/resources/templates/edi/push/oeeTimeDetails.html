<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('OEE时间明细列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>记录ID：</label>
                                <input type="text" name="recId"/>
                            </li>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>车间代码：</label>
                                <input type="text" name="workshopId"/>
                            </li>
                            <li>
                                <label>车间名称：</label>
                                <input type="text" name="workshopName"/>
                            </li>
                            <li>
                                <label>产线代码：</label>
                                <input type="text" name="productionLineId"/>
                            </li>
                            <li>
                                <label>产线名称：</label>
                                <input type="text" name="productionLineName"/>
                            </li>
                            <li>
                                <label>工位代码：</label>
                                <input type="text" name="stationId"/>
                            </li>
                            <li>
                                <label>工位名称：</label>
                                <input type="text" name="stationName"/>
                            </li>
                            <li>
                                <label>工艺装备代码：</label>
                                <input type="text" name="deviceId"/>
                            </li>
                            <li>
                                <label>工艺装备名称：</label>
                                <input type="text" name="deviceName"/>
                            </li>
                            <li>
                                <label>小类描述：</label>
                                <input type="text" name="subTypeDescription"/>
                            </li>
                            <li>
                                <label>计划开始时间：</label>
                                <input type="text" name="planBeginTime"/>
                            </li>
                            <li>
                                <label>计划结束时间：</label>
                                <input type="text" name="planEndTime"/>
                            </li>
                            <li>
                                <label>实际开始时间：</label>
                                <input type="text" name="startTime"/>
                            </li>
                            <li>
                                <label>实际结束时间：</label>
                                <input type="text" name="endTime"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:oeeTimeDetails:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:oeeTimeDetails:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:oeeTimeDetails:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:oeeTimeDetails:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:oeeTimeDetails:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:oeeTimeDetails:remove')}]];
        var prefix = ctx + "edi/push/oeeTimeDetails";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "OEE时间明细",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'recId',
                    title: '记录ID'
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'workshopId',
                    title: '车间代码'
                },
                {
                    field: 'workshopName',
                    title: '车间名称'
                },
                {
                    field: 'productionLineId',
                    title: '产线代码'
                },
                {
                    field: 'productionLineName',
                    title: '产线名称'
                },
                {
                    field: 'stationId',
                    title: '工位代码'
                },
                {
                    field: 'stationName',
                    title: '工位名称'
                },
                {
                    field: 'deviceType',
                    title: '工艺装备分类(1设备；2模具；)'
                },
                {
                    field: 'deviceId',
                    title: '工艺装备代码'
                },
                {
                    field: 'deviceName',
                    title: '工艺装备名称'
                },
                {
                    field: 'type',
                    title: '大类(1计划工作,2计划停机,3非计划停机)'
                },
                {
                    field: 'subType',
                    title: '小类(1工作；2维修；3保养；4停机)'
                },
                {
                    field: 'subTypeDescription',
                    title: '小类描述'
                },
                {
                    field: 'planBeginTime',
                    title: '计划开始时间'
                },
                {
                    field: 'planEndTime',
                    title: '计划结束时间'
                },
                {
                    field: 'startTime',
                    title: '实际开始时间'
                },
                {
                    field: 'endTime',
                    title: '实际结束时间'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>