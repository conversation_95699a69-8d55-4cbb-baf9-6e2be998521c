<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('排产数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>供应商代码：</label>
                                <input type="text" name="supplierCode"/>
                            </li>
                            <li>
                                <label>供应商名称：</label>
                                <input type="text" name="supplierName"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>供应商总成零件号：</label>
                                <input type="text" name="vendorProductNo"/>
                            </li>
                            <li>
                                <label>供应商总成零件名称：</label>
                                <input type="text" name="vendorProductName"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="cheryProductNo"/>
                            </li>
                            <li>
                                <label>奇瑞零件名称：</label>
                                <input type="text" name="cheryProductName"/>
                            </li>
                            <li>
                                <label>计划单号：</label>
                                <input type="text" name="planNo"/>
                            </li>
                            <li>
                                <label>生产工单号：</label>
                                <input type="text" name="manufactureNo"/>
                            </li>
                            <li>
                                <label>生产批次号：</label>
                                <input type="text" name="productBatchNo"/>
                            </li>
                            <li>
                                <label>批次计划数量：</label>
                                <input type="text" name="manufactureNum"/>
                            </li>
                            <li>
                                <label>批次投入数量：</label>
                                <input type="text" name="manufactureInputNum"/>
                            </li>
                            <li>
                                <label>批次产出数量：</label>
                                <input type="text" name="manufactureOutputNum"/>
                            </li>
                            <li>
                                <label>计划开始时间：</label>
                                <input type="text" name="planBeginTime"/>
                            </li>
                            <li>
                                <label>计划结束时间：</label>
                                <input type="text" name="planEndTime"/>
                            </li>
                            <li>
                                <label>实际开始时间：</label>
                                <input type="text" name="actualBeginTime"/>
                            </li>
                            <li>
                                <label>实际结束时间：</label>
                                <input type="text" name="actualEndTime"/>
                            </li>
                              <li>
                                <label>是否已推送：</label>
                                <select name="hasPush">
                                    <option value="">请选择</option>
                                    <option value=false>未推送</option>
                                    <option value=true>已推送</option>
                                </select>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:scheduling:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:scheduling:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:scheduling:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:scheduling:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:scheduling:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:scheduling:remove')}]];
        var prefix = ctx + "edi/push/scheduling";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "排产数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'supplierCode',
                    title: '供应商代码'
                },
                {
                    field: 'supplierName',
                    title: '供应商名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'vendorProductNo',
                    title: '供应商总成零件号'
                },
                {
                    field: 'vendorProductName',
                    title: '供应商总成零件名称'
                },
                {
                    field: 'cheryProductNo',
                    title: '奇瑞零件号'
                },
                {
                    field: 'cheryProductName',
                    title: '奇瑞零件名称'
                },
                {
                    field: 'planNo',
                    title: '计划单号'
                },
                {
                    field: 'manufactureNo',
                    title: '生产工单号'
                },
                {
                    field: 'productBatchNo',
                    title: '生产批次号'
                },
                {
                    field: 'manufactureNum',
                    title: '批次计划数量'
                },
                {
                    field: 'manufactureInputNum',
                    title: '批次投入数量'
                },
                {
                    field: 'manufactureOutputNum',
                    title: '批次产出数量'
                },
                {
                    field: 'planStatus',
                    title: '排产状态 , 0 未生产 1.生产中，2.已完工，3.已取消，4.已终止'
                },
                {
                    field: 'planBeginTime',
                    title: '计划开始时间'
                },
                {
                    field: 'planEndTime',
                    title: '计划结束时间'
                },
                {
                    field: 'actualBeginTime',
                    title: '实际开始时间'
                },
                {
                    field: 'actualEndTime',
                    title: '实际结束时间'
                },
                {
                    field: 'hasPush',
                    title: '是否已推送'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>