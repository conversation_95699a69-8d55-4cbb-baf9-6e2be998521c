<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('推送数据查看')" />
    <th:block th:include="include :: bootstrap-editable-css"/>
    <style>
        .search-form {
            margin-bottom: 15px;
        }
        .push-button-container {
            margin-bottom: 15px;
        }
        .tab-content .tab-pane {
            padding-top: 15px;
        }
        .push-button-container {
            margin: 10px 0;
            text-align: right;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- Tab导航 -->
        <div class="col-sm-12">
            <ul class="nav nav-tabs" id="interfaceTabs">
                <!-- 动态生成tab -->
            </ul>
        </div>
        <!-- Tab内容 -->
        <div class="col-sm-12">
            <div class="tab-content" id="interfaceTabContent">
                <!-- 动态生成tab内容 -->
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    /*<![CDATA[*/
    // const ctx = /*[[@{/}]]*/;
    const interfaces = [
        {
            name: '供应商BOM',
            path: 'edi/push/bom',
            tableName: 'supplier_bom',
            pushUrl: 'push/directlyPushSupplierBom',
            searchFields: ['supplierCode', 'supplierName', 'bomCode', 'bomName', 'bomVersion', 'cheryProductNo', 'cheryProductName']
        },
        {
            name: '日物料需求计划风险确认',
            path: 'edi/push/conDate',
            tableName: 'supplier_con_date',
            pushUrl: 'push/directlyPushSupplierConDate',
            searchFields: ['supplierCode', 'supplierName', 'planDate', 'materialCode']
        },
        {
            name: 'M+6月物料需求计划风险确认',
            path: 'edi/push/conMmrp',
            tableName: 'supplier_con_mmrp',
            pushUrl: 'push/directlyPushSupplierConMmrp',
            searchFields: ['supplierCode', 'supplierName', 'planMonth', 'materialCode']
        },
        {
            name: '采购订单风险确认',
            path: 'edi/push/conPo',
            tableName: 'supplier_con_po',
            pushUrl: 'push/directlyPushSupplierConPo',
            searchFields: ['supplierCode', 'supplierName', 'poNumber', 'materialCode']
        },
        {
            name: '人员资质信息',
            path: 'edi/push/employee',
            tableName: 'supplier_employee',
            pushUrl: 'push/directlyPushSupplierEmployee',
            searchFields: ['employeeCode', 'employeeName', 'department', 'position']
        },
        {
            name: '供应商基础信息',
            path: 'edi/push/info',
            tableName: 'supplier_info',
            pushUrl: 'push/directlyPushSupplierInfo',
            searchFields: ['supplierCode', 'supplierName', 'plantId', 'plantName']
        },
        {
            name: '附件类数据',
            path: 'edi/push/proAttachmentData',
            tableName: 'supplier_pro_attachment_data',
            pushUrl: 'push/directlyPushSupplierProAttachmentData',
            searchFields: ['attachmentName', 'attachmentType', 'relatedId']
        },
        {
            name: '过程控制项质量数据',
            path: 'edi/push/cps',
            tableName: 'supplier_pro_cps',
            pushUrl: 'push/directlyPushSupplierProCps',
            searchFields: ['controlItem', 'qualityData', 'checkTime']
        },
        {
            name: '生产过程数据',
            path: 'edi/push/proData',
            tableName: 'supplier_pro_data',
            pushUrl: 'push/directlyPushSupplierProData',
            searchFields: ['productionLine', 'workStation', 'productCode']
        },
        {
            name: '环境业务数据',
            path: 'edi/push/environment',
            tableName: 'supplier_pro_environment',
            pushUrl: 'push/directlyPushSupplierProEnvironment',
            searchFields: ['environmentType', 'monitorPoint', 'measureTime']
        },
        {
            name: '产品一次合格率',
            path: 'edi/push/firstPassyield',
            tableName: 'supplier_pro_first_passyield',
            pushUrl: 'push/directlyPushSupplierProFirstPassyield',
            searchFields: ['productCode', 'productName', 'yieldRate', 'statisticsDate']
        },
        {
            name: '缺陷业务数据',
            path: 'edi/push/proFlaw',
            tableName: 'supplier_pro_flaw',
            pushUrl: 'push/directlyPushSupplierProFlaw',
            searchFields: ['flawType', 'flawCode', 'productCode', 'occurTime']
        },
        {
            name: '物料主数据',
            path: 'edi/push/materialData',
            tableName: 'supplier_pro_material_data',
            pushUrl: 'push/directlyPushSupplierProMaterialData',
            searchFields: ['materialCode', 'materialName', 'materialType', 'unit']
        },
        {
            name: '来料检验数据',
            path: 'edi/push/materialStock',
            tableName: 'supplier_pro_material_stock',
            pushUrl: 'push/directlyPushSupplierProMaterialStock',
            searchFields: ['materialCode', 'batchNumber', 'inspectionResult', 'inspectionDate']
        },
        {
            name: '设备OEE达成率',
            path: 'edi/push/oeeAchievementRate',
            tableName: 'supplier_pro_oee_achievement_rate',
            pushUrl: 'push/directlyPushSupplierProOeeAchievementRate',
            searchFields: ['equipmentCode', 'equipmentName', 'oeeRate', 'statisticsDate']
        },
        {
            name: 'OEE时间明细',
            path: 'edi/push/oeeTimeDetails',
            tableName: 'supplier_pro_oee_time_details',
            pushUrl: 'push/directlyPushSupplierProOeeTimeDetails',
            searchFields: ['equipmentCode', 'timeType', 'startTime', 'endTime']
        },
        {
            name: '工艺',
            path: 'edi/push/process',
            tableName: 'supplier_pro_process',
            pushUrl: 'push/directlyPushSupplierProProcess',
            searchFields: ['processCode', 'processName', 'processType', 'version']
        },
        {
            name: '工艺装备',
            path: 'edi/push/equipment',
            tableName: 'supplier_pro_process_equipment',
            pushUrl: 'push/directlyPushSupplierProProcessEquipment',
            searchFields: ['equipmentCode', 'equipmentName', 'equipmentType', 'status']
        },
        {
            name: '排产数据',
            path: 'edi/push/scheduling',
            tableName: 'supplier_pro_scheduling',
            pushUrl: 'push/directlyPushSupplierProScheduling',
            searchFields: ['scheduleCode', 'productCode', 'planDate', 'quantity']
        },
        {
            name: '工位一次合格率',
            path: 'edi/push/stationFirstPassyield',
            tableName: 'supplier_pro_station_first_passyield',
            pushUrl: 'push/directlyPushSupplierProStationFirstPassyield',
            searchFields: ['stationCode', 'stationName', 'yieldRate', 'statisticsDate']
        },
        {
            name: '供应商共享库存',
            path: 'edi/push/sinvData',
            tableName: 'supplier_sinv_data',
            pushUrl: 'push/directlyPushSupplierSinvData',
            searchFields: ['supplierCode', 'materialCode', 'stockQuantity', 'updateTime']
        }
    ];

    $(function() {
        $.ajax({
            url: ctx + 'edi/pushInterfaceConfig/listFunctionForCurrentUser',
            type: 'POST',
            success: function(userInterfaces) {
                if (userInterfaces && userInterfaces.length > 0) {
                    const availableInterfaces = interfaces.filter(function(inter) {
                        return userInterfaces.indexOf(inter.name) !== -1;
                    });

                    if (availableInterfaces.length === 0) {
                        $('#interfaceTabContent').html('<div class="alert alert-warning">暂无配置的数据项</div>');
                        return;
                    }

                    availableInterfaces.forEach(function(inter, index) {
                        const tabId = 'tab-' + index;
                        const tableId = 'table-' + index;
                        const formId = 'form-' + index;
                        const isActive = (index === 0) ? 'active' : '';

                        $('#interfaceTabs').append(
                            '<li class="' + isActive + '"><a href="#' + tabId + '" data-toggle="tab" data-index="' + index + '">' + inter.name + '</a></li>'
                        );

                        const searchFormHtml = createSearchForm(inter);

                        const tabPane =
                            '<div class="tab-pane ' + isActive + '" id="' + tabId + '">' +
                            '  <div class="row">' +
                            '    <div class="col-sm-12 search-collapse">' +
                            '      <form id="' + formId + '">' +
                            '        <div class="select-list">' +
                            '          <ul>' +
                            '<li>' +
                            '                            <label>批次号：</label>' +
                            '                            <select id="outBatchNo" name="outBatchNo" class="form-control-select">' +
                            '                            </select>' +
                            '                        </li>' +
                            // searchFormHtml +
                            '            <li>' +
                            '              <a class="btn btn-primary btn-rounded btn-sm" onclick="searchTable(' + index + ')"><i class="fa fa-search"></i>&nbsp;搜索</a></li>' +
                            // '              <a class="btn btn-warning btn-rounded btn-sm" onclick="resetForm(' + index + ')"><i class="fa fa-refresh"></i>&nbsp;重置</a>' +
                            '        <li><a class="btn btn-warning btn-rounded btn-sm" onclick="pushData(' + index + ')">' +
                            '          <i class="fa fa-check"></i> 推送数据</a></li>' +
                            '            </li>' +
                            '          </ul>' +
                            '        </div>' +
                            '      </form>' +
                            '    </div>' +
                            '    <div class="col-sm-12 select-table table-striped">' +
                            '      <table id="' + tableId + '"></table>' +
                            '    </div>' +
                            '  </div>' +
                            '</div>';
                        $('#interfaceTabContent').append(tabPane);
                    });

                    window.availableInterfaces = availableInterfaces;
                    const inter = window.availableInterfaces ? window.availableInterfaces[0] : interfaces[0];
                    // 加载批次号
                    $.ajax({
                        url: ctx + "push/listAllBatchNo",
                        type: "get",
                        data: {
                            tableName: inter.tableName,
                            type: 3
                        },
                        success: function (data) {
                            if (data && data.length > 0) {
                                const outBatchNoSelect = $("#outBatchNo");
                                outBatchNoSelect.empty();
                                for (let i = 0; i < data.length; i++) {
                                    outBatchNoSelect.append(new Option(data[i], data[i]));
                                }
                                if (availableInterfaces.length > 0) {
                                    initTable(0);
                                }

                                $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                                    const index = $(e.target).data('index');
                                    initTable(index);
                                });
                            }
                        }
                    });

                    $('#outBatchNo').on('change', function () {
                        $.table.search();
                    });
                } else {
                    $('#interfaceTabContent').html('<div class="alert alert-warning">您暂无配置的数据项</div>');
                }
            },
            error: function() {
                $('#interfaceTabContent').html('<div class="alert alert-danger">加载接口权限失败</div>');
            }
        });
    });

    function createSearchForm(inter) {
        let formHtml = '';
        inter.searchFields.forEach(function(field) {
            const label = getFieldLabel(field);
            formHtml +=
                '<li>' +
                '  <label>' + label + '：</label>' +
                '  <input type="text" name="' + field + '"/>' +
                '</li>';
        });
        return formHtml;
    }

    function getFieldLabel(field) {
        const labelMap = {
            'supplierCode': '供应商代码',
            'supplierName': '供应商名称',
            'bomCode': 'BOM编码',
            'bomName': 'BOM名称',
            'bomVersion': 'BOM版本',
            'cheryProductNo': '奇瑞零件号',
            'cheryProductName': '奇瑞零件名称',
            'planDate': '计划日期',
            'materialCode': '物料编码',
            'planMonth': '计划月份',
            'poNumber': '采购订单号',
            'employeeCode': '员工编码',
            'employeeName': '员工姓名',
            'department': '部门',
            'position': '职位',
            'plantId': '工厂代码',
            'plantName': '工厂名称',
            'attachmentName': '附件名称',
            'attachmentType': '附件类型',
            'relatedId': '关联ID',
            'controlItem': '控制项',
            'qualityData': '质量数据',
            'checkTime': '检查时间',
            'productionLine': '生产线',
            'workStation': '工位',
            'productCode': '产品编码',
            'environmentType': '环境类型',
            'monitorPoint': '监测点',
            'measureTime': '测量时间',
            'productName': '产品名称',
            'yieldRate': '合格率',
            'statisticsDate': '统计日期',
            'flawType': '缺陷类型',
            'flawCode': '缺陷编码',
            'occurTime': '发生时间',
            'materialName': '物料名称',
            'materialType': '物料类型',
            'unit': '单位',
            'batchNumber': '批次号',
            'inspectionResult': '检验结果',
            'inspectionDate': '检验日期',
            'equipmentCode': '设备编码',
            'equipmentName': '设备名称',
            'oeeRate': 'OEE达成率',
            'timeType': '时间类型',
            'startTime': '开始时间',
            'endTime': '结束时间',
            'processCode': '工艺编码',
            'processName': '工艺名称',
            'processType': '工艺类型',
            'version': '版本',
            'equipmentType': '设备类型',
            'status': '状态',
            'scheduleCode': '排产编码',
            'quantity': '数量',
            'stationCode': '工位编码',
            'stationName': '工位名称',
            'stockQuantity': '库存数量',
            'updateTime': '更新时间'
        };
        return labelMap[field] || field;
    }

    function getInterfaceColumns(index) {
        const interfaceColumnsMap = {
            'supplier_bom': [ // BOM主数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'bomCode', title: 'BOM编码'},
                {field: 'bomName', title: 'BOM名称'},
                {field: 'bomVersion', title: 'BOM版本'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'vendorProductNo', title: '供应商父件编码'},
                {field: 'vendorProductName', title: '供应商父件名称'},
                {field: 'vendorProductType', title: '父件类型(成品, 半成品)'},
                {field: 'materialUnit', title: '父件单位'},
                {field: 'subMaterialCode', title: '子件编码'},
                {field: 'subMaterialName', title: '子件名称'},
                {field: 'subMaterialType', title: '子件类型(半成品, 原材料)'},
                {field: 'subMaterialUnit', title: '子件单位'},
                {field: 'subMaterialQuota', title: '子件用量'},
                {field: 'dataUpdateTime', title: 'BOM变更时间'}
            ],
            'supplier_con_date': [ // 日物料需求计划风险确认
                {field: 'id', title: 'ID', visible: false},
                {field: 'releaseEdition', title: '需求发布版次'},
                {field: 'materialCode', title: '奇瑞零件号'},
                {field: 'materialDescription', title: '零件名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'startDate', title: '起始日期'},
                {field: 'quantityDemand1', title: '需求数量1'},
                {field: 'quantityDemand2', title: '需求数量2'},
                {field: 'quantityDemand3', title: '需求数量3'},
                {field: 'quantityDemand4', title: '需求数量4'},
                {field: 'quantityDemand5', title: '需求数量5'},
                {field: 'quantityDemand6', title: '需求数量6'},
                {field: 'quantityDemand7', title: '需求数量7'},
                {field: 'quantityDemand8', title: '需求数量8'},
                {field: 'quantityDemand9', title: '需求数量9'},
                {field: 'quantityDemand10', title: '需求数量10'},
                {field: 'quantityDemand11', title: '需求数量11'},
                {field: 'quantityDemand12', title: '需求数量12'},
                {field: 'quantityDemand13', title: '需求数量13'},
                {field: 'quantityDemand14', title: '需求数量14'},
                {field: 'quantityDemand15', title: '需求数量15'},
                {field: 'quantityDemand16', title: '需求数量16'},
                {field: 'quantityDemand17', title: '需求数量17'},
                {field: 'quantityDemand18', title: '需求数量18'},
                {field: 'quantityDemand19', title: '需求数量19'},
                {field: 'quantityDemand20', title: '需求数量20'},
                {field: 'quantityDemand21', title: '需求数量21'},
                {field: 'quantityDemand22', title: '需求数量22'},
                {field: 'quantityDemand23', title: '需求数量23'},
                {field: 'quantityDemand24', title: '需求数量24'},
                {field: 'quantityDemand25', title: '需求数量25'},
                {field: 'quantityDemand26', title: '需求数量26'},
                {field: 'quantityDemand27', title: '需求数量27'},
                {field: 'quantityDemand28', title: '需求数量28'},
                {field: 'quantityDemand29', title: '需求数量29'},
                {field: 'quantityDemand30', title: '需求数量30'},
                {field: 'quantityDemand31', title: '需求数量31'}
            ],
            'supplier_con_mmrp': [ // M+6月物料需求计划风险确认
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'releaseEdition', title: '需求发布版次'},
                {field: 'materialCode', title: '零件号'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'feedbackResults', title: '反馈结果'},
                {field: 'ventureType', title: '风险类型'},
                {field: 'ventureSpecific', title: '具体风险'},
                {field: 'measures', title: '应对措施'},
                {field: 'startMonth', title: '起始月份'},
                {field: 'quantityMeet1', title: '满足数量1'},
                {field: 'quantityMeet2', title: '满足数量2'},
                {field: 'quantityMeet3', title: '满足数量3'},
                {field: 'quantityMeet4', title: '满足数量4'},
                {field: 'quantityMeet5', title: '满足数量5'},
                {field: 'quantityMeet6', title: '满足数量6'},
                {field: 'quantityMeet7', title: '满足数量7'},
                {field: 'quantityMeet8', title: '满足数量8'},
                {field: 'quantityMeet9', title: '满足数量9'},
                {field: 'quantityMeet10', title: '满足数量10'},
                {field: 'quantityMeet11', title: '满足数量11'},
                {field: 'quantityMeet12', title: '满足数量12'}
            ],
            'supplier_con_po': [ // 采购订单风险确认
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'purchaseOrder', title: '采购订单号'},
                {field: 'serialNumber', title: '行项目号'},
                {field: 'quantityMeet', title: '满足数量'},
                {field: 'feedbackResults', title: '反馈结果'},
                {field: 'ventureType', title: '风险类型'},
                {field: 'ventureSpecific', title: '具体风险'},
                {field: 'measures', title: '应对措施'}
            ],
            'supplier_employee': [ // 人员资质信息
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'operatorId', title: '工位人员账号'},
                {field: 'operatorName', title: '工位人员姓名'},
                {field: 'haveQuantity', title: '是否有资质(Y,N)'},
                {field: 'dataUpdateTime', title: '供应商修改时间'},
                {field: 'positionId', title: '岗位代码'},
                {field: 'positionName', title: '岗位名称'},
                {field: 'qualificationLevel', title: '资质等级(Level_4，Level_3，Level_2, Level_1)'},
                {field: 'checkInTime', title: '资质获取时间'},
                {field: 'checkOutTime', title: '资质失去时间'}
            ],
            'supplier_info': [ // 供应商基础信息
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'keyStation', title: '是否关键工位'},
                {field: 'dataUpdateTime', title: '供应商修改时间'},
                {field: 'productionLineOrder', title: '产线顺序'},
                {field: 'stationOrder', title: '工位顺序'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'}
            ],
            'supplier_pro_attachment_data': [ // 附件类数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'type', title: '数据类型(1产前管理；2人员资质；3监控视频)'},
                {field: 'fileName', title: '文件名'},
                {field: 'fileUrl', title: '图文地址'},
                {field: 'dateTime', title: '生成时间'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'deviceName', title: '设备名称'},
                {field: 'deviceId', title: '设备代码'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'vendorProductSn', title: '供应商总成SN码'}
            ],
            'supplier_pro_cps': [ // 过程控制项质量数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'vendorProductSn', title: '供应商总成SN码'},
                {field: 'vendorProductBatch', title: '供应商总成批次号'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'cheryProductSn', title: '奇瑞SN码'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'cpsName', title: '过程控制项名称'},
                {field: 'cpsCode', title: '过程控制项代码'},
                {field: 'gatherSpot', title: '控制项点位'},
                {field: 'samplingRate', title: '控制项要求频率'},
                {field: 'limitUpdateTime', title: '上下限更新时间'},
                {field: 'vendorFieldDesc', title: '控制项描述'},
                {field: 'carrierCode', title: '载体编码'},
                {field: 'intputQty', title: '投入数量'},
                {field: 'fttQty', title: '一次合格数量'},
                {field: 'parameter', title: '参数 , 是传Y，否传N'},
                {field: 'characteristic', title: '特性 , 是传Y，否传N'},
                {field: 'cc', title: 'CC项 , 是传Y，否传N'},
                {field: 'sc', title: 'SC项 , 是传Y，否传N'},
                {field: 'spc', title: 'SPC , 是传Y，否传N'},
                {field: 'standardValue', title: '控制项标准值'},
                {field: 'upperLimit', title: '控制项上限'},
                {field: 'lowerLimit', title: '控制项下限'},
                {field: 'decimalValue', title: '控制项实测值'},
                {field: 'unitCn', title: '控制项值的单位名称-中文'},
                {field: 'unitEn', title: '控控制项单位英文'},
                {field: 'checkResult', title: '检测结果'},
                {field: 'detectionMode', title: '在线检测'},
                {field: 'workShift', title: '班次'},
                {field: 'collectTime', title: '采集时间'},
                {field: 'checkMode', title: '检测方式'},
                {field: 'deviceCode', title: '检测设备编号'},
                {field: 'deviceName', title: '检测设备名称'}
            ],
            'supplier_pro_data': [ // 生产过程数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'empCode', title: '工位人员编号'},
                {field: 'empName', title: '工位人员姓名'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductBatch', title: '供应商总成批次号'},
                {field: 'vendorProductSn', title: '供应商总成SN码'},
                {field: 'subProdNo', title: '子件编码'},
                {field: 'subProdName', title: '子件名称'},
                {field: 'subBatchNo', title: '子件批次号'},
                {field: 'subProdSn', title: '子件SN码'},
                {field: 'childSource', title: '子件物料来源'},
                {field: 'subSupplierCode', title: '分供方代码'},
                {field: 'subSupplierName', title: '分供方名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'cheryProductSn', title: '奇瑞SN码'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'workShift', title: '班次'},
                {field: 'materialInputTime', title: '进工位的时间'},
                {field: 'materialOutputTime', title: '出工位的时间'},
                {field: 'vendorFieldNum', title: '装配设备编号'},
                {field: 'vendorFieldName', title: '装配设备名称'},
                {field: 'instrumentQualityStatus', title: '设备判定的质量状态'},
                {field: 'manualQualityStatus', title: '人工判定的质量状态'},
                {field: 'finalQualityStatus', title: '最终质量状态'},
                {field: 'collectTime', title: '采集时间'},
                {field: 'dateTime', title: '子件绑定扫码时间'},
                {field: 'parentHardwareRevision', title: '父件硬件版本号'},
                {field: 'parentSoftwareRevision', title: '父件软件版本号'},
                {field: 'childHardwareRevision', title: '子件硬件版本号'},
                {field: 'childSoftwareRevision', title: '子件软件版本号'}
            ],
            'supplier_pro_environment': [ // 环境业务数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'envIndicatorName', title: '环境指标名称'},
                {field: 'numValue', title: '指标实测值'},
                {field: 'upperLimit', title: '上限值'},
                {field: 'lowerLimit', title: '下限值'},
                {field: 'chineseUnit', title: '单位'},
                {field: 'equipmentCode', title: '采集仪器代码'},
                {field: 'equipmentName', title: '采集仪器名称'},
                {field: 'dataCollectionPoint', title: '数据采集的点位'},
                {field: 'collectTime', title: '数据采集的时间'}
            ],
            'supplier_pro_first_passyield': [ // 产品一次合格率
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'workOrderNumber', title: '批次计划数量'},
                {field: 'defectiveNumber', title: '不合格数'},
                {field: 'acceptableNumber', title: '合格数'},
                {field: 'oncePassRateRealValue', title: '一次合格率实际值'},
                {field: 'oncePassRateTagValue', title: '一次合格率目标值'},
                {field: 'workShift', title: '班次'},
                {field: 'statisticalTime', title: '生产日期'},
                {field: 'dateTime', title: '值统计时间'}
            ],
            'supplier_pro_flaw': [ // 缺陷业务数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'flawType', title: '缺陷类型'},
                {field: 'flawCode', title: '缺陷编码'},
                {field: 'productCode', title: '产品编码'},
                {field: 'occurTime', title: '发生时间'},
                {field: 'defectsName', title: '缺陷名称'},
                {field: 'classOfName', title: '缺陷分类'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'vendorProductBatch', title: '供应商总成批次号'},
                {field: 'vendorProductSn', title: '供应商总成SN码'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'cheryProductSn', title: '奇瑞SN码'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'workShift', title: '班次'},
                {field: 'numberofdefect', title: '缺陷件数'},
                {field: 'defectsDesc', title: '缺陷描述'},
                {field: 'defectsLevel', title: '缺陷等级'},
                {field: 'statisticalTime', title: '缺陷录入时间'}
            ],
            'supplier_pro_material_data': [ // 物料主数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'type', title: '类型'},
                {field: 'vendorHardwareRevision', title: '供应商零件版本号'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'oemHardwareRevision', title: '奇瑞硬件版本号'},
                {field: 'oemSoftwareRevision', title: '奇瑞软件版本号'},
                {field: 'oemModel', title: '车型'},
                {field: 'oemProjectName', title: '项目名称'},
                {field: 'launched', title: '是否SOP'},
                {field: 'dateTime', title: '数据同步执行时间'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'procurementType', title: '芯片采购类型'},
                {field: 'mpnCode', title: '芯片MPN标识码'},
                {field: 'mpnName', title: '芯片MPN标识名称'},
                {field: 'validDays', title: '物料有效期'}
            ],
            'supplier_pro_material_stock': [ // 来料检验数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'supplierSubCode', title: '子件编码'},
                {field: 'supplierSubName', title: '子件名称'},
                {field: 'subSupplierCode', title: '分供方代码'},
                {field: 'subSupplierName', title: '分供方名称'},
                {field: 'subSupplierAddress', title: '分供方地址'},
                {field: 'componentCode', title: '分供方子件编码'},
                {field: 'componentName', title: '分供方子件名称'},
                {field: 'subBatchNo', title: '子件批次号'},
                {field: 'subBatchNum', title: '子件批次数量'},
                {field: 'subBatchSn', title: '子件SN码'},
                {field: 'empCode', title: '检验人员编号'},
                {field: 'empName', title: '检验人员姓名'},
                {field: 'deviceCode', title: '检测设备编号'},
                {field: 'deviceName', title: '检测设备名称'},
                {field: 'featureName', title: '参数名称/特性名称'},
                {field: 'featureUnit', title: '参数单位/特性单位'},
                {field: 'standardValue', title: '参数/特性标准值'},
                {field: 'featureUpper', title: '参数/特性上限值'},
                {field: 'featureLower', title: '参数/特性下限值'},
                {field: 'featureValue', title: '参数/特性实测值'},
                {field: 'checkNo', title: '来料检验单号'},
                {field: 'checkResult', title: '来料检验结果'},
                {field: 'checkTime', title: '检验时间'},
                {field: 'samplingRate', title: '控制项要求频率'},
                {field: 'limitUpdateTime', title: '上下限更新时间'},
                {field: 'vendorFieldDesc', title: '控制项描述'},
                {field: 'vendorFieldCode', title: '控制项代码'},
                {field: 'deadLine', title: '库存有效日期'}
            ],
            'supplier_pro_oee_achievement_rate': [ // 设备OEE达成率
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'deviceId', title: '设备代码'},
                {field: 'deviceName', title: '设备名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'rate', title: 'OEE实际值'},
                {field: 'rateTagValue', title: 'OEE目标值'},
                {field: 'workShift', title: '班次'},
                {field: 'statisticalTime', title: '生产日期'},
                {field: 'dateTime', title: '值统计时间'}
            ],
            'supplier_pro_oee_time_details': [ // OEE时间明细
                {field: 'id', title: 'ID', visible: false},
                {field: 'recId', title: '记录ID'},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'deviceType', title: '工艺装备分类'},
                {field: 'deviceId', title: '工艺装备代码'},
                {field: 'deviceName', title: '工艺装备名称'},
                {field: 'type', title: '大类'},
                {field: 'subType', title: '小类'},
                {field: 'subTypeDescription', title: '小类描述'},
                {field: 'planBeginTime', title: '计划开始时间'},
                {field: 'planEndTime', title: '计划结束时间'},
                {field: 'startTime', title: '实际开始时间'},
                {field: 'endTime', title: '实际结束时间'}
            ],
            'supplier_pro_process': [ // 工艺
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'techCode', title: '工艺编码'},
                {field: 'techName', title: '工艺名称'},
                {field: 'techVersion', title: '工艺版本'},
                {field: 'isEnabled', title: '是否启用'},
                {field: 'maxProcessingCapacity', title: '最大加工能力'},
                {field: 'promiseRatio', title: '承诺比例'},
                {field: 'makeSamePeriod', title: '按滚动预测需求的供货周期'},
                {
                    title: '操作', align: 'center', formatter: function (value, row, index) {
                        return '<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showProcessDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a>';
                    }
                }
            ],
            'supplier_pro_process_equipment': [ // 工艺装备
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'deviceType', title: '工艺装备分类'},
                {field: 'deviceId', title: '工艺装备代码'},
                {field: 'deviceName', title: '工艺装备名称'},
                {field: 'manufacturer', title: '生产厂家'},
                {field: 'modelNumber', title: '工艺装备型号'},
                {field: 'productionDate', title: '工艺装备制造日期'},
                {field: 'material', title: '主要材质'},
                {field: 'currentLocation', title: '当前存放地点'},
                {field: 'deviceStatus', title: '工艺装备状态'},
                {field: 'designLifeUnits', title: '设计寿命单位'},
                {field: 'designLifeValue', title: '设计寿命'},
                {field: 'currentUsageCount', title: '当前剩余寿命'},
                {field: 'effectiveDays', title: '每月有效工作天数'},
                {field: 'maxProcessHours', title: '每天最大加工工时'},
                {field: 'regularProcessHours', title: '每天常规加工工时'},
                {field: 'deviceStartDate', title: '投产日期'},
                {field: 'deviceEndDate', title: '报废停产日期'},
                {field: 'machineCosts', title: '单台设备投资金额'},
                {field: 'machinePurchasePeriod', title: '设备购买周期'},
                {field: 'machineType', title: '设备类型'},
                {field: 'unitperHour', title: '单位小时产出'},
                {field: 'cavityCount', title: '穴腔数量'},
                {field: 'moldSize', title: '模具尺寸规格'},
                {field: 'copyMoldCosts', title: '模具复制模费用'},
                {field: 'overhaulCount', title: '模具大修次数'},
                {field: 'calibrationDate', title: '检具最近校准日期'},
                {field: 'calibrationDueDays', title: '检具校准到期天数'},
                {field: 'unitType', title: '检具检测单位'}
            ],
            'supplier_pro_scheduling': [ // 排产数据
                {field: 'id', title: 'ID', visible: false},
                {field: 'scheduleCode', title: '排产编码'},
                {field: 'productCode', title: '产品编码'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'planNo', title: '计划单号'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'manufactureNum', title: '批次计划数量'},
                {field: 'manufactureInputNum', title: '批次投入数量'},
                {field: 'manufactureOutputNum', title: '批次产出数量'},
                {field: 'planStatus', title: '排产状态'},
                {field: 'planBeginTime', title: '计划开始时间'},
                {field: 'planEndTime', title: '计划结束时间'},
                {field: 'actualBeginTime', title: '实际开始时间'},
                {field: 'actualEndTime', title: '实际结束时间'}
            ],
            'supplier_pro_station_first_passyield': [ // 工位一次合格率
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'plantId', title: '工厂代码'},
                {field: 'plantName', title: '工厂名称'},
                {field: 'workshopId', title: '车间代码'},
                {field: 'workshopName', title: '车间名称'},
                {field: 'productionLineId', title: '产线代码'},
                {field: 'productionLineName', title: '产线名称'},
                {field: 'stationId', title: '工位代码'},
                {field: 'stationName', title: '工位名称'},
                {field: 'cheryProductNo', title: '奇瑞零件号'},
                {field: 'cheryProductName', title: '奇瑞零件名称'},
                {field: 'vendorProductNo', title: '供应商总成零件号'},
                {field: 'vendorProductName', title: '供应商总成零件名称'},
                {field: 'productBatchNo', title: '生产批次号'},
                {field: 'manufactureNo', title: '生产工单号'},
                {field: 'workOrderNumber', title: '批次计划数量'},
                {field: 'defectiveNumber', title: '不合格数'},
                {field: 'acceptableNumber', title: '合格数'},
                {field: 'oncePassRateRealValue', title: '一次合格率实际值'},
                {field: 'oncePassRateTagValue', title: '一次合格率目标值'},
                {field: 'workShift', title: '班次'},
                {field: 'statisticalTime', title: '生产日期'},
                {field: 'dateTime', title: '值统计时间'}
            ],
            'supplier_sinv_data': [ // 供应商共享库存
                {field: 'id', title: 'ID', visible: false},
                {field: 'supplierCode', title: '供应商代码'},
                {field: 'supplierName', title: '供应商名称'},
                {field: 'materialCode', title: '物料编码'},
                {field: 'materialDescription', title: '物料名称'},
                {field: 'materialType', title: '物料类型'},
                {field: 'quantityCurrent', title: '当前库存数量'},
                {field: 'quantityPlan', title: '原材料在途数量'},
                {field: 'inventoryStatus', title: '库存状态'},
                {field: 'safetyStock', title: '安全库存'},
                {field: 'productionCycle', title: '生产/采购周期'},
                {field: 'dataUpdateTime', title: '库存更新时间'},
                {field: 'supplierBatch', title: '批次'},
                {field: 'supplieryxqDate', title: '有效期截止日期'}
            ]
        };
        const inter = window.availableInterfaces ? window.availableInterfaces[index] : interfaces[index];
        return interfaceColumnsMap[inter.tableName];
    }

    function initTable(index) {
        const tableId = '#table-' + index;
        if ($.data($(tableId)[0], 'bootstrap.table')) {
            return;
        }

        const inter = window.availableInterfaces ? window.availableInterfaces[index] : interfaces[index];
        const columns = getInterfaceColumns(index);

        $(tableId).bootstrapTable({
            url: ctx + inter.path + '/list',
            method: 'post',
            idField: 'id',
            uniqueId: 'id',
            clickToSelect: true,
            contentType: false,
            processData: false,
            queryParams: function(params) {
                let formData = new FormData();
                formData.append("outBatchNo", $('#outBatchNo').val());
                formData.append("hasPush", "false");
                formData.append("fieldHasConfirm", "false");
                formData.append("pageSize", params.limit);
                let pageNum = (params.offset / params.limit) + 1;
                formData.append("pageNum", pageNum.toString());
                return formData;
            },
            ajax: function (request) {
                $.ajax({
                    type: "POST",
                    url: request.url,
                    data: request.data,
                    dataType: "json",
                    contentType: false,
                    processData: false,
                    success: function (res) {
                        request.success({
                            total: res.total,
                            rows: res.rows
                        });
                    },
                    error: function (err) {
                        request.error(err);
                    }
                });
            },
            columns: columns,
            pagination: true,
            pageSize: 10,
            pageList: [10, 25, 50],
            sidePagination: 'server',
            showRefresh: true,
            search: false,
            toolbar: '#toolbar-' + index
        });
    }

    function searchTable(index) {
        const tableId = '#table-' + index;
        $(tableId).bootstrapTable('destroy');
        initTable(index)
    }

    function resetForm(index) {
        const formId = '#form-' + index;
        $(formId)[0].reset();
        const tableId = '#table-' + index;
        $(tableId).bootstrapTable('refresh');
    }

    function exportData(index) {
        const inter = window.availableInterfaces ? window.availableInterfaces[index] : interfaces[index];
        const formId = '#form-' + index;
        const params = {};
        $(formId).serializeArray().forEach(function(item) {
            if (item.value) {
                params[item.name] = item.value;
            }
        });
        $.table.exportExcel(ctx + inter.path + '/export', params);
    }

    function pushData(index) {
        const inter = window.availableInterfaces ? window.availableInterfaces[index] : interfaces[index];
        const tableId = '#table-' + index;
        let batchNo = $('#outBatchNo').val();
        if(!batchNo){
            $.modal.msgError('请选择批次号');
            return;
        }
        $.modal.confirm('即将推送“' + inter.name + '”该批次的所有数据，是否确认？', function() {
            $.ajax({
                url: ctx + inter.pushUrl,
                type: 'POST',
                contentType: 'application/json',
                data: {
                    batchNo: batchNo
                },
                success: function(result) {
                    if (result.code === web_status.SUCCESS) {
                        $.modal.alertSuccess("推送成功！");
                        $(tableId).bootstrapTable('refresh');
                    } else {
                        $.modal.alertError("推送失败：" + result.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("推送请求失败：" + error);
                }
            });
        });
    }

    // 显示工艺详情
    function showProcessDetail(id) {
        const url = ctx + "edi/push/process/detail/" + id;
        $.modal.openTab("工艺详情", url);
    }
    /*]]>*/
</script>
</body>
</html>
