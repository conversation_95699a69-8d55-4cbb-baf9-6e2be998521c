<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('采购订单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>采购订单号：</label>
                                <input type="text" name="purchaseOrder"/>
                            </li>
                            <li>
                                <label>行项目号：</label>
                                <input type="text" name="serialNumber"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>凭证日期：</label>
                                <input type="text" name="voucherDate"/>
                            </li>
                            <li>
                                <label>需方联系人：</label>
                                <input type="text" name="purchaser"/>
                            </li>
                            <li>
                                <label>供方联系人：</label>
                                <input type="text" name="supplier"/>
                            </li>
                            <li>
                                <label>物料编码：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>物料描述：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>需求数量：</label>
                                <input type="text" name="quantityDemand"/>
                            </li>
                            <li>
                                <label>物料单位：</label>
                                <input type="text" name="materialUnit"/>
                            </li>
                            <li>
                                <label>交货日期：</label>
                                <input type="text" name="deliveryDate"/>
                            </li>
                            <li>
                                <label>交货地点：</label>
                                <input type="text" name="deliveryPlace"/>
                            </li>
                            <li>
                                <label>到货数量：</label>
                                <input type="text" name="quantityDelivery"/>
                            </li>
                            <li>
                                <label>备注:含批次号信息：</label>
                                <input type="text" name="note"/>
                            </li>
                            <li>
                                <label>国际贸易条件：</label>
                                <input type="text" name="tradeTerms"/>
                            </li>
                            <li>
                                <label>出口国家：</label>
                                <input type="text" name="country"/>
                            </li>
                            <li>
                                <label>批次：</label>
                                <input type="text" name="batch"/>
                            </li>
<!--                            <li>-->
<!--                                <label>创建人：</label>-->
<!--                                <input type="text" name="createByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>修改人：</label>-->
<!--                                <input type="text" name="updateByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>是否删除：</label>-->
<!--                                <input type="text" name="isDelete"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>版本号：</label>-->
<!--                                <input type="text" name="version"/>-->
<!--                            </li>-->

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:po:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:po:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:po:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:po:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:po:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:po:remove')}]];
        var prefix = ctx + "edi/pull/po";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "采购订单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'purchaseOrder',
                    title: '采购订单号'
                },
                {
                    field: 'serialNumber',
                    title: '行项目号'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'voucherDate',
                    title: '凭证日期'
                },
                {
                    field: 'purchaser',
                    title: '需方联系人'
                },
                {
                    field: 'supplier',
                    title: '供方联系人'
                },
                {
                    field: 'materialCode',
                    title: '物料编码'
                },
                {
                    field: 'materialDescription',
                    title: '物料描述'
                },
                {
                    field: 'quantityDemand',
                    title: '需求数量'
                },
                {
                    field: 'materialUnit',
                    title: '物料单位'
                },
                {
                    field: 'deliveryDate',
                    title: '交货日期'
                },
                {
                    field: 'deliveryPlace',
                    title: '交货地点'
                },
                {
                    field: 'quantityDelivery',
                    title: '到货数量'
                },
                {
                    field: 'note',
                    title: '备注:含批次号信息'
                },
                {
                    field: 'itemType',
                    title: '项目类别文本'
                },
                {
                    field: 'tradeTerms',
                    title: '国际贸易条件'
                },
                {
                    field: 'country',
                    title: '出口国家'
                },
                {
                    field: 'batch',
                    title: '批次'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除'
                },
                {
                    field: 'version',
                    title: '版本号'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>