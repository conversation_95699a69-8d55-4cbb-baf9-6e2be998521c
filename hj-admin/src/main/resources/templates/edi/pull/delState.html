<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('看板配送单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>配送单号：</label>
                                <input type="text" name="deliveryNumber"/>
                            </li>
                            <li>
                                <label>行项目号：</label>
                                <input type="text" name="serialNumber"/>
                            </li>
                            <li>
                                <label>配送单状态：</label>
                                <input type="text" name="serialSrate"/>
                            </li>
                            <li>
                                <label>零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>零件名称：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>收货道口：</label>
                                <input type="text" name="receivingCrossings"/>
                            </li>
                            <li>
                                <label>数量：</label>
                                <input type="text" name="quantityDelivery"/>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" name="dataCreateTime"/>
                            </li>
                            <li>
                                <label>供应商接收时间：</label>
                                <input type="text" name="supplierReceiveTime"/>
                            </li>
                            <li>
                                <label>道口发货时间：</label>
                                <input type="text" name="roadShippedTime"/>
                            </li>
                            <li>
                                <label>道口收货时间：</label>
                                <input type="text" name="roadReceiveTime"/>
                            </li>
                            <li>
                                <label>要求到货时间：</label>
                                <input type="text" name="lastRequrieTime"/>
                            </li>
<!--                            <li>-->
<!--                                <label>创建人：</label>-->
<!--                                <input type="text" name="createByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>修改人：</label>-->
<!--                                <input type="text" name="updateByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>是否删除：</label>-->
<!--                                <input type="text" name="isDelete"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>版本号：</label>-->
<!--                                <input type="text" name="version"/>-->
<!--                            </li>-->

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:delState:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:delState:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:delState:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:delState:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:delState:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:delState:remove')}]];
        var prefix = ctx + "edi/pull/delState";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "看板配送单",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'deliveryNumber',
                    title: '配送单号'
                },
                {
                    field: 'serialNumber',
                    title: '行项目号'
                },
                {
                    field: 'serialSrate',
                    title: '配送单状态'
                },
                {
                    field: 'materialCode',
                    title: '零件号'
                },
                {
                    field: 'materialDescription',
                    title: '零件名称'
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'receivingCrossings',
                    title: '收货道口'
                },
                {
                    field: 'quantityDelivery',
                    title: '数量'
                },
                {
                    field: 'dataCreateTime',
                    title: '创建时间'
                },
                {
                    field: 'supplierReceiveTime',
                    title: '供应商接收时间'
                },
                {
                    field: 'roadShippedTime',
                    title: '道口发货时间'
                },
                {
                    field: 'roadReceiveTime',
                    title: '道口收货时间'
                },
                {
                    field: 'lastRequrieTime',
                    title: '要求到货时间'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除'
                },
                {
                    field: 'version',
                    title: '版本号'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>