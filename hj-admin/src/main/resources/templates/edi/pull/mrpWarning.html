<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('日MRP预警推移列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>零件名称：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>当前库存：</label>
                                <input type="text" name="quantityCurrent"/>
                            </li>
                            <li>
                                <label>需求日期：</label>
                                <input type="text" name="reckonDate"/>
                            </li>
                            <li>
                                <label>需求数量：</label>
                                <input type="text" name="quantityPlanned"/>
                            </li>
                            <li>
                                <label>满足数量：</label>
                                <input type="text" name="quantityPlannedDelivery"/>
                            </li>
                            <li>
                                <label>在途数量：</label>
                                <input type="text" name="quantityInTransit"/>
                            </li>
                            <li>
                                <label>日GAP:日需求数量与满足数量差异：</label>
                                <input type="text" name="dateGap"/>
                            </li>
                            <li>
                                <label>库存GAP:库存推移差异：</label>
                                <input type="text" name="inventoryGap"/>
                            </li>
<!--                            <li>-->
<!--                                <label>创建人：</label>-->
<!--                                <input type="text" name="createByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>修改人：</label>-->
<!--                                <input type="text" name="updateByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>是否删除：</label>-->
<!--                                <input type="text" name="isDelete"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>版本号：</label>-->
<!--                                <input type="text" name="version"/>-->
<!--                            </li>-->

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:mrpWarning:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:mrpWarning:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:mrpWarning:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:mrpWarning:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:mrpWarning:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:mrpWarning:remove')}]];
        var prefix = ctx + "edi/pull/mrpWarning";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "日MRP预警推移",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'materialCode',
                    title: '奇瑞零件号'
                },
                {
                    field: 'materialDescription',
                    title: '零件名称'
                },
                {
                    field: 'quantityCurrent',
                    title: '当前库存'
                },
                {
                    field: 'reckonDate',
                    title: '需求日期'
                },
                {
                    field: 'quantityPlanned',
                    title: '需求数量'
                },
                {
                    field: 'quantityPlannedDelivery',
                    title: '满足数量'
                },
                {
                    field: 'quantityInTransit',
                    title: '在途数量'
                },
                {
                    field: 'dateGap',
                    title: '日GAP:日需求数量与满足数量差异'
                },
                {
                    field: 'inventoryGap',
                    title: '库存GAP:库存推移差异'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除'
                },
                {
                    field: 'version',
                    title: '版本号'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>