<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('日MRP状态监控列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>工厂代码：</label>
                                <input type="text" name="plantId"/>
                            </li>
                            <li>
                                <label>工厂名称：</label>
                                <input type="text" name="plantName"/>
                            </li>
                            <li>
                                <label>需求状态：</label>
                                <input type="text" name="demandSrate"/>
                            </li>
                            <li>
                                <label>奇瑞零件号：</label>
                                <input type="text" name="materialCode"/>
                            </li>
                            <li>
                                <label>零件名称：</label>
                                <input type="text" name="materialDescription"/>
                            </li>
                            <li>
                                <label>集货标识：</label>
                                <input type="text" name="summarySign"/>
                            </li>
                            <li>
                                <label>需求日期：</label>
                                <input type="text" name="dateRequired"/>
                            </li>
                            <li>
                                <label>需求数量：</label>
                                <input type="text" name="quantityDemand"/>
                            </li>
                            <li>
                                <label>需求确认时间：</label>
                                <input type="text" name="confirmTime"/>
                            </li>
                            <li>
                                <label>已建单数量：</label>
                                <input type="text" name="creatQuantity"/>
                            </li>
                            <li>
                                <label>已发货数量：</label>
                                <input type="text" name="quantityDelivery"/>
                            </li>
                            <li>
                                <label>已收货数量：</label>
                                <input type="text" name="quantityReceive"/>
                            </li>
                            <li>
                                <label>在途数量：</label>
                                <input type="text" name="quantityInTransit"/>
                            </li>
                            <li>
                                <label>按时到货比：</label>
                                <input type="text" name="onTimePercentage"/>
                            </li>
                            <li>
                                <label>集货件已建单数量：</label>
                                <input type="text" name="summaryCreatQuantity"/>
                            </li>
                            <li>
                                <label>集货件已发货数量：</label>
                                <input type="text" name="summaryQuantityDelivery"/>
                            </li>
                            <li>
                                <label>集货件已收货数量：</label>
                                <input type="text" name="summaryQuantityReceive"/>
                            </li>
                            <li>
                                <label>集货件已在途数量：</label>
                                <input type="text" name="summaryQuantityInTransit"/>
                            </li>
<!--                            <li>-->
<!--                                <label>创建人：</label>-->
<!--                                <input type="text" name="createByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>修改人：</label>-->
<!--                                <input type="text" name="updateByUser"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>是否删除：</label>-->
<!--                                <input type="text" name="isDelete"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <label>版本号：</label>-->
<!--                                <input type="text" name="version"/>-->
<!--                            </li>-->

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="edi:mrpState:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
<!--                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="edi:mrpState:edit">-->
<!--                    <i class="fa fa-edit"></i> 修改-->
<!--                </a>-->
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="edi:mrpState:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="edi:mrpState:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('edi:mrpState:edit')}]];
        var removeFlag = [[${@permission.hasPermi('edi:mrpState:remove')}]];
        var prefix = ctx + "edi/pull/mrpState";

        $(function() {
            var options = {
                url: prefix + "/list",
                // createUrl: prefix + "/add",
                // updateUrl: prefix + "/edit/{id}",
                // removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "日MRP状态监控",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键：拉取数据供应商根据主键id判断是否存在，在新增和更新',
                    visible: false
                },
                {
                    field: 'plantId',
                    title: '工厂代码'
                },
                {
                    field: 'plantName',
                    title: '工厂名称'
                },
                {
                    field: 'demandSrate',
                    title: '需求状态'
                },
                {
                    field: 'demandType',
                    title: '需求类型'
                },
                {
                    field: 'materialCode',
                    title: '奇瑞零件号'
                },
                {
                    field: 'materialDescription',
                    title: '零件名称'
                },
                {
                    field: 'summarySign',
                    title: '集货标识'
                },
                {
                    field: 'dateRequired',
                    title: '需求日期'
                },
                {
                    field: 'quantityDemand',
                    title: '需求数量'
                },
                {
                    field: 'confirmTime',
                    title: '需求确认时间'
                },
                {
                    field: 'creatQuantity',
                    title: '已建单数量'
                },
                {
                    field: 'quantityDelivery',
                    title: '已发货数量'
                },
                {
                    field: 'quantityReceive',
                    title: '已收货数量'
                },
                {
                    field: 'quantityInTransit',
                    title: '在途数量'
                },
                {
                    field: 'onTimePercentage',
                    title: '按时到货比'
                },
                {
                    field: 'summaryCreatQuantity',
                    title: '集货件已建单数量'
                },
                {
                    field: 'summaryQuantityDelivery',
                    title: '集货件已发货数量'
                },
                {
                    field: 'summaryQuantityReceive',
                    title: '集货件已收货数量'
                },
                {
                    field: 'summaryQuantityInTransit',
                    title: '集货件已在途数量'
                },
                {
                    field: 'createByUser',
                    title: '创建人'
                },
                {
                    field: 'updateByUser',
                    title: '修改人'
                },
                {
                    field: 'isDelete',
                    title: '是否删除'
                },
                {
                    field: 'version',
                    title: '版本号'
                },

                {
                    title: '操作',
                    align: 'center',
                                       // formatter: function(value, row, index) {
                    //     var actions = [];
                    //     actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                    //     actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                    //     return actions.join('');
                    // }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>